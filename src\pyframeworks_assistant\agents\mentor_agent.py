"""
Mentor Agent - Provides guidance, motivation, and learning strategy advice.
"""

from typing import Optional
from langchain_core.messages import HumanMessage
from langchain_core.language_models.chat_models import BaseChatModel

from .base_agent import BaseAgent
from ..core.constellation import ConstellationState, AgentRole


class MentorAgent(BaseAgent):
    """Provides guidance, motivation, and learning strategy advice."""
    
    def __init__(self, model: Optional[BaseChatModel] = None):
        super().__init__(AgentRole.MENTOR, model, "mentoring")
    
    def get_system_prompt(self, state: ConstellationState) -> str:
        return f"""You are a learning mentor specializing in {state.framework.value} education.

Current Context:
- Framework: {state.framework.value}
- Learning Progress: {state.progress_percentage:.1f}%
- Engagement Level: {state.engagement_level:.1f}
- Session Duration: Ongoing

Your role is to:
1. Provide learning strategy guidance
2. Offer motivation and encouragement
3. Help overcome learning obstacles
4. Suggest personalized learning paths
5. Connect concepts to real-world applications
6. Foster a growth mindset

Be supportive, wise, and encouraging. Help learners develop confidence and effective learning strategies.

Respond as the mentor agent with guidance and encouragement."""
    
    def should_activate(self, state: ConstellationState) -> bool:
        # Activate for motivation, strategy, or when engagement is low
        if state.engagement_level < 0.4:
            return True
        
        if not state.messages:
            return False
        
        latest_message = state.messages[-1]
        if isinstance(latest_message, HumanMessage):
            content_lower = latest_message.content.lower()
            mentor_keywords = ["stuck", "difficult", "frustrated", "advice", "strategy", "motivation"]
            return any(keyword in content_lower for keyword in mentor_keywords)
        
        return False 