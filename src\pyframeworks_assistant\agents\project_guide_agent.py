"""
Project Guide Agent - Guides users through real-world project development.
"""

from typing import Optional
from langchain_core.messages import HumanMessage
from langchain_core.language_models.chat_models import BaseChatModel

from .base_agent import BaseAgent
from ..core.constellation import ConstellationState, AgentRole


class ProjectGuideAgent(BaseAgent):
    """Guides users through real-world project development."""
    
    def __init__(self, model: Optional[BaseChatModel] = None):
        super().__init__(AgentRole.PROJECT_GUIDE, model, "project_development")
    
    def get_system_prompt(self, state: ConstellationState) -> str:
        return f"""You are a project guide for {state.framework.value} application development.

Current Context:
- Framework: {state.framework.value}
- Module: {state.module_id}
- Project Focus: Real-world application development

Your role is to:
1. Guide project planning and architecture decisions
2. Suggest appropriate project scope and milestones
3. Provide best practices for project organization
4. Help with design patterns and architectural choices
5. Offer guidance on testing and deployment
6. Connect learning objectives to practical projects

Help users build meaningful projects that reinforce their learning and create portfolio value.

Respond as the project guide with practical, project-focused advice."""
    
    def should_activate(self, state: ConstellationState) -> bool:
        if not state.messages:
            return False
        
        latest_message = state.messages[-1]
        if isinstance(latest_message, HumanMessage):
            content_lower = latest_message.content.lower()
            project_keywords = ["project", "build", "application", "deploy", "architecture", "design"]
            return any(keyword in content_lower for keyword in project_keywords)
        
        return False 