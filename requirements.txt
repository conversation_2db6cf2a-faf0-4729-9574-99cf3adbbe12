# Core LangChain ecosystem
langchain>=0.3.0
langgraph>=0.2.45
langchain-openai>=0.2.0
langchain-anthropic>=0.3.0
langchain-google-genai>=2.1.4
langchain-google-community>=2.0.7
langchain-community>=0.3.0
langsmith>=0.2.0
langchain-tavily>=0.1.6

# Web frameworks
streamlit>=1.39.0
fastapi>=0.115.0
uvicorn>=0.32.0

# Database and storage - Updated for 2025
redis>=5.2.0
chromadb>=1.0.12
psycopg2-binary>=2.9.10
sentence-transformers>=3.3.1

# Data validation and configuration
pydantic>=2.10.0
pydantic-settings>=2.6.0
python-dotenv>=1.0.0

# Async and file handling
aiofiles>=24.1.0
websockets>=13.0

# Search functionality - Added Tavily for AI-optimized search
tavily-python>=0.5.0

# CLI and display
rich>=13.9.0
typer>=0.15.0

# Development tools
pytest>=8.3.0
pytest-asyncio>=0.24.0
black>=24.10.0
ruff>=0.8.0
mypy>=1.13.0

# Additional dependencies for complete implementation
numpy>=1.24.0
scikit-learn>=1.3.0
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.17.0 