[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "pyframeworks-assistant"
version = "0.1.0"
description = "A multi-agent system for learning Python AI frameworks through personalized, interactive experiences"
readme = "README.md"
requires-python = ">=3.10"
license = {text = "MIT"}
authors = [
    {name = "GAAPF - Guidance AI Agent Team"}
]
keywords = ["AI", "multi-agent", "education", "langchain", "langgraph", "machine-learning"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "Intended Audience :: Education",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Education",
]

dependencies = [
    "langchain>=0.3.0",
    "langgraph>=0.2.45",
    "langchain-openai>=0.2.0",
    "langchain-anthropic>=0.3.0",
    "langchain-community>=0.3.0",
    "langsmith>=0.2.0",
    "streamlit>=1.39.0",
    "fastapi>=0.115.0",
    "uvicorn>=0.32.0",
    "redis>=5.2.0",
    "chromadb>=0.5.0",
    "psycopg2-binary>=2.9.0",
    "pydantic>=2.10.0",
    "pydantic-settings>=2.6.0",
    "python-dotenv>=1.0.0",
    "aiofiles>=24.1.0",
    "websockets>=13.0",
    "rich>=13.9.0",
    "typer>=0.15.0",
    "pytest>=8.3.0",
    "pytest-asyncio>=0.24.0",
    "black>=24.10.0",
    "ruff>=0.8.0",
    "mypy>=1.13.0"
]

[project.optional-dependencies]
dev = [
    "pytest>=8.3.0",
    "pytest-asyncio>=0.24.0",
    "pytest-cov>=6.0.0",
    "black>=24.10.0",
    "ruff>=0.8.0",
    "mypy>=1.13.0",
    "pre-commit>=4.0.0"
]

all = [
    "jupyter>=1.1.0",
    "matplotlib>=3.9.0",
    "seaborn>=0.13.0",
    "plotly>=5.24.0"
]

[project.urls]
Homepage = "https://github.com/your-org/pyframeworks-assistant"
Documentation = "https://pyframeworks-assistant.readthedocs.io"
Repository = "https://github.com/your-org/pyframeworks-assistant"
"Bug Tracker" = "https://github.com/your-org/pyframeworks-assistant/issues"

[project.scripts]
gaapf = "pyframeworks_assistant.cli:main"

[tool.setuptools.packages.find]
where = ["src"]
include = ["pyframeworks_assistant*"]

[tool.black]
line-length = 88
target-version = ['py310']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.ruff]
target-version = "py310"
line-length = 88
select = ["E", "F", "I", "N", "W", "UP"]
ignore = ["E501", "F401"]

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers"
testpaths = ["tests"]
pythonpath = ["src"]
asyncio_mode = "auto" 