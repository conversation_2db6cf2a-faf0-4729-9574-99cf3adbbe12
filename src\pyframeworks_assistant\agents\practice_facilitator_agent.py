"""
Practice Facilitator Agent - Facilitates hands-on practice and interactive exercises.
"""

from typing import Optional
from langchain_core.messages import HumanMessage
from langchain_core.language_models.chat_models import BaseChatModel

from .base_agent import BaseAgent
from ..core.constellation import ConstellationState, AgentRole


class PracticeFacilitatorAgent(BaseAgent):
    """Facilitates hands-on practice and interactive exercises."""
    
    def __init__(self, model: Optional[BaseChatModel] = None):
        super().__init__(AgentRole.PRACTICE_FACILITATOR, model, "practice")
    
    def get_system_prompt(self, state: ConstellationState) -> str:
        return f"""You are a practice facilitator for {state.framework.value} learning.

Current Context:
- Framework: {state.framework.value}
- Module: {state.module_id}
- Exercises Completed: {len(state.exercises_completed)}

Your role is to:
1. Design engaging hands-on exercises
2. Create progressive challenges that build skills
3. Provide step-by-step practice guidance
4. Offer immediate feedback on attempts
5. Suggest variations and extensions
6. Encourage experimentation and exploration

Create exercises that are practical, achievable, and directly relevant to the learning objectives. Provide clear instructions and expected outcomes.

Respond as the practice facilitator with actionable, engaging exercises."""
    
    def should_activate(self, state: ConstellationState) -> bool:
        if not state.messages:
            return False
        
        latest_message = state.messages[-1]
        if isinstance(latest_message, HumanMessage):
            content_lower = latest_message.content.lower()
            practice_keywords = ["practice", "exercise", "hands-on", "try", "build", "create", "challenge"]
            return any(keyword in content_lower for keyword in practice_keywords)
        
        return False 