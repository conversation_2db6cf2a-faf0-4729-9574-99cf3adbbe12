"""
Knowledge memory management for tracking concept understanding and skill development.
Handles knowledge graphs, concept relationships, and skill progression tracking.
"""

from typing import Dict, List, Optional, Any, Set
from datetime import datetime
from dataclasses import dataclass
import logging

from pydantic import BaseModel, Field

from ..config.framework_configs import SupportedFrameworks

logger = logging.getLogger(__name__)


class ConceptNode(BaseModel):
    """Individual concept in the knowledge graph."""
    concept_id: str = Field(description="Unique concept identifier")
    name: str = Field(description="Concept name")
    framework: str = Field(description="Associated framework")
    description: str = Field(description="Concept description")
    difficulty_level: float = Field(description="Difficulty level (0-1)")
    prerequisites: List[str] = Field(default_factory=list, description="Required prerequisite concepts")
    related_concepts: List[str] = Field(default_factory=list, description="Related concept IDs")
    learning_resources: List[str] = Field(default_factory=list, description="Learning resource links")
    
    # Learning tracking
    times_studied: int = Field(default=0, description="Number of times studied")
    mastery_level: float = Field(default=0.0, description="Current mastery level (0-1)")
    last_studied: Optional[datetime] = Field(default=None, description="Last study timestamp")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class SkillAssessment(BaseModel):
    """Assessment of user skill in a specific area."""
    skill_id: str = Field(description="Skill identifier")
    framework: str = Field(description="Framework context")
    category: str = Field(description="Skill category")
    current_level: float = Field(description="Current skill level (0-1)")
    confidence_score: float = Field(description="Confidence in assessment (0-1)")
    evidence: List[str] = Field(default_factory=list, description="Evidence supporting assessment")
    last_assessed: datetime = Field(default_factory=datetime.now, description="Last assessment time")
    progression_history: List[Dict[str, Any]] = Field(default_factory=list, description="Historical progression")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class KnowledgeBase:
    """Central knowledge base for tracking concepts and relationships."""
    
    def __init__(self):
        """Initialize knowledge base."""
        self.concepts: Dict[str, ConceptNode] = {}
        self.concept_graph: Dict[str, Set[str]] = {}
        self._initialize_framework_concepts()
    
    def _initialize_framework_concepts(self) -> None:
        """Initialize concepts for supported frameworks."""
        
        # LangChain concepts
        langchain_concepts = [
            {
                "concept_id": "lc_chat_models",
                "name": "Chat Models",
                "framework": "langchain",
                "description": "Language models for conversational AI",
                "difficulty_level": 0.2,
                "prerequisites": [],
                "related_concepts": ["lc_prompts", "lc_chains"]
            },
            {
                "concept_id": "lc_prompts",
                "name": "Prompt Templates",
                "framework": "langchain",
                "description": "Templates for structuring prompts",
                "difficulty_level": 0.3,
                "prerequisites": ["lc_chat_models"],
                "related_concepts": ["lc_chains", "lc_memory"]
            },
            {
                "concept_id": "lc_chains",
                "name": "Chains",
                "framework": "langchain",
                "description": "Combining multiple components into workflows",
                "difficulty_level": 0.5,
                "prerequisites": ["lc_chat_models", "lc_prompts"],
                "related_concepts": ["lc_agents", "lc_memory"]
            },
            {
                "concept_id": "lc_agents",
                "name": "Agents",
                "framework": "langchain",
                "description": "Autonomous agents with tool access",
                "difficulty_level": 0.7,
                "prerequisites": ["lc_chains", "lc_tools"],
                "related_concepts": ["lc_memory", "lc_tools"]
            },
            {
                "concept_id": "lc_memory",
                "name": "Memory",
                "framework": "langchain",
                "description": "Conversation and context memory",
                "difficulty_level": 0.6,
                "prerequisites": ["lc_chains"],
                "related_concepts": ["lc_agents", "lc_prompts"]
            },
            {
                "concept_id": "lc_tools",
                "name": "Tools",
                "framework": "langchain",
                "description": "External tools and function calling",
                "difficulty_level": 0.6,
                "prerequisites": ["lc_chains"],
                "related_concepts": ["lc_agents"]
            }
        ]
        
        # LangGraph concepts
        langgraph_concepts = [
            {
                "concept_id": "lg_state_graphs",
                "name": "State Graphs",
                "framework": "langgraph",
                "description": "Stateful workflow graphs",
                "difficulty_level": 0.4,
                "prerequisites": ["lc_chains"],
                "related_concepts": ["lg_agents", "lg_checkpoints"]
            },
            {
                "concept_id": "lg_agents",
                "name": "Graph Agents",
                "framework": "langgraph",
                "description": "Multi-agent systems with graphs",
                "difficulty_level": 0.8,
                "prerequisites": ["lg_state_graphs", "lc_agents"],
                "related_concepts": ["lg_checkpoints", "lg_human_feedback"]
            },
            {
                "concept_id": "lg_checkpoints",
                "name": "Checkpointing",
                "framework": "langgraph",
                "description": "Saving and resuming graph state",
                "difficulty_level": 0.6,
                "prerequisites": ["lg_state_graphs"],
                "related_concepts": ["lg_agents"]
            },
            {
                "concept_id": "lg_human_feedback",
                "name": "Human-in-the-Loop",
                "framework": "langgraph",
                "description": "Human feedback and interrupts",
                "difficulty_level": 0.7,
                "prerequisites": ["lg_agents"],
                "related_concepts": ["lg_checkpoints"]
            }
        ]
        
        # FastAPI concepts
        fastapi_concepts = [
            {
                "concept_id": "fa_routing",
                "name": "API Routing",
                "framework": "fastapi",
                "description": "Defining API endpoints and routes",
                "difficulty_level": 0.3,
                "prerequisites": [],
                "related_concepts": ["fa_validation", "fa_dependencies"]
            },
            {
                "concept_id": "fa_validation",
                "name": "Data Validation",
                "framework": "fastapi",
                "description": "Automatic request/response validation",
                "difficulty_level": 0.4,
                "prerequisites": ["fa_routing"],
                "related_concepts": ["fa_dependencies", "fa_error_handling"]
            },
            {
                "concept_id": "fa_dependencies",
                "name": "Dependency Injection",
                "framework": "fastapi",
                "description": "Dependency injection system",
                "difficulty_level": 0.6,
                "prerequisites": ["fa_routing"],
                "related_concepts": ["fa_security", "fa_testing"]
            },
            {
                "concept_id": "fa_security",
                "name": "Security",
                "framework": "fastapi",
                "description": "Authentication and authorization",
                "difficulty_level": 0.7,
                "prerequisites": ["fa_dependencies"],
                "related_concepts": ["fa_middleware"]
            }
        ]
        
        # Add all concepts
        all_concepts = langchain_concepts + langgraph_concepts + fastapi_concepts
        
        for concept_data in all_concepts:
            concept = ConceptNode(**concept_data)
            self.add_concept(concept)
    
    def add_concept(self, concept: ConceptNode) -> None:
        """Add a concept to the knowledge base."""
        self.concepts[concept.concept_id] = concept
        
        # Update concept graph
        if concept.concept_id not in self.concept_graph:
            self.concept_graph[concept.concept_id] = set()
        
        # Add bidirectional relationships
        for related_id in concept.related_concepts:
            self.concept_graph[concept.concept_id].add(related_id)
            if related_id not in self.concept_graph:
                self.concept_graph[related_id] = set()
            self.concept_graph[related_id].add(concept.concept_id)
    
    def get_concept(self, concept_id: str) -> Optional[ConceptNode]:
        """Get a concept by ID."""
        return self.concepts.get(concept_id)
    
    def get_framework_concepts(self, framework: str) -> List[ConceptNode]:
        """Get all concepts for a framework."""
        return [
            concept for concept in self.concepts.values()
            if concept.framework == framework
        ]
    
    def get_prerequisites(self, concept_id: str) -> List[ConceptNode]:
        """Get prerequisite concepts for a given concept."""
        concept = self.concepts.get(concept_id)
        if not concept:
            return []
        
        return [
            self.concepts[prereq_id] for prereq_id in concept.prerequisites
            if prereq_id in self.concepts
        ]
    
    def get_learning_path(self, target_concept_id: str) -> List[ConceptNode]:
        """Get an optimal learning path to a target concept."""
        if target_concept_id not in self.concepts:
            return []
        
        # Simple topological sort for learning path
        visited = set()
        path = []
        
        def visit(concept_id: str):
            if concept_id in visited:
                return
            
            visited.add(concept_id)
            concept = self.concepts[concept_id]
            
            # Visit prerequisites first
            for prereq_id in concept.prerequisites:
                if prereq_id in self.concepts:
                    visit(prereq_id)
            
            path.append(concept)
        
        visit(target_concept_id)
        return path
    
    def update_concept_mastery(
        self, 
        concept_id: str, 
        mastery_delta: float,
        evidence: str = ""
    ) -> None:
        """Update mastery level for a concept."""
        if concept_id not in self.concepts:
            return
        
        concept = self.concepts[concept_id]
        concept.times_studied += 1
        concept.last_studied = datetime.now()
        
        # Update mastery with decay and growth
        current_mastery = concept.mastery_level
        new_mastery = min(1.0, current_mastery + mastery_delta)
        
        # Apply forgetting curve if not studied recently
        if concept.last_studied:
            days_since = (datetime.now() - concept.last_studied).days
            if days_since > 7:
                decay_factor = 0.95 ** (days_since - 7)
                new_mastery *= decay_factor
        
        concept.mastery_level = max(0.0, new_mastery)
    
    def get_recommended_concepts(self, user_masteries: Dict[str, float]) -> List[ConceptNode]:
        """Get recommended concepts to study next."""
        recommendations = []
        
        for concept in self.concepts.values():
            # Check if prerequisites are met
            prereq_mastery = []
            for prereq_id in concept.prerequisites:
                if prereq_id in user_masteries:
                    prereq_mastery.append(user_masteries[prereq_id])
                else:
                    prereq_mastery.append(0.0)
            
            # Recommend if prerequisites are well understood but concept isn't mastered
            avg_prereq_mastery = sum(prereq_mastery) / len(prereq_mastery) if prereq_mastery else 1.0
            current_mastery = user_masteries.get(concept.concept_id, 0.0)
            
            if avg_prereq_mastery >= 0.7 and current_mastery < 0.8:
                recommendations.append(concept)
        
        # Sort by difficulty and current mastery
        recommendations.sort(key=lambda c: (c.difficulty_level, -user_masteries.get(c.concept_id, 0.0)))
        
        return recommendations[:5]  # Return top 5 recommendations


class ConceptTracker:
    """Tracks concept understanding for individual users."""
    
    def __init__(self, user_id: str, knowledge_base: KnowledgeBase):
        """Initialize concept tracker."""
        self.user_id = user_id
        self.knowledge_base = knowledge_base
        self.concept_masteries: Dict[str, float] = {}
        self.study_history: List[Dict[str, Any]] = []
    
    def record_concept_study(
        self, 
        concept_id: str, 
        performance_score: float,
        study_time: int,
        activity_type: str = "general"
    ) -> None:
        """Record study session for a concept."""
        # Calculate mastery increase based on performance and time
        base_increase = performance_score * 0.1
        time_bonus = min(study_time / 30.0, 1.0) * 0.05  # Max 5% bonus for 30+ minutes
        mastery_increase = base_increase + time_bonus
        
        # Update concept in knowledge base
        self.knowledge_base.update_concept_mastery(
            concept_id, 
            mastery_increase,
            f"{activity_type} study session"
        )
        
        # Update local tracking
        current_mastery = self.concept_masteries.get(concept_id, 0.0)
        self.concept_masteries[concept_id] = min(1.0, current_mastery + mastery_increase)
        
        # Record history
        self.study_history.append({
            "timestamp": datetime.now().isoformat(),
            "concept_id": concept_id,
            "activity_type": activity_type,
            "performance_score": performance_score,
            "study_time": study_time,
            "mastery_increase": mastery_increase
        })
    
    def get_concept_mastery(self, concept_id: str) -> float:
        """Get current mastery level for a concept."""
        return self.concept_masteries.get(concept_id, 0.0)
    
    def get_framework_progress(self, framework: str) -> Dict[str, Any]:
        """Get progress summary for a framework."""
        framework_concepts = self.knowledge_base.get_framework_concepts(framework)
        
        if not framework_concepts:
            return {
                "framework": framework,
                "total_concepts": 0,
                "mastered_concepts": 0,
                "average_mastery": 0.0,
                "progress_percentage": 0.0
            }
        
        total_concepts = len(framework_concepts)
        mastered_concepts = sum(
            1 for concept in framework_concepts
            if self.get_concept_mastery(concept.concept_id) >= 0.8
        )
        
        total_mastery = sum(
            self.get_concept_mastery(concept.concept_id)
            for concept in framework_concepts
        )
        average_mastery = total_mastery / total_concepts if total_concepts > 0 else 0.0
        
        return {
            "framework": framework,
            "total_concepts": total_concepts,
            "mastered_concepts": mastered_concepts,
            "average_mastery": round(average_mastery, 3),
            "progress_percentage": round((average_mastery * 100), 1),
            "concept_details": [
                {
                    "concept_id": concept.concept_id,
                    "name": concept.name,
                    "mastery": round(self.get_concept_mastery(concept.concept_id), 3),
                    "difficulty": concept.difficulty_level
                }
                for concept in framework_concepts
            ]
        }
    
    def get_recommended_concepts(self) -> List[ConceptNode]:
        """Get concepts recommended for this user."""
        return self.knowledge_base.get_recommended_concepts(self.concept_masteries)
    
    def get_knowledge_gaps(self) -> List[Dict[str, Any]]:
        """Identify knowledge gaps and weaknesses."""
        gaps = []
        
        for concept_id, mastery in self.concept_masteries.items():
            if mastery < 0.5:  # Below average understanding
                concept = self.knowledge_base.get_concept(concept_id)
                if concept:
                    gaps.append({
                        "concept_id": concept_id,
                        "name": concept.name,
                        "framework": concept.framework,
                        "current_mastery": mastery,
                        "difficulty": concept.difficulty_level,
                        "importance": "high" if mastery < 0.3 else "medium"
                    })
        
        # Sort by importance and mastery level
        gaps.sort(key=lambda x: (x["importance"] == "high", -x["current_mastery"]))
        
        return gaps


class SkillTracker:
    """Tracks overall skill development across frameworks and categories."""
    
    def __init__(self, user_id: str):
        """Initialize skill tracker."""
        self.user_id = user_id
        self.skill_assessments: Dict[str, SkillAssessment] = {}
        self._initialize_default_skills()
    
    def _initialize_default_skills(self) -> None:
        """Initialize default skill categories."""
        default_skills = [
            {"skill_id": "lc_fundamentals", "framework": "langchain", "category": "fundamentals"},
            {"skill_id": "lc_advanced", "framework": "langchain", "category": "advanced"},
            {"skill_id": "lg_fundamentals", "framework": "langgraph", "category": "fundamentals"},
            {"skill_id": "lg_advanced", "framework": "langgraph", "category": "advanced"},
            {"skill_id": "fa_fundamentals", "framework": "fastapi", "category": "fundamentals"},
            {"skill_id": "fa_advanced", "framework": "fastapi", "category": "advanced"},
            {"skill_id": "general_ai", "framework": "general", "category": "ai_concepts"},
            {"skill_id": "general_programming", "framework": "general", "category": "programming"}
        ]
        
        for skill_data in default_skills:
            assessment = SkillAssessment(
                skill_id=skill_data["skill_id"],
                framework=skill_data["framework"],
                category=skill_data["category"],
                current_level=0.0,
                confidence_score=0.5
            )
            self.skill_assessments[skill_data["skill_id"]] = assessment
    
    def update_skill_assessment(
        self, 
        skill_id: str, 
        performance_data: Dict[str, Any]
    ) -> None:
        """Update skill assessment based on performance data."""
        if skill_id not in self.skill_assessments:
            return
        
        assessment = self.skill_assessments[skill_id]
        
        # Calculate new skill level based on performance
        quiz_scores = performance_data.get("quiz_scores", [])
        exercise_completions = performance_data.get("exercise_completions", 0)
        study_time = performance_data.get("study_time", 0)
        
        # Performance factors
        performance_factors = []
        
        if quiz_scores:
            avg_quiz = sum(quiz_scores) / len(quiz_scores)
            performance_factors.append(avg_quiz * 0.4)
        
        if exercise_completions > 0:
            exercise_factor = min(exercise_completions / 5.0, 1.0) * 0.3
            performance_factors.append(exercise_factor)
        
        if study_time > 0:
            time_factor = min(study_time / 120.0, 1.0) * 0.3
            performance_factors.append(time_factor)
        
        # Calculate new level
        if performance_factors:
            performance_score = sum(performance_factors)
            
            # Update with exponential moving average
            alpha = 0.3  # Learning rate
            new_level = alpha * performance_score + (1 - alpha) * assessment.current_level
            assessment.current_level = min(1.0, new_level)
            
            # Update confidence based on amount of evidence
            evidence_count = len(assessment.evidence) + 1
            assessment.confidence_score = min(0.95, 0.5 + (evidence_count * 0.1))
            
            # Add evidence
            assessment.evidence.append(f"Performance update: {performance_score:.2f}")
            
            # Record progression
            assessment.progression_history.append({
                "timestamp": datetime.now().isoformat(),
                "level": assessment.current_level,
                "evidence": performance_data
            })
            
            assessment.last_assessed = datetime.now()
    
    def get_skill_level(self, skill_id: str) -> float:
        """Get current skill level."""
        assessment = self.skill_assessments.get(skill_id)
        return assessment.current_level if assessment else 0.0
    
    def get_framework_skills(self, framework: str) -> Dict[str, float]:
        """Get all skills for a framework."""
        return {
            skill_id: assessment.current_level
            for skill_id, assessment in self.skill_assessments.items()
            if assessment.framework == framework
        }
    
    def get_overall_skill_summary(self) -> Dict[str, Any]:
        """Get comprehensive skill summary."""
        framework_skills = {}
        total_assessments = len(self.skill_assessments)
        
        if total_assessments == 0:
            return {"overall_level": 0.0, "frameworks": {}, "confidence": 0.0}
        
        # Group by framework
        for assessment in self.skill_assessments.values():
            framework = assessment.framework
            if framework not in framework_skills:
                framework_skills[framework] = []
            
            framework_skills[framework].append({
                "category": assessment.category,
                "level": assessment.current_level,
                "confidence": assessment.confidence_score
            })
        
        # Calculate averages
        framework_averages = {}
        overall_levels = []
        overall_confidences = []
        
        for framework, skills in framework_skills.items():
            avg_level = sum(skill["level"] for skill in skills) / len(skills)
            avg_confidence = sum(skill["confidence"] for skill in skills) / len(skills)
            
            framework_averages[framework] = {
                "average_level": round(avg_level, 3),
                "confidence": round(avg_confidence, 3),
                "skills": skills
            }
            
            overall_levels.append(avg_level)
            overall_confidences.append(avg_confidence)
        
        return {
            "overall_level": round(sum(overall_levels) / len(overall_levels), 3),
            "overall_confidence": round(sum(overall_confidences) / len(overall_confidences), 3),
            "frameworks": framework_averages,
            "total_assessments": total_assessments
        }


class KnowledgeMemory:
    """
    Wrapper for knowledge base functionality.
    Provides a unified interface for knowledge management.
    """
    
    def __init__(self, user_id: str):
        """Initialize knowledge memory for a user."""
        self.user_id = user_id
        self.knowledge_base = KnowledgeBase()
        self.concept_tracker = ConceptTracker(user_id, self.knowledge_base)
        self.skill_tracker = SkillTracker(user_id)
    
    def get_concept_mastery(self, concept_id: str) -> float:
        """Get mastery level for a concept."""
        return self.concept_tracker.get_concept_mastery(concept_id)
    
    def update_concept_progress(self, concept_id: str, performance_score: float) -> None:
        """Update progress for a concept."""
        self.concept_tracker.record_concept_study(concept_id, performance_score, 30)  # Default 30 min
    
    def get_framework_progress(self, framework: str) -> Dict[str, Any]:
        """Get progress for a framework."""
        return self.concept_tracker.get_framework_progress(framework)


class ConceptMemory:
    """
    Memory for individual concepts.
    """
    
    def __init__(self, concept_id: str):
        """Initialize concept memory."""
        self.concept_id = concept_id
        self.study_sessions: List[Dict[str, Any]] = []
        self.mastery_level = 0.0
        self.last_studied: Optional[datetime] = None
    
    def add_study_session(self, performance: float, duration: int) -> None:
        """Add a study session."""
        session = {
            "timestamp": datetime.now(),
            "performance": performance,
            "duration": duration
        }
        self.study_sessions.append(session)
        self.last_studied = datetime.now()
        
        # Update mastery level
        self.mastery_level = min(1.0, self.mastery_level + performance * 0.1)


class LearningPathMemory:
    """
    Memory for learning paths.
    """
    
    def __init__(self, path_id: str):
        """Initialize learning path memory."""
        self.path_id = path_id
        self.concepts_covered: List[str] = []
        self.current_position = 0
        self.completion_percentage = 0.0
    
    def mark_concept_completed(self, concept_id: str) -> None:
        """Mark a concept as completed."""
        if concept_id not in self.concepts_covered:
            self.concepts_covered.append(concept_id)
        self.current_position += 1


class KnowledgeMemoryManager:
    """
    Manager for knowledge memory across the system.
    Provides the interface needed by the Learning Hub.
    """
    
    def __init__(self):
        """Initialize the knowledge memory manager."""
        self.user_knowledge: Dict[str, KnowledgeMemory] = {}
        self.global_knowledge_base = KnowledgeBase()
        logger.info("KnowledgeMemoryManager initialized")
    
    async def initialize(self) -> None:
        """Initialize the knowledge memory manager asynchronously."""
        try:
            # Any async initialization can go here
            logger.info("KnowledgeMemoryManager async initialization complete")
        except Exception as e:
            logger.error(f"Failed to initialize KnowledgeMemoryManager: {e}")
            raise
    
    def get_user_knowledge(self, user_id: str) -> KnowledgeMemory:
        """Get or create knowledge memory for a user."""
        if user_id not in self.user_knowledge:
            self.user_knowledge[user_id] = KnowledgeMemory(user_id)
        return self.user_knowledge[user_id]
    
    def get_concept_mastery(self, user_id: str, concept_id: str) -> float:
        """Get concept mastery for a user."""
        knowledge = self.get_user_knowledge(user_id)
        return knowledge.get_concept_mastery(concept_id)
    
    def update_concept_progress(self, user_id: str, concept_id: str, performance: float) -> None:
        """Update concept progress for a user."""
        knowledge = self.get_user_knowledge(user_id)
        knowledge.update_concept_progress(concept_id, performance)


def get_knowledge_memory(user_id: str) -> KnowledgeMemory:
    """Get knowledge memory for a user."""
    # In a real implementation, this would be managed by the KnowledgeMemoryManager
    return KnowledgeMemory(user_id)


# Global instances
knowledge_base = KnowledgeBase()


def get_concept_tracker(user_id: str) -> ConceptTracker:
    """Get or create concept tracker for user."""
    return ConceptTracker(user_id, knowledge_base)


def get_skill_tracker(user_id: str) -> SkillTracker:
    """Get or create skill tracker for user."""
    return SkillTracker(user_id) 