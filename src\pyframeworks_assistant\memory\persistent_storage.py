"""
Persistent Storage System for Memory and Vector Data
Implements ChromaDB, PostgreSQL, and Redis storage with proper data flow
"""

import asyncio
import json
import sqlite3
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from pathlib import Path
import hashlib
import pickle
from loguru import logger
import os


try:
    import chromadb
    from chromadb.config import Settings as ChromaSettings
    CHROMADB_AVAILABLE = True
except ImportError:
    CHROMADB_AVAILABLE = False
    logger.warning("ChromaDB not available - vector storage will be disabled")

try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    logger.warning("Redis not available - using memory cache fallback")

try:
    import psycopg2
    from psycopg2.extras import RealDictCursor
    POSTGRES_AVAILABLE = True
except ImportError:
    POSTGRES_AVAILABLE = False
    logger.warning("PostgreSQL not available - using SQLite")

try:
    import numpy as np
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False
    logger.warning("SentenceTransformers not available - using basic embeddings")

from ..config.settings import settings

logger = logging.getLogger(__name__)


@dataclass
class ConversationRecord:
    """Record of a conversation for storage."""
    session_id: str
    user_id: str
    timestamp: datetime
    user_message: str
    ai_response: str
    framework: str
    topic: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class LearningProgressRecord:
    """Learning progress record for storage."""
    user_id: str
    framework: str
    current_phase: str
    completed_topics: List[str]
    mastered_concepts: List[str]
    engagement_level: float
    study_time_minutes: int
    last_activity: datetime
    metadata: Optional[Dict[str, Any]] = None


class DatabaseManager:
    """Manages SQLite/PostgreSQL database operations."""
    
    def __init__(self):
        self.db_path = Path("./data/learning_assistant.db")
        self.postgres_url = settings.postgres_url
        self.use_postgres = self._should_use_postgres()
        self._ensure_db_directory()
        self._initialize_database()
    
    def _should_use_postgres(self) -> bool:
        """Check if PostgreSQL should be used instead of SQLite."""
        if not POSTGRES_AVAILABLE:
            logger.info("PostgreSQL client not installed, using SQLite")
            return False
        
        try:
            # Try to connect to PostgreSQL
            conn = psycopg2.connect(self.postgres_url)
            conn.close()
            return True
        except Exception:
            logger.info("PostgreSQL not available, using SQLite")
            return False
    
    def _ensure_db_directory(self):
        """Ensure database directory exists."""
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
    
    def _initialize_database(self):
        """Initialize database tables."""
        if self.use_postgres:
            self._init_postgres_tables()
        else:
            self._init_sqlite_tables()
    
    def _init_sqlite_tables(self):
        """Initialize SQLite tables."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Conversations table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS conversations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT NOT NULL,
                    user_id TEXT NOT NULL,
                    timestamp DATETIME NOT NULL,
                    user_message TEXT NOT NULL,
                    ai_response TEXT NOT NULL,
                    framework TEXT NOT NULL,
                    topic TEXT,
                    metadata TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Learning progress table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS learning_progress (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    framework TEXT NOT NULL,
                    current_phase TEXT NOT NULL,
                    completed_topics TEXT NOT NULL,
                    mastered_concepts TEXT NOT NULL,
                    engagement_level REAL NOT NULL,
                    study_time_minutes INTEGER NOT NULL,
                    last_activity DATETIME NOT NULL,
                    metadata TEXT,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(user_id, framework)
                )
            """)
            
            # User profiles table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS user_profiles (
                    user_id TEXT PRIMARY KEY,
                    name TEXT,
                    email TEXT,
                    learning_preferences TEXT,
                    skill_level TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Knowledge embeddings table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS knowledge_embeddings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    content_hash TEXT UNIQUE NOT NULL,
                    content_type TEXT NOT NULL,
                    framework TEXT NOT NULL,
                    topic TEXT,
                    content_text TEXT NOT NULL,
                    embedding_vector TEXT NOT NULL,
                    metadata TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            conn.commit()
    
    def _init_postgres_tables(self):
        """Initialize PostgreSQL tables."""
        with psycopg2.connect(self.postgres_url) as conn:
            cursor = conn.cursor()
            
            # Conversations table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS conversations (
                    id SERIAL PRIMARY KEY,
                    session_id VARCHAR(255) NOT NULL,
                    user_id VARCHAR(255) NOT NULL,
                    timestamp TIMESTAMP NOT NULL,
                    user_message TEXT NOT NULL,
                    ai_response TEXT NOT NULL,
                    framework VARCHAR(100) NOT NULL,
                    topic VARCHAR(255),
                    metadata JSONB,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Learning progress table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS learning_progress (
                    id SERIAL PRIMARY KEY,
                    user_id VARCHAR(255) NOT NULL,
                    framework VARCHAR(100) NOT NULL,
                    current_phase VARCHAR(100) NOT NULL,
                    completed_topics JSONB NOT NULL,
                    mastered_concepts JSONB NOT NULL,
                    engagement_level REAL NOT NULL,
                    study_time_minutes INTEGER NOT NULL,
                    last_activity TIMESTAMP NOT NULL,
                    metadata JSONB,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(user_id, framework)
                )
            """)
            
            # User profiles table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS user_profiles (
                    user_id VARCHAR(255) PRIMARY KEY,
                    name VARCHAR(255),
                    email VARCHAR(255),
                    learning_preferences JSONB,
                    skill_level VARCHAR(50),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Knowledge embeddings table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS knowledge_embeddings (
                    id SERIAL PRIMARY KEY,
                    content_hash VARCHAR(64) UNIQUE NOT NULL,
                    content_type VARCHAR(50) NOT NULL,
                    framework VARCHAR(100) NOT NULL,
                    topic VARCHAR(255),
                    content_text TEXT NOT NULL,
                    embedding_vector TEXT NOT NULL,
                    metadata JSONB,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            conn.commit()
    
    def save_conversation(self, record: ConversationRecord):
        """Save conversation record to database."""
        if self.use_postgres:
            self._save_conversation_postgres(record)
        else:
            self._save_conversation_sqlite(record)
    
    def _save_conversation_sqlite(self, record: ConversationRecord):
        """Save conversation to SQLite."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO conversations 
                (session_id, user_id, timestamp, user_message, ai_response, framework, topic, metadata)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                record.session_id,
                record.user_id,
                record.timestamp,
                record.user_message,
                record.ai_response,
                record.framework,
                record.topic,
                json.dumps(record.metadata) if record.metadata else None
            ))
            conn.commit()
    
    def _save_conversation_postgres(self, record: ConversationRecord):
        """Save conversation to PostgreSQL."""
        with psycopg2.connect(self.postgres_url) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO conversations 
                (session_id, user_id, timestamp, user_message, ai_response, framework, topic, metadata)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                record.session_id,
                record.user_id,
                record.timestamp,
                record.user_message,
                record.ai_response,
                record.framework,
                record.topic,
                record.metadata
            ))
            conn.commit()
    
    def get_conversation_history(self, user_id: str, session_id: str, limit: int = 50) -> List[ConversationRecord]:
        """Get conversation history for a user session."""
        if self.use_postgres:
            return self._get_conversation_history_postgres(user_id, session_id, limit)
        else:
            return self._get_conversation_history_sqlite(user_id, session_id, limit)
    
    def _get_conversation_history_sqlite(self, user_id: str, session_id: str, limit: int) -> List[ConversationRecord]:
        """Get conversation history from SQLite."""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute("""
                SELECT * FROM conversations 
                WHERE user_id = ? AND session_id = ?
                ORDER BY timestamp DESC
                LIMIT ?
            """, (user_id, session_id, limit))
            
            records = []
            for row in cursor.fetchall():
                metadata = json.loads(row['metadata']) if row['metadata'] else None
                records.append(ConversationRecord(
                    session_id=row['session_id'],
                    user_id=row['user_id'],
                    timestamp=datetime.fromisoformat(row['timestamp']),
                    user_message=row['user_message'],
                    ai_response=row['ai_response'],
                    framework=row['framework'],
                    topic=row['topic'],
                    metadata=metadata
                ))
            return records[::-1]  # Reverse to get chronological order
    
    def _get_conversation_history_postgres(self, user_id: str, session_id: str, limit: int) -> List[ConversationRecord]:
        """Get conversation history from PostgreSQL."""
        with psycopg2.connect(self.postgres_url) as conn:
            cursor = conn.cursor(cursor_factory=RealDictCursor)
            cursor.execute("""
                SELECT * FROM conversations 
                WHERE user_id = %s AND session_id = %s
                ORDER BY timestamp DESC
                LIMIT %s
            """, (user_id, session_id, limit))
            
            records = []
            for row in cursor.fetchall():
                records.append(ConversationRecord(
                    session_id=row['session_id'],
                    user_id=row['user_id'],
                    timestamp=row['timestamp'],
                    user_message=row['user_message'],
                    ai_response=row['ai_response'],
                    framework=row['framework'],
                    topic=row['topic'],
                    metadata=row['metadata']
                ))
            return records[::-1]  # Reverse to get chronological order
    
    def save_learning_progress(self, record: LearningProgressRecord):
        """Save or update learning progress."""
        if self.use_postgres:
            self._save_learning_progress_postgres(record)
        else:
            self._save_learning_progress_sqlite(record)
    
    def _save_learning_progress_sqlite(self, record: LearningProgressRecord):
        """Save learning progress to SQLite."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT OR REPLACE INTO learning_progress 
                (user_id, framework, current_phase, completed_topics, mastered_concepts, 
                 engagement_level, study_time_minutes, last_activity, metadata)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                record.user_id,
                record.framework,
                record.current_phase,
                json.dumps(record.completed_topics),
                json.dumps(record.mastered_concepts),
                record.engagement_level,
                record.study_time_minutes,
                record.last_activity,
                json.dumps(record.metadata) if record.metadata else None
            ))
            conn.commit()
    
    def _save_learning_progress_postgres(self, record: LearningProgressRecord):
        """Save learning progress to PostgreSQL."""
        with psycopg2.connect(self.postgres_url) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO learning_progress 
                (user_id, framework, current_phase, completed_topics, mastered_concepts, 
                 engagement_level, study_time_minutes, last_activity, metadata)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (user_id, framework) 
                DO UPDATE SET 
                    current_phase = EXCLUDED.current_phase,
                    completed_topics = EXCLUDED.completed_topics,
                    mastered_concepts = EXCLUDED.mastered_concepts,
                    engagement_level = EXCLUDED.engagement_level,
                    study_time_minutes = EXCLUDED.study_time_minutes,
                    last_activity = EXCLUDED.last_activity,
                    metadata = EXCLUDED.metadata,
                    updated_at = CURRENT_TIMESTAMP
            """, (
                record.user_id,
                record.framework,
                record.current_phase,
                record.completed_topics,
                record.mastered_concepts,
                record.engagement_level,
                record.study_time_minutes,
                record.last_activity,
                record.metadata
            ))
            conn.commit()
    
    def get_learning_progress(self, user_id: str, framework: str) -> Optional[LearningProgressRecord]:
        """Get learning progress for a user and framework."""
        if self.use_postgres:
            return self._get_learning_progress_postgres(user_id, framework)
        else:
            return self._get_learning_progress_sqlite(user_id, framework)
    
    def _get_learning_progress_sqlite(self, user_id: str, framework: str) -> Optional[LearningProgressRecord]:
        """Get learning progress from SQLite."""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute("""
                SELECT * FROM learning_progress 
                WHERE user_id = ? AND framework = ?
            """, (user_id, framework))
            
            row = cursor.fetchone()
            if row:
                metadata = json.loads(row['metadata']) if row['metadata'] else None
                return LearningProgressRecord(
                    user_id=row['user_id'],
                    framework=row['framework'],
                    current_phase=row['current_phase'],
                    completed_topics=json.loads(row['completed_topics']),
                    mastered_concepts=json.loads(row['mastered_concepts']),
                    engagement_level=row['engagement_level'],
                    study_time_minutes=row['study_time_minutes'],
                    last_activity=datetime.fromisoformat(row['last_activity']),
                    metadata=metadata
                )
        return None
    
    def _get_learning_progress_postgres(self, user_id: str, framework: str) -> Optional[LearningProgressRecord]:
        """Get learning progress from PostgreSQL."""
        with psycopg2.connect(self.postgres_url) as conn:
            cursor = conn.cursor(cursor_factory=RealDictCursor)
            cursor.execute("""
                SELECT * FROM learning_progress 
                WHERE user_id = %s AND framework = %s
            """, (user_id, framework))
            
            row = cursor.fetchone()
            if row:
                return LearningProgressRecord(
                    user_id=row['user_id'],
                    framework=row['framework'],
                    current_phase=row['current_phase'],
                    completed_topics=row['completed_topics'],
                    mastered_concepts=row['mastered_concepts'],
                    engagement_level=row['engagement_level'],
                    study_time_minutes=row['study_time_minutes'],
                    last_activity=row['last_activity'],
                    metadata=row['metadata']
                )
        return None


class VectorStoreManager:
    """Manages ChromaDB vector store operations."""
    
    def __init__(self):
        self.persist_directory = Path(settings.chroma_persist_directory)
        self.persist_directory.mkdir(parents=True, exist_ok=True)
        
        self.available = CHROMADB_AVAILABLE and SENTENCE_TRANSFORMERS_AVAILABLE
        
        if not self.available:
            logger.warning("Vector store not available - missing dependencies")
            self.client = None
            self.embedding_model = None
            self.conversation_collection = None
            self.knowledge_collection = None
            self.learning_collection = None
            return
        
        # Initialize ChromaDB client
        self.client = chromadb.PersistentClient(
            path=str(self.persist_directory),
            settings=ChromaSettings(anonymized_telemetry=False)
        )
        
        # Initialize sentence transformer for embeddings
        self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
        
        # Create collections for different types of data
        self.conversation_collection = self._get_or_create_collection("conversations")
        self.knowledge_collection = self._get_or_create_collection("knowledge_base")
        self.learning_collection = self._get_or_create_collection("learning_content")
    
    def _get_or_create_collection(self, name: str):
        """Get or create a ChromaDB collection."""
        if not self.available:
            return None
        try:
            return self.client.get_collection(name)
        except Exception:
            return self.client.create_collection(name)
    
    def add_conversation_to_vector_store(self, record: ConversationRecord):
        """Add conversation to vector store for semantic search."""
        if not self.available:
            return
            
        # Create combined text for embedding
        combined_text = f"{record.user_message} {record.ai_response}"
        
        # Generate embedding
        embedding = self.embedding_model.encode(combined_text).tolist()
        
        # Create unique ID
        doc_id = f"{record.session_id}_{record.timestamp.isoformat()}"
        
        # Add to collection
        self.conversation_collection.add(
            documents=[combined_text],
            embeddings=[embedding],
            metadatas=[{
                "session_id": record.session_id,
                "user_id": record.user_id,
                "timestamp": record.timestamp.isoformat(),
                "framework": record.framework,
                "topic": record.topic or "",
                "user_message": record.user_message,
                "ai_response": record.ai_response
            }],
            ids=[doc_id]
        )
    
    def search_conversations(self, query: str, user_id: str, limit: int = 5) -> List[Dict[str, Any]]:
        """Search conversations using semantic similarity."""
        if not self.available:
            return []
            
        # Generate query embedding
        query_embedding = self.embedding_model.encode(query).tolist()
        
        # Search in vector store
        results = self.conversation_collection.query(
            query_embeddings=[query_embedding],
            n_results=limit,
            where={"user_id": user_id}
        )
        
        # Format results
        conversations = []
        if results['documents'] and results['documents'][0]:
            for i, doc in enumerate(results['documents'][0]):
                metadata = results['metadatas'][0][i]
                conversations.append({
                    "content": doc,
                    "user_message": metadata["user_message"],
                    "ai_response": metadata["ai_response"],
                    "timestamp": metadata["timestamp"],
                    "framework": metadata["framework"],
                    "topic": metadata["topic"],
                    "similarity_score": 1 - results['distances'][0][i]  # Convert distance to similarity
                })
        
        return conversations
    
    def add_knowledge_content(self, content: str, framework: str, topic: str, content_type: str, metadata: Dict[str, Any] = None):
        """Add knowledge content to vector store."""
        if not self.available:
            return
            
        # Generate embedding
        embedding = self.embedding_model.encode(content).tolist()
        
        # Create content hash for deduplication
        content_hash = hashlib.sha256(content.encode()).hexdigest()
        
        # Prepare metadata
        doc_metadata = {
            "framework": framework,
            "topic": topic,
            "content_type": content_type,
            "content_hash": content_hash,
            "created_at": datetime.now().isoformat()
        }
        if metadata:
            doc_metadata.update(metadata)
        
        try:
            # Add to collection
            self.knowledge_collection.add(
                documents=[content],
                embeddings=[embedding],
                metadatas=[doc_metadata],
                ids=[content_hash]
            )
        except Exception as e:
            if "already exists" in str(e):
                logger.info(f"Content already exists in knowledge base: {content_hash[:8]}")
            else:
                logger.error(f"Error adding knowledge content: {e}")
    
    def search_knowledge(self, query: str, framework: str = None, topic: str = None, limit: int = 5) -> List[Dict[str, Any]]:
        """Search knowledge base using semantic similarity."""
        if not self.available:
            return []
            
        # Generate query embedding
        query_embedding = self.embedding_model.encode(query).tolist()
        
        # Build where clause
        where_clause = {}
        if framework:
            where_clause["framework"] = framework
        
        # For multiple filters, we need to use the $and operator
        if topic and framework:
            where_clause = {
                "$and": [
                    {"framework": framework},
                    {"topic": topic}
                ]
            }
        elif framework:
            where_clause = {"framework": framework}
        elif topic:
            where_clause = {"topic": topic}
        else:
            where_clause = None
        
        # Search in knowledge collection
        results = self.knowledge_collection.query(
            query_embeddings=[query_embedding],
            n_results=limit,
            where=where_clause
        )
        
        # Format results
        knowledge_items = []
        if results['documents'] and results['documents'][0]:
            for i, doc in enumerate(results['documents'][0]):
                metadata = results['metadatas'][0][i]
                knowledge_items.append({
                    "content": doc,
                    "framework": metadata["framework"],
                    "topic": metadata["topic"],
                    "content_type": metadata["content_type"],
                    "similarity_score": 1 - results['distances'][0][i],
                    "metadata": metadata
                })
        
        return knowledge_items


class MockRedisCache:
    """In-memory mock implementation of Redis cache for testing environments."""
    
    def __init__(self):
        """Initialize the mock cache."""
        self.cache = {}
        self.available = True
        logger.info("Using MockRedisCache for testing")
    
    def set(self, key: str, value: str, ex: int = None):
        """Set a value in the cache with optional expiry."""
        self.cache[key] = {
            'value': value,
            'expires_at': datetime.now() + timedelta(seconds=ex) if ex else None
        }
    
    def setex(self, key: str, time: int, value: str):
        """Set a value with expiry time."""
        self.set(key, value, ex=time)
    
    def get(self, key: str) -> Optional[str]:
        """Get a value from the cache."""
        if key not in self.cache:
            return None
            
        item = self.cache[key]
        
        # Check if expired
        if item['expires_at'] and datetime.now() > item['expires_at']:
            del self.cache[key]
            return None
            
        return item['value']
    
    def delete(self, key: str):
        """Delete a key from the cache."""
        if key in self.cache:
            del self.cache[key]
    
    def ping(self):
        """Mock ping method."""
        return True


class RedisCache:
    """Redis cache for fast session and temporary data storage."""
    
    def __init__(self):
        # Check if we're in a testing environment
        is_testing = os.environ.get('PYFRAMEWORKS_TESTING', '').lower() == 'true'
        
        if is_testing:
            # Use mock Redis for testing
            self.redis_client = MockRedisCache()
            self.available = True
            return
            
        if not REDIS_AVAILABLE:
            logger.warning("Redis client not available")
            self.redis_client = None
            self.available = False
            return
            
        try:
            self.redis_client = redis.from_url(settings.redis_url)
            # Test connection
            self.redis_client.ping()
            self.available = True
        except Exception as e:
            logger.warning(f"Redis not available: {e}")
            self.redis_client = None
            self.available = False
    
    def set_session_data(self, session_id: str, data: Dict[str, Any], ttl: int = 3600):
        """Set session data with TTL."""
        if not self.available:
            return
        
        try:
            self.redis_client.setex(
                f"session:{session_id}",
                ttl,
                json.dumps(data, default=str)
            )
        except Exception as e:
            logger.error(f"Error setting session data: {e}")
    
    def get_session_data(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get session data."""
        if not self.available:
            return None
        
        try:
            data = self.redis_client.get(f"session:{session_id}")
            if data:
                return json.loads(data)
        except Exception as e:
            logger.error(f"Error getting session data: {e}")
        return None
    
    def cache_user_context(self, user_id: str, context: Dict[str, Any], ttl: int = 1800):
        """Cache user context for faster access."""
        if not self.available:
            return
        
        try:
            self.redis_client.setex(
                f"user_context:{user_id}",
                ttl,
                json.dumps(context, default=str)
            )
        except Exception as e:
            logger.error(f"Error caching user context: {e}")
    
    def get_user_context(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Get cached user context."""
        if not self.available:
            return None
        
        try:
            data = self.redis_client.get(f"user_context:{user_id}")
            if data:
                return json.loads(data)
        except Exception as e:
            logger.error(f"Error getting user context: {e}")
        return None


class PersistentStorageManager:
    """Main storage manager orchestrating all storage systems."""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.vector_manager = VectorStoreManager()
        self.cache = RedisCache()
        
        # Initialize with sample knowledge content
        self._initialize_knowledge_base()
    
    def _initialize_knowledge_base(self):
        """Initialize knowledge base with framework-specific content."""
        if not self.vector_manager.available:
            logger.info("Vector store not available - skipping knowledge base initialization")
            return
            
        # LangChain content - expanded with comprehensive examples
        langchain_content = [
            # Core concepts
            {
                "content": "LangChain is a framework for developing applications powered by language models. It provides tools for chaining together different components to create complex workflows.",
                "framework": "langchain",
                "topic": "introduction",
                "content_type": "concept"
            },
            {
                "content": "Chains in LangChain allow you to combine multiple components (prompts, models, output parsers) into a single, reusable pipeline. The LCEL (LangChain Expression Language) syntax makes this easy.",
                "framework": "langchain",
                "topic": "chains",
                "content_type": "concept"
            },
            {
                "content": "Memory in LangChain allows applications to store and recall information from previous interactions. Types include ConversationBufferMemory, ConversationSummaryMemory, and Vector Store-backed memory.",
                "framework": "langchain",
                "topic": "memory",
                "content_type": "concept"
            },
            
            # LangChain code examples
            {
                "content": """
                # Simple LangChain Chat Example
                from langchain.chat_models import ChatOpenAI
                from langchain.prompts import ChatPromptTemplate
                
                # Initialize the chat model
                chat_model = ChatOpenAI()
                
                # Create a prompt template
                prompt = ChatPromptTemplate.from_template("Tell me a {adjective} joke about {topic}.")
                
                # Create a chain
                chain = prompt | chat_model
                
                # Run the chain
                response = chain.invoke({"adjective": "funny", "topic": "programming"})
                print(response.content)
                """,
                "framework": "langchain",
                "topic": "chat_models",
                "content_type": "code_example"
            },
            
            # RAG implementation
            {
                "content": """
                # LangChain RAG Implementation
                from langchain.vectorstores import Chroma
                from langchain.embeddings import OpenAIEmbeddings
                from langchain.text_splitter import RecursiveCharacterTextSplitter
                from langchain.chains import create_retrieval_chain
                from langchain.chains.combine_documents import create_stuff_documents_chain
                from langchain.chat_models import ChatOpenAI
                from langchain.prompts import ChatPromptTemplate
                
                # 1. Load and prepare documents
                with open("data.txt") as f:
                    data = f.read()
                
                text_splitter = RecursiveCharacterTextSplitter(chunk_size=500, chunk_overlap=50)
                chunks = text_splitter.split_text(data)
                
                # 2. Create vector store
                embeddings = OpenAIEmbeddings()
                vectorstore = Chroma.from_texts(chunks, embeddings)
                retriever = vectorstore.as_retriever()
                
                # 3. Create prompt and LLM
                prompt = ChatPromptTemplate.from_template('''
                Answer the question based only on the following context:
                {context}
                
                Question: {input}
                ''')
                
                llm = ChatOpenAI()
                
                # 4. Create and run the RAG chain
                combine_docs_chain = create_stuff_documents_chain(llm, prompt)
                retrieval_chain = create_retrieval_chain(retriever, combine_docs_chain)
                
                response = retrieval_chain.invoke({"input": "What are the key points in the document?"})
                print(response["answer"])
                """,
                "framework": "langchain",
                "topic": "rag",
                "content_type": "code_example"
            },
            
            # Prompt templates
            {
                "content": """
                # LangChain Prompt Templates
                from langchain.prompts import PromptTemplate, ChatPromptTemplate, SystemMessagePromptTemplate, HumanMessagePromptTemplate
                
                # Simple prompt template
                simple_prompt = PromptTemplate.from_template("Tell me about {topic} in {style} style.")
                formatted_prompt = simple_prompt.format(topic="quantum physics", style="simple")
                
                # Chat prompt template
                system_template = "You are an expert in {subject}."
                human_template = "Explain {concept} to me."
                
                chat_prompt = ChatPromptTemplate.from_messages([
                    SystemMessagePromptTemplate.from_template(system_template),
                    HumanMessagePromptTemplate.from_template(human_template)
                ])
                
                messages = chat_prompt.format_messages(subject="mathematics", concept="calculus")
                """,
                "framework": "langchain",
                "topic": "prompts",
                "content_type": "code_example"
            },
            
            # Agents
            {
                "content": """
                # LangChain Agents
                from langchain.agents import create_openai_functions_agent
                from langchain.chat_models import ChatOpenAI
                from langchain.tools import DuckDuckGoSearchRun
                from langchain.agents import AgentExecutor
                from langchain.prompts import ChatPromptTemplate
                
                # Define tools
                search = DuckDuckGoSearchRun()
                tools = [search]
                
                # Create prompt
                prompt = ChatPromptTemplate.from_messages([
                    ("system", "You are a helpful assistant. Use the tools to answer the user's question."),
                    ("human", "{input}")
                ])
                
                # Create agent
                llm = ChatOpenAI()
                agent = create_openai_functions_agent(llm, tools, prompt)
                agent_executor = AgentExecutor(agent=agent, tools=tools)
                
                # Run agent
                result = agent_executor.invoke({"input": "What's the current weather in New York?"})
                print(result["output"])
                """,
                "framework": "langchain",
                "topic": "agents",
                "content_type": "code_example"
            },
            
            # Memory
            {
                "content": """
                # LangChain Memory
                from langchain.chat_models import ChatOpenAI
                from langchain.chains import ConversationChain
                from langchain.memory import ConversationBufferMemory, ConversationSummaryMemory
                
                # Buffer memory (stores all messages)
                buffer_memory = ConversationBufferMemory()
                
                # Summary memory (stores a summary of the conversation)
                summary_memory = ConversationSummaryMemory(llm=ChatOpenAI())
                
                # Create conversation chain with memory
                llm = ChatOpenAI()
                conversation = ConversationChain(
                    llm=llm,
                    memory=buffer_memory,
                    verbose=True
                )
                
                # Chat with memory
                response1 = conversation.predict(input="Hi, my name is Bob")
                response2 = conversation.predict(input="What's my name?")  # Should remember "Bob"
                """,
                "framework": "langchain",
                "topic": "memory",
                "content_type": "code_example"
            },
            
            # LangChain best practices
            {
                "content": """
                LangChain Best Practices:
                
                1. Use LCEL (LangChain Expression Language) for composing chains with the | operator
                2. Implement proper error handling for LLM calls
                3. Use streaming for better user experience with long responses
                4. Implement retries for API rate limits and transient errors
                5. Cache embeddings to reduce API costs
                6. Use structured output parsers for consistent response formats
                7. Implement proper prompt engineering with clear instructions
                8. Use the RunnableWithMessageHistory for maintaining conversation state
                9. Implement proper logging for debugging and monitoring
                10. Use the LangSmith platform for tracing and debugging
                """,
                "framework": "langchain",
                "topic": "best_practices",
                "content_type": "guide"
            },
            
            # LCEL
            {
                "content": """
                # LangChain Expression Language (LCEL)
                from langchain.chat_models import ChatOpenAI
                from langchain.prompts import ChatPromptTemplate
                from langchain.schema.output_parser import StrOutputParser
                
                # Create components
                prompt = ChatPromptTemplate.from_template("Write a short poem about {topic}.")
                model = ChatOpenAI()
                output_parser = StrOutputParser()
                
                # Compose with LCEL
                chain = prompt | model | output_parser
                
                # Invoke the chain
                result = chain.invoke({"topic": "artificial intelligence"})
                print(result)
                
                # With streaming
                for chunk in chain.stream({"topic": "space exploration"}):
                    print(chunk, end="", flush=True)
                """,
                "framework": "langchain",
                "topic": "lcel",
                "content_type": "code_example"
            }
        ]
        
        for content_item in langchain_content:
            self.vector_manager.add_knowledge_content(**content_item)
            
        # LangGraph content
        langgraph_content = [
            {
                "content": "LangGraph is a library for building stateful, multi-actor applications with LLMs. It extends LangChain with a flexible state machine model.",
                "framework": "langgraph",
                "topic": "introduction",
                "content_type": "concept"
            },
            {
                "content": """
                # Basic LangGraph Example
                from langgraph.graph import StateGraph, END
                from langchain.chat_models import ChatOpenAI
                
                # Define the state
                class State(TypedDict):
                    messages: Annotated[list, operator.add]
                    count: int
                
                # Define nodes
                def increment_count(state):
                    return {"count": state["count"] + 1}
                
                def call_model(state):
                    messages = state["messages"]
                    response = ChatOpenAI().invoke(messages)
                    return {"messages": [response]}
                
                # Create the graph
                builder = StateGraph(State)
                builder.add_node("increment", increment_count)
                builder.add_node("call_model", call_model)
                
                # Add edges
                builder.add_edge("increment", "call_model")
                builder.add_conditional_edges(
                    "call_model",
                    lambda state: "increment" if state["count"] < 3 else END
                )
                
                # Set entry point
                builder.set_entry_point("increment")
                
                # Compile the graph
                graph = builder.compile()
                
                # Run the graph
                result = graph.invoke({
                    "messages": [{"role": "user", "content": "Tell me a joke"}],
                    "count": 0
                })
                """,
                "framework": "langgraph",
                "topic": "basic_example",
                "content_type": "code_example"
            }
        ]
        
        for content_item in langgraph_content:
            self.vector_manager.add_knowledge_content(**content_item)
    
    def save_conversation(self, session_id: str, user_id: str, user_message: str, ai_response: str, framework: str, topic: str = None):
        """Save conversation to both database and vector store."""
        record = ConversationRecord(
            session_id=session_id,
            user_id=user_id,
            timestamp=datetime.now(),
            user_message=user_message,
            ai_response=ai_response,
            framework=framework,
            topic=topic
        )
        
        # Save to database
        self.db_manager.save_conversation(record)
        
        # Save to vector store for semantic search
        self.vector_manager.add_conversation_to_vector_store(record)
    
    def get_conversation_context(self, user_id: str, session_id: str, query: str = None) -> Dict[str, Any]:
        """Get conversation context including history and relevant past conversations."""
        # Get recent conversation history
        history = self.db_manager.get_conversation_history(user_id, session_id, limit=10)
        
        # If query provided, search for relevant past conversations
        relevant_conversations = []
        if query and self.vector_manager.available:
            relevant_conversations = self.vector_manager.search_conversations(query, user_id, limit=3)
        
        return {
            "recent_history": [asdict(conv) for conv in history],
            "relevant_conversations": relevant_conversations,
            "total_conversations": len(history)
        }
    
    def save_learning_progress(self, user_id: str, framework: str, progress_data: Dict[str, Any]):
        """Save learning progress to database."""
        record = LearningProgressRecord(
            user_id=user_id,
            framework=framework,
            current_phase=progress_data.get("current_phase", "foundation"),
            completed_topics=progress_data.get("completed_topics", []),
            mastered_concepts=progress_data.get("mastered_concepts", []),
            engagement_level=progress_data.get("engagement_level", 0.5),
            study_time_minutes=progress_data.get("study_time_minutes", 0),
            last_activity=datetime.now(),
            metadata=progress_data.get("metadata")
        )
        
        self.db_manager.save_learning_progress(record)
        
        # Cache for faster access
        self.cache.cache_user_context(user_id, {
            "learning_progress": asdict(record),
            "last_updated": datetime.now().isoformat()
        })
    
    def get_learning_progress(self, user_id: str, framework: str) -> Optional[Dict[str, Any]]:
        """Get learning progress with caching."""
        # Try cache first
        cached_context = self.cache.get_user_context(user_id)
        if cached_context and "learning_progress" in cached_context:
            cached_progress = cached_context["learning_progress"]
            if cached_progress["framework"] == framework:
                return cached_progress
        
        # Get from database
        record = self.db_manager.get_learning_progress(user_id, framework)
        if record:
            progress_dict = asdict(record)
            # Update cache
            self.cache.cache_user_context(user_id, {
                "learning_progress": progress_dict,
                "last_updated": datetime.now().isoformat()
            })
            return progress_dict
        
        return None
    
    def search_knowledge(self, query: str, framework: str = None, topic: str = None) -> List[Dict[str, Any]]:
        """Search for knowledge content."""
        return self.vector_manager.search_knowledge(query, framework, topic)
    
    async def store_framework_context(self, framework: str, context_data: Dict[str, Any]) -> bool:
        """
        Store framework context in persistent storage.
        
        Args:
            framework: Framework identifier
            context_data: Framework context data
            
        Returns:
            bool: Success status
        """
        try:
            # Import here to avoid circular imports
            from ..core.vector_storage import vector_storage
            
            # Store in vector database if available
            if hasattr(vector_storage, 'store_framework_context'):
                # Convert context_data to a FrameworkContext object if needed
                if not hasattr(context_data, 'framework'):
                    from ..core.framework_initializer import FrameworkContext
                    from ..config.framework_configs import SupportedFrameworks
                    
                    # If it's already a FrameworkContext, use it directly
                    if isinstance(context_data, FrameworkContext):
                        context = context_data
                    else:
                        # Try to convert dict to FrameworkContext
                        try:
                            framework_enum = SupportedFrameworks(framework)
                            context = FrameworkContext(
                                framework=framework_enum,
                                version=context_data.get('version', '1.0.0'),
                                description=context_data.get('description', ''),
                                key_concepts=context_data.get('key_concepts', []),
                                syntax=context_data.get('syntax', None),
                                core_modules=context_data.get('core_modules', []),
                                practice_examples=context_data.get('practice_examples', []),
                                learning_path=context_data.get('learning_path', []),
                                common_use_cases=context_data.get('common_use_cases', []),
                                integration_patterns=context_data.get('integration_patterns', []),
                                troubleshooting_guide=context_data.get('troubleshooting_guide', {}),
                                resources=context_data.get('resources', {}),
                                created_at=datetime.now(),
                                source_urls=context_data.get('source_urls', [])
                            )
                        except Exception as e:
                            logger.error(f"Failed to convert context data to FrameworkContext: {e}")
                            return False
                else:
                    context = context_data
                
                # Store in vector database
                await vector_storage.store_framework_context(context)
                return True
            
            # Also store a simplified version in the cache for quick access
            simplified_context = {
                'framework': framework,
                'version': getattr(context_data, 'version', context_data.get('version', '1.0.0')),
                'description': getattr(context_data, 'description', context_data.get('description', '')),
                'key_concepts': getattr(context_data, 'key_concepts', context_data.get('key_concepts', [])),
                'timestamp': datetime.now().isoformat()
            }
            
            if hasattr(self.cache, 'set'):
                self.cache.set(f"framework_context_{framework}", json.dumps(simplified_context), ex=86400)  # 24 hours
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to store framework context: {e}")
            return False
    
    async def retrieve_framework_context(self, framework: str, query: str = None) -> Optional[Dict[str, Any]]:
        """
        Retrieve framework context from persistent storage.
        
        Args:
            framework: Framework identifier
            query: Optional search query to find specific information
            
        Returns:
            Optional[Dict[str, Any]]: Framework context data if found
        """
        try:
            # Import here to avoid circular imports
            from ..core.vector_storage import vector_storage
            from ..config.framework_configs import SupportedFrameworks
            
            # Try to get from vector database if available
            if hasattr(vector_storage, 'retrieve_framework_context'):
                try:
                    framework_enum = SupportedFrameworks(framework)
                    results = await vector_storage.retrieve_framework_context(framework_enum, query)
                    
                    if results:
                        # Format the results
                        formatted_results = {
                            'framework': framework,
                            'results': results,
                            'timestamp': datetime.now().isoformat()
                        }
                        return formatted_results
                except Exception as e:
                    logger.error(f"Failed to retrieve from vector database: {e}")
            
            # Try to get from cache as fallback
            if hasattr(self.cache, 'get'):
                cached_context = self.cache.get(f"framework_context_{framework}")
                if cached_context:
                    try:
                        return json.loads(cached_context)
                    except:
                        pass
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to retrieve framework context: {e}")
            return None
    
    def get_storage_stats(self) -> Dict[str, Any]:
        """Get storage statistics."""
        stats = {
            "database_type": "PostgreSQL" if self.db_manager.use_postgres else "SQLite",
            "vector_store": "ChromaDB",
            "cache": "Redis" if self.cache.available else "None",
            "storage_directory": str(self.vector_manager.persist_directory)
        }
        
        # Get collection stats
        try:
            stats["conversation_count"] = self.vector_manager.conversation_collection.count()
            stats["knowledge_count"] = self.vector_manager.knowledge_collection.count()
        except Exception as e:
            logger.error(f"Error getting collection stats: {e}")
            stats["conversation_count"] = "Unknown"
            stats["knowledge_count"] = "Unknown"
        
        return stats


# Global storage manager instance
_storage_manager_instance = None

def get_storage_manager() -> PersistentStorageManager:
    """Get the global storage manager instance."""
    global _storage_manager_instance
    
    if _storage_manager_instance is None:
        _storage_manager_instance = PersistentStorageManager()
        
    return _storage_manager_instance 