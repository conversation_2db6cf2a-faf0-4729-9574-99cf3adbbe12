"""
Intelligent Agent Manager - Coordinates agents, manages agent selection, and handles conversation flow.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple, Callable, Set, Union
from datetime import datetime
from uuid import uuid4
import os
import json
from enum import Enum

from pydantic import BaseModel, Field
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage, BaseMessage

from ..config.user_profiles import UserProfile, SkillLevel, LearningSession
from ..config.framework_configs import SupportedFrameworks
from ..agents.base_agent import BaseAgent
from ..agents.code_assistant_agent import CodeAssistantAgent
from ..agents.research_assistant_agent import ResearchAssistantAgent
from ..agents.instructor_agent import InstructorAgent
from ..agents.documentation_expert_agent import DocumentationExpertAgent
from ..agents.practice_facilitator_agent import PracticeFacilitatorAgent
from ..agents.troubleshooter_agent import TroubleshooterAgent
from ..agents.mentor_agent import MentorAgent
from ..agents.assessment_agent import AssessmentAgent
from ..agents.project_guide_agent import ProjectGuideAgent
from ..agents.knowledge_synthesizer_agent import KnowledgeSynthesizerAgent
from ..core.constellation import ConstellationState, ConstellationManager, AgentRole
from ..memory.persistent_storage import PersistentStorageManager
from ..core.adaptive_learning_engine import AdaptiveLearningEngine
from ..core.framework_initializer import DynamicFrameworkInitializer, FrameworkContext
from ..core.onboarding_flow import start_onboarding, get_onboarding_graph
from .intelligent_context import IntelligentContext, UserIntent, ProactiveAction

logger = logging.getLogger(__name__)


class ConversationConfig(BaseModel):
    """Configuration for a conversation session."""
    framework: SupportedFrameworks = Field(description="Target framework for the conversation")
    module_id: str = Field(default="general", description="Current learning module ID")
    user_profile: Optional[UserProfile] = Field(default=None, description="User profile information")
    conversation_history: List[BaseMessage] = Field(default_factory=list, description="Conversation history")


class IntelligentAgentManager:
    """
    Manages agent coordination, conversation flow, and memory persistence.
    Provides a simplified interface for user interaction with the agent constellation.
    """
    
    def __init__(self):
        """Initialize the intelligent agent manager."""
        self.constellation_manager = ConstellationManager()
        self.learning_engine = AdaptiveLearningEngine()
        self.framework_initializer = DynamicFrameworkInitializer()
        self.memory = PersistentStorageManager()
        self.active_sessions: Dict[str, ConversationConfig] = {}
        self.onboarding_sessions: Dict[str, bool] = {}
        
    @staticmethod
    def build_intelligent_context(
        state: ConstellationState, 
        user_profile: Optional[UserProfile], 
        message: str
    ) -> IntelligentContext:
        """
        Build intelligent context from the current state, user profile, and message.
        
        Args:
            state: Current constellation state
            user_profile: User profile information
            message: Current user message
            
        Returns:
            IntelligentContext: Intelligent context for the current interaction
        """
        # Detect user intent based on message content
        intent = UserIntent.GENERAL_QUESTION
        
        # Simple keyword-based intent detection
        message_lower = message.lower()
        if any(kw in message_lower for kw in ["how to", "implement", "code", "function", "class", "write"]):
            intent = UserIntent.CODING
        elif any(kw in message_lower for kw in ["error", "bug", "fix", "issue", "problem", "doesn't work"]):
            intent = UserIntent.TROUBLESHOOTING
        elif any(kw in message_lower for kw in ["learn", "understand", "concept", "explain", "what is"]):
            intent = UserIntent.LEARNING
        elif any(kw in message_lower for kw in ["document", "reference", "api", "specification"]):
            intent = UserIntent.DOCUMENTATION
        elif any(kw in message_lower for kw in ["project", "plan", "design", "architecture"]):
            intent = UserIntent.PROJECT_PLANNING
        elif any(kw in message_lower for kw in ["test", "quiz", "assess", "evaluate"]):
            intent = UserIntent.ASSESSMENT
        elif any(kw in message_lower for kw in ["explore", "discover", "find", "search"]):
            intent = UserIntent.EXPLORATION
        elif any(kw in message_lower for kw in ["clarify", "confused", "don't understand"]):
            intent = UserIntent.CLARIFICATION
        elif any(kw in message_lower for kw in ["practice", "exercise", "try", "hands-on"]):
            intent = UserIntent.PRACTICE
            
        # Calculate topic familiarity based on user profile and conversation history
        topic_familiarity = 0.5
        if user_profile and hasattr(user_profile, 'ai_framework_experience'):
            # Get the framework name
            framework_name = state.framework.value.lower()
            
            # Check if the framework exists in the user's experience
            if hasattr(user_profile.ai_framework_experience, framework_name):
                # Get the skill level for the framework
                skill_level = getattr(user_profile.ai_framework_experience, framework_name)
                
                # Convert skill level to a numeric value (0 to 4)
                skill_values = {
                    "none": 0,
                    "beginner": 1,
                    "intermediate": 2,
                    "advanced": 3,
                    "expert": 4
                }
                
                # Get numeric value for the skill level
                experience_level = skill_values.get(skill_level.value, 0)
                
                # Calculate topic familiarity based on experience level
                topic_familiarity = min(0.8, 0.3 + (experience_level * 0.125))
            
        # Estimate engagement level based on message length, question complexity
        engagement_level = min(1.0, 0.4 + (len(message) / 200))
        
        # Estimate learning velocity based on user profile and session progress
        learning_velocity = 0.5
        if user_profile and hasattr(user_profile, 'learning_pace'):
            if user_profile.learning_pace == "fast":
                learning_velocity = 0.7
            elif user_profile.learning_pace == "slow":
                learning_velocity = 0.3
                
        # Extract knowledge gaps from session context
        knowledge_gaps = []
        if state.session_context.get("knowledge_gaps"):
            knowledge_gaps = state.session_context.get("knowledge_gaps", [])
            
        # Extract recent struggles from session context
        recent_struggles = []
        if state.session_context.get("recent_struggles"):
            recent_struggles = state.session_context.get("recent_struggles", [])
            
        # Get preferred learning style from user profile
        preferred_learning_style = None
        if user_profile and hasattr(user_profile, 'preferred_learning_style'):
            preferred_learning_style = user_profile.preferred_learning_style
            
        return IntelligentContext(
            user_intent=intent,
            topic_familiarity=topic_familiarity,
            engagement_level=engagement_level,
            learning_velocity=learning_velocity,
            recent_struggles=recent_struggles,
            knowledge_gaps=knowledge_gaps,
            preferred_learning_style=preferred_learning_style,
            session_context=state.session_context
        )
    
    @staticmethod
    def determine_proactive_actions(context: IntelligentContext, state: ConstellationState) -> List[ProactiveAction]:
        """
        Determine proactive actions based on the intelligent context and constellation state.
        
        Args:
            context: Intelligent context
            state: Current constellation state
            
        Returns:
            List[ProactiveAction]: List of proactive actions to take
        """
        actions = []
        
        # Suggest resources for detected knowledge gaps
        if context.knowledge_gaps:
            actions.append(ProactiveAction(
                action_type="suggest_resources",
                description=f"Suggest resources for knowledge gap: {context.knowledge_gaps[0]}",
                priority=0.7,
                metadata={"gap": context.knowledge_gaps[0]}
            ))
            
        # Suggest practice exercises if engagement is low
        if context.engagement_level < 0.4 and context.user_intent == UserIntent.LEARNING:
            actions.append(ProactiveAction(
                action_type="suggest_practice",
                description="Suggest practice exercise to increase engagement",
                priority=0.8,
                metadata={"topic": state.module_id}
            ))
            
        # Offer assessment if learning velocity is high
        if context.learning_velocity > 0.7 and len(state.messages) > 10:
            actions.append(ProactiveAction(
                action_type="offer_assessment",
                description="Offer assessment to validate learning",
                priority=0.6,
                metadata={"module_id": state.module_id}
            ))
            
        # Sort by priority
        actions.sort(key=lambda x: x.priority, reverse=True)
        
        return actions
    
    @staticmethod
    async def execute_proactive_actions(
        actions: List[ProactiveAction], 
        state: ConstellationState
    ) -> Dict[str, Any]:
        """
        Execute proactive actions and return results.
        
        Args:
            actions: List of proactive actions to execute
            state: Current constellation state
            
        Returns:
            Dict[str, Any]: Results of executed actions
        """
        results = {}
        
        for action in actions:
            if action.action_type == "suggest_resources":
                # Add resource suggestions to state context
                state.session_context["suggested_resources"] = [
                    {"title": f"Resource for {action.metadata.get('gap')}", "type": "documentation"}
                ]
                results["suggested_resources"] = True
                
            elif action.action_type == "suggest_practice":
                # Add practice suggestion to state context
                state.session_context["suggested_practice"] = {
                    "topic": action.metadata.get("topic"),
                    "difficulty": "beginner"
                }
                results["suggested_practice"] = True
                
            elif action.action_type == "offer_assessment":
                # Add assessment offer to state context
                state.session_context["assessment_offered"] = {
                    "module_id": action.metadata.get("module_id"),
                    "offered_at": datetime.now().isoformat()
                }
                results["assessment_offered"] = True
                
        return results
    
    @staticmethod
    def select_optimal_agent(
        agents: Union[List[BaseAgent], Dict[AgentRole, BaseAgent]], 
        context: IntelligentContext, 
        state: ConstellationState
    ) -> Tuple[BaseAgent, float]:
        """
        Select the optimal agent based on intelligent context and state.
        
        Args:
            agents: List or dictionary of available agents
            context: Intelligent context
            state: Current constellation state
            
        Returns:
            Tuple[BaseAgent, float]: Selected agent and confidence score
        """
        # Map intents to agent roles
        intent_to_role = {
            UserIntent.LEARNING: AgentRole.INSTRUCTOR,
            UserIntent.CODING: AgentRole.CODE_ASSISTANT,
            UserIntent.TROUBLESHOOTING: AgentRole.TROUBLESHOOTER,
            UserIntent.DOCUMENTATION: AgentRole.DOCUMENTATION_EXPERT,
            UserIntent.PROJECT_PLANNING: AgentRole.PROJECT_GUIDE,
            UserIntent.ASSESSMENT: AgentRole.ASSESSMENT_AGENT,
            UserIntent.EXPLORATION: AgentRole.RESEARCH_ASSISTANT,
            UserIntent.CLARIFICATION: AgentRole.MENTOR,
            UserIntent.PRACTICE: AgentRole.PRACTICE_FACILITATOR,
            UserIntent.GENERAL_QUESTION: AgentRole.KNOWLEDGE_SYNTHESIZER
        }
        
        # Get the preferred agent role based on user intent
        preferred_role = intent_to_role.get(context.user_intent, AgentRole.KNOWLEDGE_SYNTHESIZER)
        
        # Convert agents to list if it's a dictionary
        agent_list = []
        if isinstance(agents, dict):
            agent_list = list(agents.values())
        else:
            agent_list = agents
        
        # Calculate scores for each agent
        agent_scores = []
        for agent in agent_list:
            base_score = 0.5
            
            # Boost score if agent role matches preferred role
            if agent.role == preferred_role:
                base_score += 0.3
                
            # Boost score based on agent's self-reported confidence
            agent_confidence = agent.evaluate_confidence(state)
            base_score += (agent_confidence * 0.2)
            
            # Adjust based on topic familiarity
            if context.topic_familiarity < 0.3 and agent.role in [AgentRole.INSTRUCTOR, AgentRole.MENTOR]:
                base_score += 0.1
            elif context.topic_familiarity > 0.7 and agent.role in [AgentRole.CODE_ASSISTANT, AgentRole.PRACTICE_FACILITATOR]:
                base_score += 0.1
                
            # Adjust based on learning style preference
            if context.preferred_learning_style:
                if context.preferred_learning_style == "visual" and agent.role == AgentRole.DOCUMENTATION_EXPERT:
                    base_score += 0.1
                elif context.preferred_learning_style == "hands-on" and agent.role == AgentRole.PRACTICE_FACILITATOR:
                    base_score += 0.1
                
            # Cap the score at 1.0
            final_score = min(1.0, base_score)
            agent_scores.append((agent, final_score))
            
        # Select the agent with the highest score
        selected_agent, confidence = max(agent_scores, key=lambda x: x[1])
        
        return selected_agent, confidence
    
    async def initialize(self) -> None:
        """Initialize the agent manager and its components."""
        await self.constellation_manager.initialize()
        await self.memory.initialize()
        await self.framework_initializer.initialize()
    
    async def start_session(self, user_id: str, framework: Optional[SupportedFrameworks] = None) -> str:
        """Start a new conversation session."""
        session_id = f"session_{uuid4().hex[:8]}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Load or create user profile
        user_profile = await self._load_user_profile(user_id)
        
        # Set default framework if not provided
        if not framework and user_profile and user_profile.framework_experience:
            # Use the framework with highest experience
            framework_exp = user_profile.framework_experience
            framework_name = max(framework_exp, key=framework_exp.get)
            try:
                framework = SupportedFrameworks(framework_name)
            except ValueError:
                framework = SupportedFrameworks.LANGCHAIN
        elif not framework:
            framework = SupportedFrameworks.LANGCHAIN
        
        # Create conversation config
        config = ConversationConfig(
            framework=framework,
            user_profile=user_profile,
            module_id="general",
        )
        
        # Store session
        self.active_sessions[session_id] = config
        
        # Check if user needs onboarding
        needs_onboarding = await self._check_needs_onboarding(user_id, framework)
        if needs_onboarding:
            self.onboarding_sessions[session_id] = True
            logger.info(f"User {user_id} needs onboarding. Starting onboarding session {session_id}")
        else:
            # Initialize framework context if user doesn't need onboarding
            framework_context = await self.framework_initializer.initialize_framework_context(framework)
            
            # Create a constellation state
            await self._create_constellation_state(session_id, user_id, framework, framework_context)
            
            logger.info(f"Started session {session_id} for user {user_id} with framework {framework.value}")
        
        return session_id
    
    async def process_message(self, session_id: str, user_message: str) -> Dict[str, Any]:
        """Process a user message and generate a response."""
        if session_id not in self.active_sessions:
            return {
                "error": "Session not found",
                "message": "Please start a new session",
                "session_id": None
            }
        
        config = self.active_sessions[session_id]
        user_id = config.user_profile.user_id if config.user_profile else "anonymous"
        
        # Check if this is an onboarding session
        if session_id in self.onboarding_sessions:
            # Process with onboarding flow
            onboarding_result = await self._process_onboarding_message(session_id, user_id, user_message)
            
            # If onboarding is complete, transition to regular session
            if onboarding_result.get("onboarding_complete", False):
                # Remove from onboarding sessions
                del self.onboarding_sessions[session_id]
                
                # Update user profile
                if onboarding_result.get("user_profile"):
                    config.user_profile = onboarding_result["user_profile"]
                    await self._save_user_profile(config.user_profile)
                
                # Update framework if selected during onboarding
                if onboarding_result.get("framework"):
                    config.framework = onboarding_result["framework"]
                
                # Initialize framework context
                framework_context = await self.framework_initializer.initialize_framework_context(config.framework)
                
                # Create constellation state
                state = await self._create_constellation_state(
                    session_id, user_id, config.framework, framework_context
                )
                
                # Generate a personalized welcome message based on the user's profile and selected framework
                welcome_message = self._generate_welcome_message(config.user_profile, config.framework)
                
                # Add welcome message to constellation state
                state.add_message(AIMessage(content=welcome_message))
                
                # Add transition message
                return {
                    "message": welcome_message,
                    "session_id": session_id,
                    "agent_role": "system",
                    "framework": config.framework.value
                }
            
            # Return onboarding response
            return {
                "message": onboarding_result.get("message", ""),
                "session_id": session_id,
                "agent_role": "onboarding",
                "framework": config.framework.value
            }
        
        # Regular session processing
        try:
            # Add message to conversation history
            config.conversation_history.append(HumanMessage(content=user_message))
            
            # Get constellation state
            state = self.constellation_manager.get_session_state(session_id)
            if not state:
                # Re-create state if it was lost
                framework_context = await self.framework_initializer.initialize_framework_context(config.framework)
                state = await self._create_constellation_state(
                    session_id, user_id, config.framework, framework_context
                )
            
            # Add message to constellation state
            state.add_message(HumanMessage(content=user_message))
            
            # Process message with constellation
            updated_state = await self.constellation_manager.run_session(
                session_id,
                user_message,
                config.user_profile,
                config.framework,
                config.module_id
            )
            
            # Get the latest AI message
            ai_messages = [msg for msg in updated_state.messages if isinstance(msg, AIMessage)]
            if not ai_messages:
                response = "I'm sorry, I couldn't generate a response. Please try again."
                agent_role = "system"
            else:
                response = ai_messages[-1].content
                agent_role = updated_state.session_context.get("last_agent", "assistant")
                
                # Add message to conversation history
                config.conversation_history.append(AIMessage(content=response))
            
            # Extract learning insights
            learning_insights = self._extract_learning_insights(user_message, response)
            if learning_insights and config.user_profile:
                await self._update_learning_progress(config.user_profile, learning_insights)
            
            # Record in persistent memory
            await self._record_interaction(
                user_id=user_id,
                session_id=session_id,
                user_message=user_message,
                system_response=response,
                framework=config.framework,
                agent_role=agent_role,
                learning_insights=learning_insights
            )
            
            return {
                "message": response,
                "session_id": session_id,
                "agent_role": agent_role,
                "framework": config.framework.value,
                "learning_insights": learning_insights
            }
            
        except Exception as e:
            logger.error(f"Error processing message: {e}")
            return {
                "error": str(e),
                "message": "I encountered an error processing your message. Please try again.",
                "session_id": session_id,
                "agent_role": "system",
                "framework": config.framework.value
            }
    
    async def _process_onboarding_message(self, session_id: str, user_id: str, message: str) -> Dict[str, Any]:
        """Process a message in an onboarding session."""
        try:
            # If this is the first message in the session
            if not self.onboarding_sessions.get(session_id):
                self.onboarding_sessions[session_id] = True
                
                # Start onboarding
                onboarding_result = await start_onboarding(user_id, session_id, message)
                
                if onboarding_result.get("success", False):
                    # Onboarding completed successfully
                    user_profile = onboarding_result.get("user_profile")
                    framework = onboarding_result.get("framework")
                    learning_objectives = onboarding_result.get("learning_objectives", [])
                    
                    # Mark onboarding as complete
                    self.onboarding_sessions[session_id] = False
                    
                    # Update the conversation config with the results
                    config = self.active_sessions.get(session_id)
                    if config:
                        config.user_profile = user_profile
                        config.framework = framework
                        
                        # Store learning objectives in the session context
                        config.session_context = {
                            "learning_objectives": learning_objectives,
                            "onboarding_complete": True,
                            "framework_context": await self.framework_initializer.initialize_framework_context(framework)
                        }
                    
                    # Save the user profile
                    await self._save_user_profile(user_profile)
                    
                    # Create a constellation state for the session
                    framework_context = await self.framework_initializer.initialize_framework_context(framework)
                    await self._create_constellation_state(session_id, user_id, framework, framework_context)
                    
                    # Generate a personalized learning path based on the learning objectives
                    if learning_objectives:
                        learning_path = await self.learning_engine.generate_learning_path(
                            user_profile=user_profile,
                            objectives=learning_objectives,
                            framework=framework
                        )
                        
                        # Store the learning path in the session context
                        if config:
                            config.session_context["learning_path"] = learning_path
                    
                    # Get formatted curriculum if available
                    formatted_curriculum = ""
                    if "tool_results" in onboarding_result and "formatted_curriculum" in onboarding_result["tool_results"]:
                        formatted_curriculum = onboarding_result["tool_results"]["formatted_curriculum"]
                    
                    # Return success message with curriculum
                    message = onboarding_result.get("messages", [])[-1].content if onboarding_result.get("messages") else "Onboarding complete! Now I'll help you with your learning journey."
                    
                    # Add formatted curriculum to the message if available
                    if formatted_curriculum:
                        message = f"{message}\n\n{formatted_curriculum}"
                    
                    return {
                        "message": message,
                        "onboarding_complete": True,
                        "user_profile": user_profile,
                        "framework": framework.value if framework else None,
                        "formatted_curriculum": formatted_curriculum
                    }
                else:
                    # Onboarding failed
                    return {
                        "message": "I'm sorry, there was an issue with the onboarding process. Let's try again.",
                        "onboarding_complete": False,
                        "error": onboarding_result.get("error", "Unknown error")
                    }
            else:
                # Process subsequent onboarding messages
                # Get the current onboarding state
                onboarding_graph = get_onboarding_graph()
                
                # Add the message to the onboarding state
                # This will continue the onboarding flow
                onboarding_result = await onboarding_graph.start_onboarding(user_id, session_id, message)
                
                if onboarding_result.get("success", False):
                    # Check if onboarding is complete
                    if onboarding_result.get("user_profile") and onboarding_result.get("framework"):
                        # Onboarding is complete
                        user_profile = onboarding_result.get("user_profile")
                        framework = onboarding_result.get("framework")
                        learning_objectives = onboarding_result.get("learning_objectives", [])
                        
                        # Mark onboarding as complete
                        self.onboarding_sessions[session_id] = False
                        
                        # Update the conversation config with the results
                        config = self.active_sessions.get(session_id)
                        if config:
                            config.user_profile = user_profile
                            config.framework = framework
                            
                            # Store learning objectives in the session context
                            config.session_context = {
                                "learning_objectives": learning_objectives,
                                "onboarding_complete": True,
                                "framework_context": await self.framework_initializer.initialize_framework_context(framework)
                            }
                        
                        # Save the user profile
                        await self._save_user_profile(user_profile)
                        
                        # Create a constellation state for the session
                        framework_context = await self.framework_initializer.initialize_framework_context(framework)
                        await self._create_constellation_state(session_id, user_id, framework, framework_context)
                        
                        # Generate a personalized learning path based on the learning objectives
                        if learning_objectives:
                            learning_path = await self.learning_engine.generate_learning_path(
                                user_profile=user_profile,
                                objectives=learning_objectives,
                                framework=framework
                            )
                            
                            # Store the learning path in the session context
                            if config:
                                config.session_context["learning_path"] = learning_path
                        
                        # Get formatted curriculum if available
                        formatted_curriculum = ""
                        if "tool_results" in onboarding_result and "formatted_curriculum" in onboarding_result["tool_results"]:
                            formatted_curriculum = onboarding_result["tool_results"]["formatted_curriculum"]
                        
                        # Return success message with curriculum
                        message = onboarding_result.get("messages", [])[-1].content if onboarding_result.get("messages") else "Onboarding complete! Now I'll help you with your learning journey."
                        
                        # Add formatted curriculum to the message if available
                        if formatted_curriculum:
                            message = f"{message}\n\n{formatted_curriculum}"
                        
                        return {
                            "message": message,
                            "onboarding_complete": True,
                            "user_profile": user_profile,
                            "framework": framework.value if framework else None,
                            "formatted_curriculum": formatted_curriculum
                        }
                    else:
                        # Onboarding is still in progress
                        return {
                            "message": onboarding_result.get("messages", [])[-1].content if onboarding_result.get("messages") else "Let's continue with your onboarding.",
                            "onboarding_complete": False
                        }
                else:
                    # Onboarding message processing failed
                    return {
                        "message": "I'm sorry, there was an issue processing your message. Let's try again.",
                        "onboarding_complete": False,
                        "error": onboarding_result.get("error", "Unknown error")
                    }
        except Exception as e:
            logger.error(f"Error in onboarding process: {e}")
            return {
                "message": f"I encountered an error during onboarding: {str(e)}. Let's try again.",
                "onboarding_complete": False,
                "error": str(e)
            }
    
    async def _check_needs_onboarding(self, user_id: str, framework: SupportedFrameworks) -> bool:
        """Check if a user needs onboarding."""
        # Load user profile
        profile = await self._load_user_profile(user_id)
        
        # If no profile exists, definitely need onboarding
        if not profile:
            return True
            
        # Check if user has experience with this framework
        if str(framework) not in profile.framework_experience:
            return True
            
        # Check if user has completed onboarding for this framework
        if profile.preferences:
            completed_onboarding = profile.preferences.get(f"completed_onboarding_{framework.value}", False)
            if not completed_onboarding:
                return True
                
        # No need for onboarding
        return False
    
    async def _create_constellation_state(
        self, 
        session_id: str, 
        user_id: str, 
        framework: SupportedFrameworks,
        framework_context: Optional[FrameworkContext] = None
    ) -> ConstellationState:
        """Create a constellation state for the session."""
        try:
            # Get user preferences
            user_profile = self.active_sessions[session_id].user_profile
            user_preferences = {}
            if user_profile:
                user_preferences = {
                    "skill_level": user_profile.skill_level.value if hasattr(user_profile, 'skill_level') and user_profile.skill_level else "intermediate",
                    "learning_style": user_profile.learning_style_preferences[0] if hasattr(user_profile, 'learning_style_preferences') and user_profile.learning_style_preferences else "visual",
                    "pace": user_profile.learning_pace.value if hasattr(user_profile, 'learning_pace') and user_profile.learning_pace else "moderate"
                }
            
            # Get learning objectives from the session context
            learning_objectives = []
            session_context = self.active_sessions[session_id].session_context if hasattr(self.active_sessions[session_id], 'session_context') else {}
            if session_context and "learning_objectives" in session_context:
                learning_objectives = session_context["learning_objectives"]
            
            # Get learning path from the session context
            learning_path = None
            if session_context and "learning_path" in session_context:
                learning_path = session_context["learning_path"]
            
            # Create initial state
            state = ConstellationState(
                session_id=session_id,
                user_id=user_id,
                framework=framework,
                module_id="general",
                messages=[],
                user_preferences=user_preferences,
                framework_context=framework_context,
                session_context={
                    "onboarding_complete": True,
                    "learning_objectives": [obj.objective_id for obj in learning_objectives] if learning_objectives else [],
                    "learning_path_id": learning_path.path_id if learning_path else None,
                    "current_module": learning_path.current_module_id if learning_path else None,
                    "framework_context_available": framework_context is not None
                }
            )
            
            # Store the state
            self.constellation_manager.set_session_state(session_id, state)
            
            # Initialize analytics for the session
            await self.learning_engine.initialize_analytics_session(
                user_id=user_id,
                session_id=session_id,
                framework=framework
            )
            
            return state
            
        except Exception as e:
            logger.error(f"Error creating constellation state: {e}")
            # Create a minimal state as fallback
            state = ConstellationState(
                session_id=session_id,
                user_id=user_id,
                framework=framework,
                module_id="general",
                messages=[]
            )
            self.constellation_manager.set_session_state(session_id, state)
            return state
    
    async def _load_user_profile(self, user_id: str) -> Optional[UserProfile]:
        """Load a user profile from storage."""
        try:
            # For now, return None since user profiles are managed through onboarding
            # This can be enhanced later to load profiles from storage
            return None
            
        except Exception as e:
            logger.error(f"Error loading user profile: {e}")
            return None
    
    async def _save_user_profile(self, profile: UserProfile) -> bool:
        """Save a user profile to storage."""
        try:
            # For now, skip saving since user profiles are managed through onboarding
            # This can be enhanced later to save profiles to storage
            return True
            
        except Exception as e:
            logger.error(f"Error saving user profile: {e}")
            return False
    
    async def _update_learning_progress(self, profile: UserProfile, insights: Dict[str, Any]) -> None:
        """Update user learning progress based on insights."""
        if not insights:
            return
            
        # Update learning sessions
        if insights.get("topics"):
            active_sessions = profile.learning_sessions
            if active_sessions:
                current_session = active_sessions[-1]
                for topic in insights["topics"]:
                    if topic not in current_session.topics_covered:
                        current_session.topics_covered.append(topic)
                
                # Update progress percentage
                if current_session.learning_objectives:
                    completed = sum(1 for topic in current_session.topics_covered 
                                   if any(topic in obj for obj in current_session.learning_objectives))
                    total = len(current_session.learning_objectives)
                    current_session.completion_percentage = (completed / total) * 100 if total > 0 else 0
        
        # Update framework experience
        if insights.get("framework"):
            framework = insights["framework"]
            current_exp = profile.framework_experience.get(framework, 0.0)
            profile.framework_experience[framework] = min(current_exp + 0.01, 10.0)  # Cap at 10
        
        # Save updated profile
        await self._save_user_profile(profile)
    
    def _extract_learning_insights(self, user_message: str, system_response: str) -> Dict[str, Any]:
        """Extract learning insights from the conversation."""
        insights = {}
        
        # Simple keyword-based topic extraction
        topics = []
        
        # Check for programming concepts
        programming_concepts = ["function", "class", "method", "object", "variable", "inheritance", "api"]
        for concept in programming_concepts:
            if concept in user_message.lower() or concept in system_response.lower():
                topics.append(concept)
        
        # Check for framework-specific concepts
        framework_concepts = {
            "langchain": ["chains", "llms", "agents", "memory", "prompts", "embeddings", "vectorstores"],
            "langgraph": ["graph", "nodes", "edges", "state", "multi-agent"],
            "crewai": ["crew", "agents", "tasks", "processes", "workflows"],
            "autogen": ["agents", "conversation", "groupchat", "workflow"]
        }
        
        for framework, concepts in framework_concepts.items():
            for concept in concepts:
                if concept in user_message.lower() or concept in system_response.lower():
                    topics.append(f"{framework}_{concept}")
                    insights["framework"] = framework
        
        if topics:
            insights["topics"] = list(set(topics))  # Deduplicate
        
        return insights
    
    async def _record_interaction(
        self, 
        user_id: str,
        session_id: str, 
        user_message: str, 
        system_response: str,
        framework: SupportedFrameworks,
        agent_role: str,
        learning_insights: Optional[Dict[str, Any]] = None
    ) -> None:
        """Record an interaction in persistent memory."""
        try:
            interaction_data = {
                "user_id": user_id,
                "session_id": session_id,
                "timestamp": datetime.now().isoformat(),
                "user_message": user_message,
                "system_response": system_response,
                "framework": framework.value,
                "agent_role": agent_role
            }
            
            if learning_insights:
                interaction_data["learning_insights"] = learning_insights
            
            self.memory.save_conversation(
                session_id=session_id,
                user_id=user_id,
                user_message=user_message,
                ai_response=system_response,
                framework=framework.value,
                topic=learning_insights.get("framework") if learning_insights else None
            )
            
        except Exception as e:
            logger.error(f"Error recording interaction: {e}")
    
    async def end_session(self, session_id: str) -> bool:
        """End a conversation session."""
        try:
            # Clean up constellation session
            self.constellation_manager.end_session(session_id)
            
            # Remove from active sessions
            if session_id in self.active_sessions:
                del self.active_sessions[session_id]
                
            # Remove from onboarding sessions
            if session_id in self.onboarding_sessions:
                del self.onboarding_sessions[session_id]
                
            logger.info(f"Ended session {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error ending session {session_id}: {e}")
            return False
    
    def get_active_sessions(self) -> List[str]:
        """Get a list of active session IDs."""
        return list(self.active_sessions.keys())
    
    def get_session_info(self, session_id: str) -> Dict[str, Any]:
        """Get information about a specific session."""
        if session_id not in self.active_sessions:
            return {"error": "Session not found"}
            
        config = self.active_sessions[session_id]
        return {
            "session_id": session_id,
            "framework": config.framework.value,
            "module_id": config.module_id,
            "onboarding": session_id in self.onboarding_sessions,
            "message_count": len(config.conversation_history)
        }

    def _generate_welcome_message(self, user_profile, framework) -> str:
        """Generate a personalized welcome message based on user profile and selected framework."""
        # Get user's name or use a generic greeting
        name = user_profile.name if user_profile and hasattr(user_profile, 'name') and user_profile.name else "there"
        
        # Get user's skill level
        skill_level = "beginner"
        if user_profile and hasattr(user_profile, 'skill_level') and user_profile.skill_level:
            skill_level = user_profile.skill_level.value
        
        # Get framework name
        framework_name = framework.value if framework else "Python framework"
        
        # Generate personalized welcome message
        welcome_message = f"""Welcome to your personalized {framework_name} learning journey, {name}!
        
Based on your profile and learning goals, I've created a customized curriculum to help you master {framework_name} at your own pace. As a {skill_level} learner, we'll focus on building your skills step by step.

Here's what you can expect:
• Interactive lessons tailored to your learning style
• Hands-on coding exercises to reinforce concepts
• Real-time assistance from specialized AI agents
• Progress tracking to keep you motivated

You can ask me questions about {framework_name}, request code examples, or get help with specific concepts at any time. I'm here to support your learning journey!

What would you like to start with today?
"""
        
        return welcome_message


# Factory function to get a singleton instance
_agent_manager_instance = None

async def get_agent_manager() -> IntelligentAgentManager:
    """Get a singleton instance of the IntelligentAgentManager."""
    global _agent_manager_instance
    
    if _agent_manager_instance is None:
        _agent_manager_instance = IntelligentAgentManager()
        await _agent_manager_instance.initialize()
    
    return _agent_manager_instance 