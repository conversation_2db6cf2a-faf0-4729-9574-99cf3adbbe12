#!/usr/bin/env python3
"""
Convenience script to run the GAAPF - Guidance AI Agent CLI.
"""

import asyncio
import sys
import os

# Add src to path
src_dir = os.path.join(os.path.dirname(__file__), "src")
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

from pyframeworks_assistant.interfaces.cli.main import main

if __name__ == "__main__":
    print("🚀 Starting GAAPF - Guidance AI Agent CLI...")
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1) 