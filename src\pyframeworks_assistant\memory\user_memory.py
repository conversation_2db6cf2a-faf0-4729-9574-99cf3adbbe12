"""
User memory management for tracking individual user learning history and preferences.
Handles persistent user data, learning analytics, and personalization.
"""

from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
import json
import logging

from pydantic import BaseModel, Field

from ..config.user_profiles import UserProfile, SkillLevel, LearningPace, LearningStyle
from ..config.framework_configs import SupportedFrameworks

logger = logging.getLogger(__name__)


class LearningSession(BaseModel):
    """Individual learning session record."""
    session_id: str = Field(description="Session identifier")
    start_time: datetime = Field(description="Session start time")
    end_time: Optional[datetime] = Field(default=None, description="Session end time")
    framework: str = Field(description="Framework studied")
    topics_covered: List[str] = Field(default_factory=list, description="Topics covered")
    exercises_completed: List[str] = Field(default_factory=list, description="Exercises completed")
    quiz_scores: List[float] = Field(default_factory=list, description="Quiz scores (0-1)")
    time_spent: int = Field(default=0, description="Time spent in minutes")
    engagement_score: float = Field(default=0.0, description="Session engagement score")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class LearningHistory(BaseModel):
    """Complete learning history for a user."""
    user_id: str = Field(description="User identifier")
    total_sessions: int = Field(default=0, description="Total number of sessions")
    total_time: int = Field(default=0, description="Total time spent learning (minutes)")
    frameworks_studied: List[str] = Field(default_factory=list, description="Frameworks studied")
    skill_progression: Dict[str, float] = Field(default_factory=dict, description="Skill levels by framework")
    favorite_topics: List[str] = Field(default_factory=list, description="Most studied topics")
    learning_streak: int = Field(default=0, description="Current learning streak (days)")
    achievements: List[str] = Field(default_factory=list, description="Unlocked achievements")
    last_active: Optional[datetime] = Field(default=None, description="Last activity timestamp")
    sessions: List[LearningSession] = Field(default_factory=list, description="Learning sessions")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class UserMemory:
    """Manages user-specific memory and learning data."""
    
    def __init__(self, user_id: str):
        """Initialize user memory."""
        self.user_id = user_id
        self.profile: Optional[UserProfile] = None
        self.learning_history = LearningHistory(user_id=user_id)
        self.preferences: Dict[str, Any] = {}
        self.active_session: Optional[LearningSession] = None
        
    def set_profile(self, profile: UserProfile) -> None:
        """Set user profile."""
        self.profile = profile
        
    def get_profile(self) -> Optional[UserProfile]:
        """Get user profile."""
        return self.profile
        
    def start_session(self, framework: str, session_id: str) -> LearningSession:
        """Start a new learning session."""
        session = LearningSession(
            session_id=session_id,
            start_time=datetime.now(),
            framework=framework
        )
        
        self.active_session = session
        return session
    
    def end_session(self) -> Optional[LearningSession]:
        """End the current session and save to history."""
        if not self.active_session:
            return None
        
        # Complete the session
        self.active_session.end_time = datetime.now()
        
        if self.active_session.start_time:
            duration = self.active_session.end_time - self.active_session.start_time
            self.active_session.time_spent = int(duration.total_seconds() / 60)
        
        # Add to history
        self.learning_history.sessions.append(self.active_session)
        self.learning_history.total_sessions += 1
        self.learning_history.total_time += self.active_session.time_spent
        self.learning_history.last_active = self.active_session.end_time
        
        # Update framework list
        if self.active_session.framework not in self.learning_history.frameworks_studied:
            self.learning_history.frameworks_studied.append(self.active_session.framework)
        
        # Update skill progression
        self._update_skill_progression()
        
        # Update learning streak
        self._update_learning_streak()
        
        completed_session = self.active_session
        self.active_session = None
        
        return completed_session
    
    def add_topic_progress(self, topic: str) -> None:
        """Add topic to current session."""
        if self.active_session and topic not in self.active_session.topics_covered:
            self.active_session.topics_covered.append(topic)
    
    def add_exercise_completion(self, exercise_id: str) -> None:
        """Add completed exercise to current session."""
        if self.active_session and exercise_id not in self.active_session.exercises_completed:
            self.active_session.exercises_completed.append(exercise_id)
    
    def add_quiz_score(self, score: float) -> None:
        """Add quiz score to current session."""
        if self.active_session:
            self.active_session.quiz_scores.append(max(0.0, min(1.0, score)))
    
    def update_engagement(self, score: float) -> None:
        """Update engagement score for current session."""
        if self.active_session:
            self.active_session.engagement_score = max(0.0, min(1.0, score))
    
    def get_learning_analytics(self) -> Dict[str, Any]:
        """Get comprehensive learning analytics."""
        if not self.learning_history.sessions:
            return {
                "total_sessions": 0,
                "total_time": 0,
                "average_session_time": 0,
                "frameworks_studied": [],
                "skill_levels": {},
                "learning_streak": 0,
                "recent_activity": []
            }
        
        # Calculate analytics
        recent_sessions = self.learning_history.sessions[-10:]  # Last 10 sessions
        
        avg_session_time = (
            self.learning_history.total_time / self.learning_history.total_sessions
            if self.learning_history.total_sessions > 0 else 0
        )
        
        # Quiz performance by framework
        quiz_performance = {}
        for session in self.learning_history.sessions:
            if session.quiz_scores:
                framework = session.framework
                if framework not in quiz_performance:
                    quiz_performance[framework] = []
                quiz_performance[framework].extend(session.quiz_scores)
        
        avg_quiz_performance = {
            framework: sum(scores) / len(scores)
            for framework, scores in quiz_performance.items()
        }
        
        return {
            "total_sessions": self.learning_history.total_sessions,
            "total_time": self.learning_history.total_time,
            "average_session_time": round(avg_session_time, 1),
            "frameworks_studied": self.learning_history.frameworks_studied,
            "skill_levels": self.learning_history.skill_progression,
            "learning_streak": self.learning_history.learning_streak,
            "quiz_performance": avg_quiz_performance,
            "recent_activity": [
                {
                    "date": session.start_time.date().isoformat(),
                    "framework": session.framework,
                    "topics": len(session.topics_covered),
                    "time": session.time_spent
                }
                for session in recent_sessions
            ],
            "achievements": self.learning_history.achievements
        }
    
    def _update_skill_progression(self) -> None:
        """Update skill progression based on recent activity."""
        if not self.active_session:
            return
        
        framework = self.active_session.framework
        current_skill = self.learning_history.skill_progression.get(framework, 0.0)
        
        # Calculate skill increase based on session activity
        progress_factors = []
        
        # Topics covered
        topic_progress = min(len(self.active_session.topics_covered) * 0.1, 0.3)
        progress_factors.append(topic_progress)
        
        # Exercises completed
        exercise_progress = min(len(self.active_session.exercises_completed) * 0.15, 0.4)
        progress_factors.append(exercise_progress)
        
        # Quiz performance
        if self.active_session.quiz_scores:
            avg_quiz_score = sum(self.active_session.quiz_scores) / len(self.active_session.quiz_scores)
            quiz_progress = avg_quiz_score * 0.3
            progress_factors.append(quiz_progress)
        
        # Time spent (diminishing returns)
        time_progress = min(self.active_session.time_spent / 120.0, 0.2)  # Max 0.2 for 2 hours
        progress_factors.append(time_progress)
        
        # Calculate new skill level
        skill_increase = sum(progress_factors)
        new_skill = min(current_skill + skill_increase, 1.0)
        
        self.learning_history.skill_progression[framework] = new_skill
    
    def _update_learning_streak(self) -> None:
        """Update learning streak based on session dates."""
        if not self.learning_history.sessions:
            self.learning_history.learning_streak = 0
            return
        
        # Get unique session dates
        session_dates = list(set(
            session.start_time.date() for session in self.learning_history.sessions
        ))
        session_dates.sort(reverse=True)
        
        if not session_dates:
            self.learning_history.learning_streak = 0
            return
        
        # Calculate streak
        streak = 0
        today = datetime.now().date()
        
        for i, date in enumerate(session_dates):
            expected_date = today - timedelta(days=i)
            if date == expected_date:
                streak += 1
            else:
                break
        
        self.learning_history.learning_streak = streak
    
    def get_recommendations(self) -> Dict[str, Any]:
        """Get personalized learning recommendations."""
        recommendations = {
            "next_topics": [],
            "suggested_frameworks": [],
            "study_schedule": {},
            "areas_to_improve": []
        }
        
        if not self.learning_history.sessions:
            return {
                "next_topics": ["Start with LangChain basics"],
                "suggested_frameworks": ["langchain"],
                "study_schedule": {"recommended_frequency": "3-4 times per week"},
                "areas_to_improve": ["Begin your learning journey"]
            }
        
        # Analyze recent activity
        recent_frameworks = set()
        recent_topics = set()
        
        for session in self.learning_history.sessions[-5:]:  # Last 5 sessions
            recent_frameworks.add(session.framework)
            recent_topics.update(session.topics_covered)
        
        # Recommend next topics based on skill progression
        for framework, skill_level in self.learning_history.skill_progression.items():
            if skill_level < 0.3:
                recommendations["next_topics"].append(f"Continue {framework} fundamentals")
            elif skill_level < 0.7:
                recommendations["next_topics"].append(f"Explore advanced {framework} features")
            else:
                recommendations["next_topics"].append(f"Build projects with {framework}")
        
        # Suggest new frameworks
        all_frameworks = ["langchain", "langgraph", "fastapi", "streamlit"]
        unstudied = [fw for fw in all_frameworks if fw not in self.learning_history.frameworks_studied]
        recommendations["suggested_frameworks"] = unstudied[:2]
        
        # Study schedule based on current streak and profile
        if self.learning_history.learning_streak > 7:
            recommendations["study_schedule"] = {"status": "Great streak! Keep it up!"}
        elif self.learning_history.learning_streak > 0:
            recommendations["study_schedule"] = {"status": "Good momentum", "suggestion": "Try to study daily"}
        else:
            recommendations["study_schedule"] = {"status": "Start building a routine", "suggestion": "Aim for 3 sessions per week"}
        
        return recommendations


class PreferenceManager:
    """Manages user preferences and personalization settings."""
    
    def __init__(self, user_id: str):
        """Initialize preference manager."""
        self.user_id = user_id
        self.preferences: Dict[str, Any] = {
            "notification_settings": {
                "daily_reminders": True,
                "achievement_alerts": True,
                "progress_reports": True
            },
            "learning_preferences": {
                "preferred_difficulty": "adaptive",
                "example_style": "detailed",
                "feedback_frequency": "immediate"
            },
            "interface_preferences": {
                "theme": "light",
                "language": "english",
                "accessibility": {}
            }
        }
    
    def get_preference(self, category: str, key: str) -> Any:
        """Get a specific preference value."""
        return self.preferences.get(category, {}).get(key)
    
    def set_preference(self, category: str, key: str, value: Any) -> None:
        """Set a specific preference value."""
        if category not in self.preferences:
            self.preferences[category] = {}
        self.preferences[category][key] = value
    
    def get_all_preferences(self) -> Dict[str, Any]:
        """Get all preferences."""
        return self.preferences.copy()
    
    def update_preferences(self, preferences: Dict[str, Any]) -> None:
        """Update multiple preferences."""
        for category, settings in preferences.items():
            if category not in self.preferences:
                self.preferences[category] = {}
            self.preferences[category].update(settings)
    
    def reset_preferences(self) -> None:
        """Reset preferences to defaults."""
        self.__init__(self.user_id)


class UserMemoryManager:
    """
    Manager for user memory across the system.
    Provides the interface needed by the Learning Hub.
    """
    
    def __init__(self):
        """Initialize the user memory manager."""
        self.user_memories: Dict[str, UserMemory] = {}
        logger.info("UserMemoryManager initialized")
    
    async def initialize(self) -> None:
        """Initialize the user memory manager asynchronously."""
        try:
            # Any async initialization can go here
            logger.info("UserMemoryManager async initialization complete")
        except Exception as e:
            logger.error(f"Failed to initialize UserMemoryManager: {e}")
            raise
    
    async def update_session_history(self, user_id: str, session_id: str) -> None:
        """Update user's session history."""
        try:
            if user_id not in self.user_memories:
                self.user_memories[user_id] = UserMemory(user_id)
            
            # Note: In a real implementation, you might want to store more session details
            logger.info(f"Updated session history for user {user_id}, session {session_id}")
            
        except Exception as e:
            logger.error(f"Failed to update session history: {e}")
    
    async def update_learning_history(
        self,
        user_id: str,
        framework: SupportedFrameworks,
        effectiveness_score: float,
        completion_percentage: float
    ) -> None:
        """Update user's learning history with session results."""
        try:
            if user_id not in self.user_memories:
                self.user_memories[user_id] = UserMemory(user_id)
            
            user_memory = self.user_memories[user_id]
            
            # Update skill progression
            framework_str = framework.value
            current_skill = user_memory.learning_history.skill_progression.get(framework_str, 0.0)
            
            # Calculate skill improvement based on effectiveness and completion
            skill_improvement = (effectiveness_score * 0.3 + completion_percentage / 100 * 0.7) * 0.1
            new_skill = min(1.0, current_skill + skill_improvement)
            
            user_memory.learning_history.skill_progression[framework_str] = new_skill
            
            # Update last active time
            user_memory.learning_history.last_active = datetime.now()
            
            logger.info(f"Updated learning history for user {user_id}")
            
        except Exception as e:
            logger.error(f"Failed to update learning history: {e}")
    
    def get_user_memory(self, user_id: str) -> UserMemory:
        """Get or create user memory."""
        if user_id not in self.user_memories:
            self.user_memories[user_id] = UserMemory(user_id)
        return self.user_memories[user_id]
    
    def get_user_analytics(self, user_id: str) -> Dict[str, Any]:
        """Get user learning analytics."""
        user_memory = self.get_user_memory(user_id)
        return user_memory.get_learning_analytics()


# Global user memory storage (in production, this would be a proper database)
_user_memories: Dict[str, UserMemory] = {}


def get_user_memory(user_id: str) -> UserMemory:
    """Get or create user memory instance."""
    if user_id not in _user_memories:
        _user_memories[user_id] = UserMemory(user_id)
    return _user_memories[user_id]


def get_user_preferences(user_id: str) -> PreferenceManager:
    """Get or create user preferences manager."""
    return PreferenceManager(user_id) 