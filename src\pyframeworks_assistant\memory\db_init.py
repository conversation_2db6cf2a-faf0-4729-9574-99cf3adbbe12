"""
Database Initialization Script for PyFrameworks Assistant
Ensures all storage systems are properly initialized before starting guidance.
"""

import os
import logging
from pathlib import Path
from typing import Dict, Any, Optional
import sqlite3
import json
from datetime import datetime

from .persistent_storage import (
    DatabaseManager, 
    VectorStoreManager, 
    RedisCache, 
    PersistentStorageManager,
    ConversationRecord,
    LearningProgressRecord
)
from .constellation_memory import ConstellationMemoryManager, get_constellation_memory
from ..config.settings import settings

logger = logging.getLogger(__name__)


class StorageInitializer:
    """Handles initialization of all storage systems."""
    
    def __init__(self):
        """Initialize storage initializer."""
        self.db_manager: Optional[DatabaseManager] = None
        self.vector_manager: Optional[VectorStoreManager] = None
        self.cache: Optional[RedisCache] = None
        self.storage_manager: Optional[PersistentStorageManager] = None
        self.constellation_memory: Optional[ConstellationMemoryManager] = None
        
    def initialize_all_storage(self) -> Dict[str, Any]:
        """Initialize all storage systems and return status."""
        results = {
            "database": {"status": "pending", "type": None, "error": None},
            "vector_store": {"status": "pending", "error": None},
            "cache": {"status": "pending", "error": None},
            "constellation_memory": {"status": "pending", "error": None},
            "storage_manager": {"status": "pending", "error": None}
        }
        
        # 1. Initialize database
        try:
            logger.info("Initializing database storage...")
            self.db_manager = DatabaseManager()
            results["database"]["status"] = "success"
            results["database"]["type"] = "PostgreSQL" if self.db_manager.use_postgres else "SQLite"
            results["database"]["path"] = str(self.db_manager.db_path) if not self.db_manager.use_postgres else "PostgreSQL"
            logger.info(f"Database initialized: {results['database']['type']}")
        except Exception as e:
            logger.error(f"Database initialization failed: {e}")
            results["database"]["status"] = "failed"
            results["database"]["error"] = str(e)
        
        # 2. Initialize vector store
        try:
            logger.info("Initializing vector store...")
            self.vector_manager = VectorStoreManager()
            results["vector_store"]["status"] = "success"
            results["vector_store"]["path"] = str(self.vector_manager.persist_directory)
            logger.info("Vector store initialized")
        except Exception as e:
            logger.error(f"Vector store initialization failed: {e}")
            results["vector_store"]["status"] = "failed"
            results["vector_store"]["error"] = str(e)
        
        # 3. Initialize cache
        try:
            logger.info("Initializing cache...")
            self.cache = RedisCache()
            results["cache"]["status"] = "success" if self.cache.available else "unavailable"
            if not self.cache.available:
                results["cache"]["error"] = "Redis not available, using memory fallback"
            logger.info(f"Cache initialized: {'Redis' if self.cache.available else 'Memory fallback'}")
        except Exception as e:
            logger.error(f"Cache initialization failed: {e}")
            results["cache"]["status"] = "failed"
            results["cache"]["error"] = str(e)
        
        # 4. Initialize constellation memory
        try:
            logger.info("Initializing constellation memory...")
            self.constellation_memory = get_constellation_memory()
            results["constellation_memory"]["status"] = "success"
            logger.info("Constellation memory initialized")
        except Exception as e:
            logger.error(f"Constellation memory initialization failed: {e}")
            results["constellation_memory"]["status"] = "failed"
            results["constellation_memory"]["error"] = str(e)
        
        # 5. Initialize main storage manager
        try:
            logger.info("Initializing main storage manager...")
            self.storage_manager = PersistentStorageManager()
            results["storage_manager"]["status"] = "success"
            logger.info("Main storage manager initialized")
        except Exception as e:
            logger.error(f"Storage manager initialization failed: {e}")
            results["storage_manager"]["status"] = "failed"
            results["storage_manager"]["error"] = str(e)
        
        # 6. Verify storage systems
        verification_results = self._verify_storage_systems()
        results["verification"] = verification_results
        
        return results
    
    def _verify_storage_systems(self) -> Dict[str, Any]:
        """Verify that all storage systems are working correctly."""
        verification = {
            "database_tables": {"status": "pending"},
            "vector_collections": {"status": "pending"},
            "constellation_memory": {"status": "pending"},
            "test_operations": {"status": "pending"}
        }
        
        # Verify database tables
        try:
            if self.db_manager:
                if self.db_manager.use_postgres:
                    # PostgreSQL verification
                    verification["database_tables"]["status"] = "success"
                    verification["database_tables"]["type"] = "PostgreSQL"
                else:
                    # SQLite verification
                    with sqlite3.connect(self.db_manager.db_path) as conn:
                        cursor = conn.cursor()
                        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                        tables = [row[0] for row in cursor.fetchall()]
                        verification["database_tables"]["status"] = "success"
                        verification["database_tables"]["tables"] = tables
                        verification["database_tables"]["type"] = "SQLite"
        except Exception as e:
            verification["database_tables"]["status"] = "failed"
            verification["database_tables"]["error"] = str(e)
        
        # Verify vector collections
        try:
            if self.vector_manager:
                collections = ["conversations", "knowledge_base", "learning_content"]
                collection_status = {}
                for collection_name in collections:
                    try:
                        collection = getattr(self.vector_manager, f"{collection_name.replace('_', '_')}_collection")
                        collection_status[collection_name] = "available"
                    except Exception as e:
                        collection_status[collection_name] = f"error: {str(e)}"
                
                verification["vector_collections"]["status"] = "success"
                verification["vector_collections"]["collections"] = collection_status
        except Exception as e:
            verification["vector_collections"]["status"] = "failed"
            verification["vector_collections"]["error"] = str(e)
        
        # Verify constellation memory
        try:
            if self.constellation_memory:
                # Test basic constellation memory operations
                test_user_id = f"test_user_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                analytics = self.constellation_memory.get_user_constellation_analytics(test_user_id)
                verification["constellation_memory"]["status"] = "success"
                verification["constellation_memory"]["test_result"] = analytics
        except Exception as e:
            verification["constellation_memory"]["status"] = "failed"
            verification["constellation_memory"]["error"] = str(e)
        
        # Test basic operations
        try:
            if self.storage_manager and self.constellation_memory:
                # Test storage operations
                test_session_id = f"test_session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                test_user_id = f"test_user_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                
                # Test constellation session creation
                from ..core.constellation_types import ConstellationType
                session = self.constellation_memory.start_constellation_session(
                    session_id=test_session_id,
                    user_id=test_user_id,
                    initial_constellation=ConstellationType.THEORY_PRACTICE_BALANCED,
                    learning_objectives=["test_initialization"]
                )
                
                # Clean up test session
                self.constellation_memory.end_constellation_session(
                    session_id=test_session_id,
                    final_skill_assessment={},
                    achievements=[]
                )
                
                verification["test_operations"]["status"] = "success"
                verification["test_operations"]["test_session_id"] = test_session_id
        except Exception as e:
            verification["test_operations"]["status"] = "failed"
            verification["test_operations"]["error"] = str(e)
        
        return verification
    
    def create_sample_data(self) -> Dict[str, Any]:
        """Create sample data for testing and demonstration."""
        if not all([self.storage_manager, self.constellation_memory]):
            return {"status": "failed", "error": "Storage systems not initialized"}
        
        try:
            # Create sample user profile data
            sample_user_id = "demo_user_001"
            
            # Create sample learning progress
            progress_record = LearningProgressRecord(
                user_id=sample_user_id,
                framework="langchain",
                current_phase="beginner",
                completed_topics=["introduction", "basic_concepts"],
                mastered_concepts=["llm_basics", "prompt_engineering"],
                engagement_level=0.8,
                study_time_minutes=120,
                last_activity=datetime.now(),
                metadata={"sample_data": True}
            )
            
            self.storage_manager.save_learning_progress(
                user_id=sample_user_id,
                framework="langchain",
                progress_data={
                    "current_phase": progress_record.current_phase,
                    "completed_topics": progress_record.completed_topics,
                    "mastered_concepts": progress_record.mastered_concepts,
                    "engagement_level": progress_record.engagement_level,
                    "study_time_minutes": progress_record.study_time_minutes,
                    "metadata": progress_record.metadata
                }
            )
            
            # Create sample conversation
            self.storage_manager.save_conversation(
                session_id="demo_session_001",
                user_id=sample_user_id,
                user_message="What is LangChain?",
                ai_response="LangChain is a framework for developing applications powered by language models...",
                framework="langchain",
                topic="introduction"
            )
            
            return {
                "status": "success",
                "sample_user_id": sample_user_id,
                "created_records": {
                    "learning_progress": 1,
                    "conversations": 1
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to create sample data: {e}")
            return {"status": "failed", "error": str(e)}
    
    def get_initialization_status(self) -> Dict[str, Any]:
        """Get current initialization status."""
        status = {
            "timestamp": datetime.now().isoformat(),
            "components": {
                "database": self.db_manager is not None,
                "vector_store": self.vector_manager is not None,
                "cache": self.cache is not None,
                "constellation_memory": self.constellation_memory is not None,
                "storage_manager": self.storage_manager is not None
            }
        }
        
        if self.storage_manager:
            try:
                status["storage_stats"] = self.storage_manager.get_storage_stats()
            except Exception as e:
                status["storage_stats_error"] = str(e)
        
        return status


def initialize_storage_systems() -> Dict[str, Any]:
    """Main function to initialize all storage systems."""
    logger.info("Starting storage systems initialization...")
    
    initializer = StorageInitializer()
    results = initializer.initialize_all_storage()
    
    # Log summary
    successful_components = [
        comp for comp, data in results.items() 
        if isinstance(data, dict) and data.get("status") == "success"
    ]
    
    failed_components = [
        comp for comp, data in results.items() 
        if isinstance(data, dict) and data.get("status") == "failed"
    ]
    
    logger.info(f"Storage initialization completed:")
    logger.info(f"  Successful: {successful_components}")
    if failed_components:
        logger.warning(f"  Failed: {failed_components}")
    
    return results


def create_storage_summary() -> Dict[str, Any]:
    """Create a summary of storage system status."""
    try:
        initializer = StorageInitializer()
        results = initializer.initialize_all_storage()
        status = initializer.get_initialization_status()
        
        return {
            "initialization_results": results,
            "current_status": status,
            "ready_for_guidance": all(
                data.get("status") in ["success", "unavailable"] 
                for data in results.values() 
                if isinstance(data, dict)
            )
        }
    except Exception as e:
        logger.error(f"Failed to create storage summary: {e}")
        return {
            "error": str(e),
            "ready_for_guidance": False
        }


# Global storage initializer instance
_storage_initializer = None

def get_storage_initializer() -> StorageInitializer:
    """Get the global storage initializer instance."""
    global _storage_initializer
    if _storage_initializer is None:
        _storage_initializer = StorageInitializer()
    return _storage_initializer 