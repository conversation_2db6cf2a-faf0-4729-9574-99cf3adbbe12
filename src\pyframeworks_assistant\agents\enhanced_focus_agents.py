"""
Enhanced Focus Agents System for Framework-Specific Learning
Implements curriculum-based learning with improved focus and context management
Based on 2025 best practices for agent focus and learning effectiveness
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from enum import Enum
import logging

from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, SystemMessage
from langchain_core.prompts import ChatPromptTemplate, PromptTemplate
from langchain.schema import ChatMessage
from pydantic import BaseModel, Field

from ..core.models import llm_manager
from ..core.constellation import ConstellationState, SupportedFrameworks
from ..config.settings import settings

logger = logging.getLogger(__name__)


class LearningPhase(str, Enum):
    """Learning phases for progressive education."""
    FOUNDATION = "foundation"
    CORE_CONCEPTS = "core_concepts"
    PRACTICAL_APPLICATION = "practical_application"
    ADVANCED_PATTERNS = "advanced_patterns"
    MASTERY = "mastery"


class CurriculumTopic(BaseModel):
    """Individual topic in the learning curriculum."""
    id: str
    title: str
    phase: LearningPhase
    prerequisites: List[str] = []
    concepts: List[str] = []
    practical_exercises: List[str] = []
    estimated_time_minutes: int = 30
    difficulty_level: int = Field(1, ge=1, le=5)
    mastery_criteria: List[str] = []


class LearningProgress(BaseModel):
    """Track user's learning progress."""
    framework: SupportedFrameworks
    current_phase: LearningPhase
    current_topic_id: Optional[str] = None
    completed_topics: List[str] = []
    mastered_concepts: List[str] = []
    struggle_areas: List[str] = []
    learning_velocity: float = 0.5
    engagement_level: float = 0.5
    last_activity: datetime = Field(default_factory=datetime.now)
    total_study_time_minutes: int = 0


class FocusContext(BaseModel):
    """Enhanced context for maintaining focus."""
    target_framework: SupportedFrameworks
    learning_objective: str
    current_session_goal: str
    allowed_deviations: List[str] = []
    focus_score: float = 1.0
    distraction_count: int = 0
    last_relevant_response: datetime = Field(default_factory=datetime.now)


# Framework-specific curricula
LANGCHAIN_CURRICULUM = {
    "foundation": [
        CurriculumTopic(
            id="langchain_intro",
            title="What is LangChain and Why Use It?",
            phase=LearningPhase.FOUNDATION,
            concepts=[
                "LangChain definition", "LLM orchestration", "Chain concept", 
                "Agent concept", "Tool integration", "Memory management"
            ],
            practical_exercises=[
                "Simple LLM call", "Basic chain creation", "Tool integration demo"
            ],
            difficulty_level=1,
            mastery_criteria=[
                "Can explain LangChain's purpose",
                "Understands chain vs agent difference",
                "Can identify use cases for LangChain"
            ]
        ),
        CurriculumTopic(
            id="langchain_setup",
            title="Setting Up LangChain Environment",
            phase=LearningPhase.FOUNDATION,
            prerequisites=["langchain_intro"],
            concepts=[
                "Installation", "API keys", "Environment setup", "First imports"
            ],
            practical_exercises=[
                "Install LangChain", "Configure OpenAI key", "Run hello world"
            ],
            difficulty_level=1,
            mastery_criteria=[
                "Has working LangChain installation",
                "Can run basic examples",
                "Understands environment configuration"
            ]
        )
    ],
    "core_concepts": [
        CurriculumTopic(
            id="langchain_chains",
            title="Understanding and Building Chains",
            phase=LearningPhase.CORE_CONCEPTS,
            prerequisites=["langchain_setup"],
            concepts=[
                "Chain types", "Sequential chains", "LCEL syntax", 
                "Input/output handling", "Chain composition"
            ],
            practical_exercises=[
                "Build simple chain", "Create sequential chain", "Use LCEL"
            ],
            difficulty_level=2,
            mastery_criteria=[
                "Can build different chain types",
                "Understands LCEL syntax",
                "Can compose complex chains"
            ]
        ),
        CurriculumTopic(
            id="langchain_prompts",
            title="Prompt Templates and Engineering",
            phase=LearningPhase.CORE_CONCEPTS,
            prerequisites=["langchain_chains"],
            concepts=[
                "PromptTemplate", "ChatPromptTemplate", "Few-shot prompts",
                "Template variables", "Prompt engineering best practices"
            ],
            practical_exercises=[
                "Create prompt templates", "Build few-shot examples", "Engineer effective prompts"
            ],
            difficulty_level=2,
            mastery_criteria=[
                "Can create effective prompt templates",
                "Understands few-shot learning",
                "Can engineer prompts for specific tasks"
            ]
        )
    ],
    "practical_application": [
        CurriculumTopic(
            id="langchain_memory",
            title="Memory and Context Management",
            phase=LearningPhase.PRACTICAL_APPLICATION,
            prerequisites=["langchain_prompts"],
            concepts=[
                "ConversationBufferMemory", "ConversationSummaryMemory",
                "Vector store memory", "Custom memory types"
            ],
            practical_exercises=[
                "Implement conversation memory", "Build RAG system", "Create custom memory"
            ],
            difficulty_level=3,
            mastery_criteria=[
                "Can implement different memory types",
                "Understands when to use each memory type",
                "Can build RAG applications"
            ]
        )
    ]
}


class EnhancedFocusAgent:
    """Enhanced agent with curriculum-based learning and focus management."""
    
    def __init__(self, framework: SupportedFrameworks):
        self.framework = framework
        self.curriculum = self._load_curriculum()
        self.focus_context = FocusContext(
            target_framework=framework,
            learning_objective="Learn framework systematically",
            current_session_goal="Complete current topic"
        )
        
    def _load_curriculum(self) -> Dict[str, List[CurriculumTopic]]:
        """Load framework-specific curriculum."""
        if self.framework == SupportedFrameworks.LANGCHAIN:
            return LANGCHAIN_CURRICULUM
        # Add other frameworks as needed
        return {}
        
    def get_current_topic(self, progress: LearningProgress) -> Optional[CurriculumTopic]:
        """Get the current topic based on learning progress."""
        if progress.current_topic_id:
            # Find the current topic
            for phase_topics in self.curriculum.values():
                for topic in phase_topics:
                    if topic.id == progress.current_topic_id:
                        return topic
        
        # Find next topic to learn
        return self._find_next_topic(progress)
    
    def _find_next_topic(self, progress: LearningProgress) -> Optional[CurriculumTopic]:
        """Find the next appropriate topic based on prerequisites."""
        phase_order = [
            LearningPhase.FOUNDATION,
            LearningPhase.CORE_CONCEPTS,
            LearningPhase.PRACTICAL_APPLICATION,
            LearningPhase.ADVANCED_PATTERNS,
            LearningPhase.MASTERY
        ]
        
        for phase in phase_order:
            if phase.value in self.curriculum:
                for topic in self.curriculum[phase.value]:
                    # Check if topic is not completed and prerequisites are met
                    if (topic.id not in progress.completed_topics and
                        all(prereq in progress.completed_topics for prereq in topic.prerequisites)):
                        return topic
        
        return None
    
    def generate_focused_response(self, user_message: str, progress: LearningProgress) -> str:
        """Generate a response that maintains focus on the current learning objective."""
        current_topic = self.get_current_topic(progress)
        
        if not current_topic:
            return self._generate_framework_overview()
        
        # Check if user message is relevant to current topic
        relevance_score = self._assess_relevance(user_message, current_topic)
        
        if relevance_score < 0.3:  # Low relevance
            return self._redirect_to_topic(user_message, current_topic)
        
        # Generate topic-focused response
        return self._generate_topic_response(user_message, current_topic, progress)
    
    def _assess_relevance(self, message: str, topic: CurriculumTopic) -> float:
        """Assess how relevant the user message is to the current topic."""
        message_lower = message.lower()
        
        # Check for topic keywords
        topic_keywords = (
            topic.concepts + 
            [topic.title.lower()] + 
            [self.framework.value]
        )
        
        relevance_count = sum(1 for keyword in topic_keywords if keyword.lower() in message_lower)
        return min(relevance_count / len(topic_keywords), 1.0)
    
    def _redirect_to_topic(self, user_message: str, topic: CurriculumTopic) -> str:
        """Gently redirect user back to the current topic."""
        return f"""I understand you're interested in that, but let's stay focused on our current learning objective: **{topic.title}**.

This is important because {topic.title.lower()} is a fundamental concept in {self.framework.value} that you'll need before moving to more advanced topics.

Here's what we're covering in this topic:
{chr(10).join([f"• {concept}" for concept in topic.concepts[:3]])}

Let me help you with {topic.title.lower()}. {self._generate_topic_question(topic)}"""
    
    def _generate_topic_question(self, topic: CurriculumTopic) -> str:
        """Generate an engaging question about the current topic."""
        questions = {
            "langchain_intro": "What would you like to build with LangChain? A chatbot, a document analyzer, or something else?",
            "langchain_setup": "Do you have Python and pip installed on your system?",
            "langchain_chains": "Have you worked with function composition or pipelines before in programming?",
            "langchain_prompts": "What kind of task would you like to teach an AI to do well?",
            "langchain_memory": "How do you think a chatbot should remember previous conversations?"
        }
        return questions.get(topic.id, f"What would you like to learn about {topic.title.lower()}?")
    
    def _generate_framework_overview(self) -> str:
        """Generate an overview when no specific topic is set."""
        return f"""Welcome to your {self.framework.value} learning journey! 

I'm here to guide you through learning {self.framework.value} step by step, ensuring you build a solid foundation before moving to advanced concepts.

We'll start with the basics and progress through:
1. **Foundation** - Understanding what {self.framework.value} is and setting it up
2. **Core Concepts** - Learning the key building blocks
3. **Practical Application** - Building real projects
4. **Advanced Patterns** - Mastering complex use cases

Ready to begin? Let's start with: What's your experience level with Python and AI/LLM applications?"""
    
    def _generate_topic_response(self, message: str, topic: CurriculumTopic, progress: LearningProgress) -> str:
        """Generate a focused response for the current topic."""
        
        # This would use the LLM with a focused prompt
        system_prompt = f"""You are a {self.framework.value} expert teacher focused on teaching "{topic.title}".

CURRENT LEARNING CONTEXT:
- Topic: {topic.title}
- Phase: {topic.phase}
- Key Concepts: {', '.join(topic.concepts)}
- Difficulty Level: {topic.difficulty_level}/5

TEACHING GUIDELINES:
1. Stay strictly focused on "{topic.title}" - do not deviate to other topics
2. Build on these specific concepts: {', '.join(topic.concepts)}
3. Use practical {self.framework.value} examples only
4. Keep explanations at difficulty level {topic.difficulty_level}/5
5. Always include a specific next step or practice exercise
6. Reference {self.framework.value} documentation when helpful

STUDENT'S QUESTION: {message}

Provide a focused, educational response that advances their understanding of "{topic.title}" specifically."""

        # Here you would call the LLM with this focused prompt
        # For now, returning a template response
        return f"""Great question about {topic.title}! 

Let me explain this key concept in {self.framework.value}: {topic.concepts[0] if topic.concepts else topic.title}

[Detailed explanation would go here based on LLM response]

**Practice Exercise:** {topic.practical_exercises[0] if topic.practical_exercises else f"Try implementing {topic.title.lower()}"}

**Next Step:** Once you've tried this, we'll move on to {topic.concepts[1] if len(topic.concepts) > 1 else "the next concept"}.

Do you have any specific questions about {topic.title.lower()}?"""

    def update_progress(self, progress: LearningProgress, user_interaction: str, response_quality: float):
        """Update learning progress based on interaction."""
        progress.last_activity = datetime.now()
        
        # Update engagement based on response quality
        progress.engagement_level = (progress.engagement_level * 0.8) + (response_quality * 0.2)
        
        # Check if current topic should be marked as completed
        current_topic = self.get_current_topic(progress)
        if current_topic and self._assess_topic_completion(user_interaction, current_topic):
            if current_topic.id not in progress.completed_topics:
                progress.completed_topics.append(current_topic.id)
                progress.mastered_concepts.extend(current_topic.concepts)
                progress.current_topic_id = None  # Move to next topic
    
    def _assess_topic_completion(self, interaction: str, topic: CurriculumTopic) -> bool:
        """Assess if the user has mastered the current topic."""
        # Simple heuristic - in practice, this would be more sophisticated
        interaction_lower = interaction.lower()
        
        # Check for completion indicators
        completion_indicators = [
            "i understand", "got it", "makes sense", "clear now",
            "i can", "let's move on", "next topic", "ready for more"
        ]
        
        return any(indicator in interaction_lower for indicator in completion_indicators)


# Enhanced system with better memory and storage
class FocusedLearningMemory:
    """Enhanced memory system for focused learning."""
    
    def __init__(self):
        self.user_progress: Dict[str, LearningProgress] = {}
        self.session_contexts: Dict[str, FocusContext] = {}
    
    def get_user_progress(self, user_id: str, framework: SupportedFrameworks) -> LearningProgress:
        """Get or create user progress for a framework."""
        key = f"{user_id}_{framework.value}"
        if key not in self.user_progress:
            self.user_progress[key] = LearningProgress(
                framework=framework,
                current_phase=LearningPhase.FOUNDATION
            )
        return self.user_progress[key]
    
    def update_user_progress(self, user_id: str, progress: LearningProgress):
        """Update user progress."""
        key = f"{user_id}_{progress.framework.value}"
        self.user_progress[key] = progress
    
    def get_focus_context(self, session_id: str) -> Optional[FocusContext]:
        """Get focus context for a session."""
        return self.session_contexts.get(session_id)
    
    def set_focus_context(self, session_id: str, context: FocusContext):
        """Set focus context for a session."""
        self.session_contexts[session_id] = context


# Global instances
focused_learning_memory = FocusedLearningMemory()


class CurriculumInstructorAgent:
    """Instructor agent with curriculum-based teaching."""
    
    def __init__(self):
        self.memory = focused_learning_memory
    
    async def handle_learning_request(
        self, 
        user_id: str, 
        session_id: str, 
        message: str, 
        framework: SupportedFrameworks
    ) -> str:
        """Handle a learning request with curriculum focus."""
        
        # Get user progress and focus context
        progress = self.memory.get_user_progress(user_id, framework)
        focus_agent = EnhancedFocusAgent(framework)
        
        # Generate focused response
        response = focus_agent.generate_focused_response(message, progress)
        
        # Update progress (in practice, you'd assess response quality)
        focus_agent.update_progress(progress, message, 0.7)  # Mock quality score
        self.memory.update_user_progress(user_id, progress)
        
        return response
    
    def get_learning_dashboard(self, user_id: str, framework: SupportedFrameworks) -> Dict[str, Any]:
        """Get learning progress dashboard."""
        progress = self.memory.get_user_progress(user_id, framework)
        focus_agent = EnhancedFocusAgent(framework)
        current_topic = focus_agent.get_current_topic(progress)
        
        return {
            "framework": framework.value,
            "current_phase": progress.current_phase,
            "current_topic": current_topic.title if current_topic else None,
            "progress_percentage": len(progress.completed_topics) / max(len(focus_agent.curriculum), 1) * 100,
            "completed_topics": len(progress.completed_topics),
            "total_topics": sum(len(topics) for topics in focus_agent.curriculum.values()),
            "engagement_level": progress.engagement_level,
            "study_time_hours": progress.total_study_time_minutes / 60,
            "next_milestone": self._get_next_milestone(progress, focus_agent)
        }
    
    def _get_next_milestone(self, progress: LearningProgress, focus_agent: EnhancedFocusAgent) -> str:
        """Get the next learning milestone."""
        if progress.current_phase == LearningPhase.FOUNDATION:
            return "Complete foundation concepts"
        elif progress.current_phase == LearningPhase.CORE_CONCEPTS:
            return "Master core framework concepts"
        elif progress.current_phase == LearningPhase.PRACTICAL_APPLICATION:
            return "Build first real application"
        else:
            return "Achieve framework mastery" 