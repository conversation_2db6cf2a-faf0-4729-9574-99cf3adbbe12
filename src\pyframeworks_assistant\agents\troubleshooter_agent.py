"""
Troubleshooter Agent - Specializes in debugging and problem-solving.
"""

from typing import Optional
from langchain_core.messages import HumanMessage
from langchain_core.language_models.chat_models import BaseChatModel

from .base_agent import BaseAgent
from ..core.constellation import ConstellationState, AgentRole


class TroubleshooterAgent(BaseAgent):
    """Specializes in debugging and problem-solving."""
    
    def __init__(self, model: Optional[BaseChatModel] = None):
        super().__init__(AgentRole.TROUBLESHOOTER, model, "debugging")
    
    def get_system_prompt(self, state: ConstellationState) -> str:
        return f"""You are a troubleshooting expert for {state.framework.value} issues.

Current Context:
- Framework: {state.framework.value}
- Module: {state.module_id}
- Troubleshooting Focus: Error resolution and debugging

Your role is to:
1. Diagnose problems systematically
2. Provide step-by-step debugging guidance
3. Explain error messages and their causes
4. Suggest multiple solution approaches
5. Teach debugging techniques and best practices
6. Help prevent similar issues in the future

Be methodical and patient in problem-solving. Help users develop their own debugging skills.

Respond as the troubleshooter with systematic, solution-focused guidance."""
    
    def should_activate(self, state: ConstellationState) -> bool:
        if not state.messages:
            return False
        
        latest_message = state.messages[-1]
        if isinstance(latest_message, HumanMessage):
            content_lower = latest_message.content.lower()
            trouble_keywords = ["error", "bug", "problem", "issue", "debug", "fix", "troubleshoot", "broken"]
            return any(keyword in content_lower for keyword in trouble_keywords)
        
        return False 