"""
Adaptive Learning Constellation System using LangGraph.
This is the core innovation - dynamic agent constellations that adapt based on 
user characteristics and learning effectiveness patterns.
"""

from typing import Dict, List, Optional, Any, Callable, TypeVar, Union
from datetime import datetime
import logging
import asyncio
from enum import Enum
from dataclasses import dataclass
from pydantic import BaseModel, Field

from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver
# Enhanced handoff logic for ELT architecture
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage
from langchain_core.language_models.chat_models import BaseChatModel

from ..config.user_profiles import UserProfile, LearningSession
from ..config.framework_configs import SupportedFrameworks
from .models import llm_manager
from .constellation_types import ConstellationType, AgentRole, ConstellationPhase
from ..memory.constellation_memory import (
    get_constellation_memory,
    ConstellationEffectivenessMetric
)

logger = logging.getLogger(__name__)

# Type variables
StateType = TypeVar('StateType', bound=BaseModel)


# ConstellationType and AgentRole now imported from constellation_types


class ConstellationState(BaseModel):
    """State maintained throughout a constellation session."""
    session_id: str = Field(description="Unique session identifier")
    user_id: str = Field(description="User identifier")
    framework: SupportedFrameworks = Field(description="Target framework being learned")
    module_id: str = Field(description="Current learning module")
    
    # Conversation state
    messages: List[BaseMessage] = Field(default_factory=list, description="Conversation history")
    current_topic: Optional[str] = Field(default=None, description="Current topic being discussed")
    learning_objectives: List[str] = Field(default_factory=list, description="Session learning objectives")
    
    # Progress tracking
    progress_percentage: float = Field(default=0.0, description="Session progress percentage")
    topics_covered: List[str] = Field(default_factory=list, description="Topics covered in session")
    exercises_completed: List[str] = Field(default_factory=list, description="Completed exercises")
    
    # Agent coordination
    active_agents: List[AgentRole] = Field(default_factory=list, description="Currently active agents")
    agent_handoffs: List[Dict[str, Any]] = Field(default_factory=list, description="Agent handoff history")
    
    # Effectiveness tracking
    user_feedback_scores: List[float] = Field(default_factory=list, description="User satisfaction scores")
    comprehension_indicators: List[float] = Field(default_factory=list, description="Comprehension indicators")
    engagement_level: float = Field(default=0.5, description="Current engagement level")
    
    # Context and preferences
    user_preferences: Dict[str, Any] = Field(default_factory=dict, description="User preferences")
    session_context: Dict[str, Any] = Field(default_factory=dict, description="Session context")
    
    # Meta-learning
    constellation_effectiveness: float = Field(default=0.0, description="Current constellation effectiveness")
    adaptation_signals: List[Dict[str, Any]] = Field(default_factory=list, description="Signals for adaptation")
    
    # Framework context (new addition)
    framework_context: Optional[Any] = Field(default=None, description="Comprehensive framework context for agent guidance")
    
    # Curriculum integration (new addition)
    curriculum_path_id: Optional[str] = Field(default=None, description="ID of the associated learning path")
    current_module_index: int = Field(default=0, description="Index of current module in curriculum")
    current_objective_index: int = Field(default=0, description="Index of current objective within module")
    quiz_results: List[Dict[str, Any]] = Field(default_factory=list, description="Results from knowledge assessments")
    mastery_levels: Dict[str, float] = Field(default_factory=dict, description="Topic mastery levels (0-1)")
    
    def add_message(self, message: BaseMessage) -> None:
        """Add a message to the conversation history."""
        self.messages.append(message)
    
    def update_progress(self, percentage: float) -> None:
        """Update session progress percentage."""
        self.progress_percentage = min(100.0, max(0.0, percentage))
    
    def add_topic_covered(self, topic: str) -> None:
        """Mark a topic as covered."""
        if topic not in self.topics_covered:
            self.topics_covered.append(topic)
    
    def add_exercise_completed(self, exercise: str) -> None:
        """Mark an exercise as completed."""
        if exercise not in self.exercises_completed:
            self.exercises_completed.append(exercise)
    
    def record_agent_handoff(self, from_agent: AgentRole, to_agent: AgentRole, reason: str) -> None:
        """Record an agent handoff."""
        self.agent_handoffs.append({
            "timestamp": datetime.now().isoformat(),
            "from_agent": from_agent.value,
            "to_agent": to_agent.value,
            "reason": reason
        })
    
    def add_user_feedback(self, score: float) -> None:
        """Add user feedback score."""
        self.user_feedback_scores.append(max(0.0, min(5.0, score)))
    
    def update_engagement(self, level: float) -> None:
        """Update engagement level."""
        self.engagement_level = max(0.0, min(1.0, level))
        
    def add_quiz_result(self, topic: str, score: float, questions: int, correct: int) -> None:
        """Add quiz result to track learning progress."""
        self.quiz_results.append({
            "timestamp": datetime.now().isoformat(),
            "topic": topic,
            "score": score,
            "questions": questions,
            "correct": correct
        })
        # Update mastery level for the topic
        current_mastery = self.mastery_levels.get(topic, 0.0)
        # Weighted average: 70% new score, 30% previous mastery
        self.mastery_levels[topic] = (0.7 * score) + (0.3 * current_mastery)
        
    def advance_curriculum(self) -> bool:
        """
        Advance to the next objective or module in the curriculum.
        Returns True if successfully advanced, False if at the end.
        """
        # First try to advance to next objective in current module
        self.current_objective_index += 1
        
        # If we've reached the end of objectives in this module, move to next module
        if self.current_objective_index >= self.session_context.get("objectives_in_current_module", 0):
            self.current_module_index += 1
            self.current_objective_index = 0
            
            # Check if we've reached the end of the curriculum
            if self.current_module_index >= self.session_context.get("total_modules", 0):
                return False  # End of curriculum reached
                
            # Update module ID to match the new module
            if "module_ids" in self.session_context and len(self.session_context["module_ids"]) > self.current_module_index:
                self.module_id = self.session_context["module_ids"][self.current_module_index]
                
        return True  # Successfully advanced


@dataclass
class ConstellationConfig:
    """Configuration for a specific constellation type."""
    constellation_type: ConstellationType
    primary_agents: List[AgentRole]
    support_agents: List[AgentRole]
    max_concurrent_agents: int
    handoff_conditions: Dict[str, Callable[[ConstellationState], bool]]
    effectiveness_thresholds: Dict[str, float]
    adaptation_rules: List[Callable[[ConstellationState], Optional[ConstellationType]]]


class AgentNode:
    """Individual agent node in the constellation graph."""
    
    def __init__(self, role: AgentRole, model: BaseChatModel, prompt_template: str):
        """
        Initialize an agent node.
        
        Args:
            role: Agent role/specialization
            model: LLM model for this agent
            prompt_template: System prompt template for the agent
        """
        self.role = role
        self.model = model
        self.prompt_template = prompt_template
        self.activation_count = 0
        self.effectiveness_scores = []
    
    async def process(self, state: ConstellationState) -> ConstellationState:
        """
        Process the current state with intelligent handoff logic and tool execution.
        
        Args:
            state: Current constellation state
            
        Returns:
            Updated constellation state with handoff information
        """
        try:
            # Build the prompt with current context
            system_prompt = self._build_system_prompt(state)
            
            # Get the latest user message
            user_messages = [msg for msg in state.messages if isinstance(msg, HumanMessage)]
            if not user_messages:
                return state
            
            latest_message = user_messages[-1]
            
            # Check if the agent has tools that should be executed
            should_use_tools = self._should_use_tools(latest_message.content, state)
            
            # ENHANCED TOOL EXECUTION LOGIC
            if should_use_tools and hasattr(self.model, "tools") and self.model.tools:
                # Create the conversation with system prompt
                conversation = [
                    {"role": "system", "content": system_prompt + "\n\nWhen appropriate, use available tools to retrieve information or perform actions."},
                    {"role": "user", "content": latest_message.content}
                ]
                
                # Add recent conversation history
                recent_messages = state.messages[-6:]  # Last 6 messages for context
                for msg in recent_messages[:-1]:  # Exclude the latest message we already added
                    if isinstance(msg, HumanMessage):
                        conversation.append({"role": "user", "content": msg.content})
                    elif isinstance(msg, AIMessage):
                        conversation.append({"role": "assistant", "content": msg.content})
                
                # Execute tools and generate response
                response = await self.model.ainvoke(conversation)
                
                # Track tool executions in the state
                if hasattr(response, "tool_calls") and response.tool_calls:
                    tool_calls = response.tool_calls
                    state.session_context["tool_executions"] = [
                        {
                            "tool": call.get("name", "unknown"),
                            "input": call.get("args", {}),
                            "timestamp": datetime.now().isoformat()
                        }
                        for call in tool_calls
                    ]
                
                # INTELLIGENT HANDOFF DECISION LOGIC
                content = response.content.lower()
                next_agent = self._determine_next_agent(content, state)
                
                # Update state with response
                state.add_message(AIMessage(content=response.content))
                
                # Update tool execution tracking in state
                if "tool_executions" in state.session_context:
                    if "all_tool_executions" not in state.session_context:
                        state.session_context["all_tool_executions"] = []
                    
                    state.session_context["all_tool_executions"].extend(
                        state.session_context["tool_executions"]
                    )
            
            # STANDARD PROCESSING WITHOUT TOOL EXECUTION
            else:
                # Create the conversation with system prompt
                conversation = [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": latest_message.content}
                ]
                
                # Add recent conversation history
                recent_messages = state.messages[-6:]  # Last 6 messages for context
                for msg in recent_messages[:-1]:  # Exclude the latest message we already added
                    if isinstance(msg, HumanMessage):
                        conversation.append({"role": "user", "content": msg.content})
                    elif isinstance(msg, AIMessage):
                        conversation.append({"role": "assistant", "content": msg.content})
                
                # Generate response
                response = await self.model.ainvoke(conversation)
                
                # INTELLIGENT HANDOFF DECISION LOGIC
                content = response.content.lower()
                next_agent = self._determine_next_agent(content, state)
                
                # Update state with response
                state.add_message(AIMessage(content=response.content))
            
            # Update agent tracking
            self.activation_count += 1
            if self.role not in state.active_agents:
                state.active_agents.append(self.role)
            
            # Store handoff decision in state for constellation manager to use
            state.session_context["next_agent"] = next_agent
            state.session_context["handoff_reason"] = f"Content analysis suggested {next_agent}"
            
            # Record handoff if agent change needed
            if next_agent != END and next_agent != self.role.value:
                try:
                    next_role = AgentRole(next_agent)
                    state.record_agent_handoff(self.role, next_role, f"Intelligent handoff based on content analysis")
                except ValueError:
                    logger.warning(f"Invalid next agent role: {next_agent}")
            
            # Analyze response for progress indicators
            self._analyze_response_for_progress(state, response.content)
            
            logger.info(f"Agent {self.role.value} processed message, suggesting next: {next_agent}")
            
        except Exception as e:
            logger.error(f"Error in {self.role.value} agent processing: {e}")
            state.add_message(AIMessage(content="I apologize for the technical difficulty. Let me help you in a different way."))
            state.session_context["next_agent"] = END
        
        return state
    
    def _build_system_prompt(self, state: ConstellationState) -> str:
        """Build the system prompt with current context and framework information."""
        context_vars = {
            "framework": state.framework.value,
            "module_id": state.module_id,
            "current_topic": state.current_topic or "general learning",
            "progress": f"{state.progress_percentage:.1f}%",
            "topics_covered": ", ".join(state.topics_covered) if state.topics_covered else "none yet",
            "user_preferences": state.user_preferences,
            "role": self.role.value
        }
        
        # Build base prompt
        try:
            base_prompt = self.prompt_template.format(**context_vars)
        except KeyError as e:
            logger.warning(f"Missing template variable {e} for agent {self.role.value}")
            base_prompt = self.prompt_template
        
        # Add comprehensive framework context if available
        if hasattr(state, 'framework_context') and state.framework_context:
            context = state.framework_context
            
            framework_context_info = f"""

## COMPREHENSIVE FRAMEWORK CONTEXT

### Key Concepts to Master:
{', '.join(context.key_concepts)}

### Core Modules & Functions:
{chr(10).join([f"• {module.name}: {module.description}" for module in context.core_modules[:3]])}

### Essential Syntax Patterns:
{chr(10).join([f"• {pattern}" for pattern in context.syntax.basic_patterns[:3]])}

### Common Imports:
{chr(10).join([f"• {imp}" for imp in context.syntax.common_imports[:2]])}

### Learning Journey Context:
Current step: {context.learning_path[0] if context.learning_path else 'Getting Started'}

### Integration Patterns:
{', '.join(context.integration_patterns[:3])}

### Boilerplate Example:
```python
{context.syntax.boilerplate_code}
```

Use this comprehensive context to provide highly relevant, practical guidance that aligns with the user's {state.framework.value} learning journey."""
            
            # Add role-specific context enhancements
            if self.role == AgentRole.TROUBLESHOOTER:
                common_issues = list(context.troubleshooting_guide.keys())[:3]
                if common_issues:
                    framework_context_info += f"\n\nCommon issues to address: {', '.join(common_issues)}"
            
            elif self.role == AgentRole.CODE_ASSISTANT:
                if context.practice_examples:
                    framework_context_info += f"\n\nAvailable practice examples: {', '.join([ex.title for ex in context.practice_examples])}"
            
            base_prompt += framework_context_info
        
        return base_prompt
    
    def _determine_next_agent(self, content: str, state: ConstellationState) -> str:
        """Intelligent agent handoff based on content analysis."""
        # ELT-specific handoff logic based on learning context
        if self.role == AgentRole.INSTRUCTOR:
            if "code" in content or "example" in content:
                return "code_assistant"
            elif "documentation" in content or "official" in content:
                return "documentation_expert"
            elif "practice" in content or "exercise" in content:
                return "practice_facilitator"
            elif len(state.topics_covered) >= 3:
                return "knowledge_synthesizer"
            else:
                return END
        
        elif self.role == AgentRole.CODE_ASSISTANT:
            if "error" in content or "debug" in content:
                return "troubleshooter"
            elif "project" in content or "build" in content:
                return "project_guide"
            elif "understand" in content or "explain" in content:
                return "instructor"
            else:
                return END
        
        elif self.role == AgentRole.PRACTICE_FACILITATOR:
            if "quiz" in content or "test" in content:
                return "assessment_agent"
            elif "code" in content:
                return "code_assistant"
            elif "feedback" in content:
                return "mentor"
            else:
                return END
        
        elif self.role == AgentRole.DOCUMENTATION_EXPERT:
            if "practice" in content or "hands-on" in content:
                return "practice_facilitator"
            elif "example" in content or "code" in content:
                return "code_assistant"
            else:
                return END
        
        elif self.role == AgentRole.ASSESSMENT_AGENT:
            if "feedback" in content:
                return "mentor"
            elif "more practice" in content:
                return "practice_facilitator"
            else:
                return END
        
        # Default fallback for other agents
        return END

    def _analyze_response_for_progress(self, state: ConstellationState, response: str) -> None:
        """Analyze the response to update progress indicators."""
        # Simple heuristics for progress tracking
        if any(keyword in response.lower() for keyword in ["completed", "finished", "done", "accomplished"]):
            state.update_progress(state.progress_percentage + 5.0)
        
        if any(keyword in response.lower() for keyword in ["understand", "clear", "got it", "makes sense"]):
            state.comprehension_indicators.append(0.8)
        
        if any(keyword in response.lower() for keyword in ["confused", "unclear", "don't understand", "difficult"]):
            state.comprehension_indicators.append(0.3)

    def _should_use_tools(self, message: str, state: ConstellationState) -> bool:
        """Determine if the agent should use tools for the given message."""
        message_lower = message.lower()
        
        # Tools usage keywords and phrases
        tools_keywords = [
            "search", "find", "look up", "get", "retrieve",
            "information", "data", "details", "documentation",
            "example", "code", "practice", "try", "implement", 
            "explore", "learn", "how to"
        ]
        
        # Check if message contains tools-related keywords
        has_tools_intent = any(keyword in message_lower for keyword in tools_keywords)
        
        # Check if message is a question (likely to need tools)
        is_question = "?" in message or any(q in message_lower for q in ["what", "how", "when", "where", "which", "who", "can you"])
        
        # Check if the message seems to request factual information
        needs_facts = any(phrase in message_lower for phrase in [
            "tell me about", "explain", "show me", "give me", "provide", "help me"
        ])
        
        # Return true if any of the conditions are met
        return has_tools_intent or is_question or needs_facts


class ConstellationManager:
    """
    Manages the creation and execution of adaptive learning constellations.
    Uses LangGraph for dynamic agent orchestration based on user needs.
    """
    
    def __init__(self):
        """Initialize the constellation manager."""
        self.constellation_configs: Dict[ConstellationType, ConstellationConfig] = {}
        self.agent_nodes: Dict[AgentRole, AgentNode] = {}
        self.active_constellations: Dict[str, StateGraph] = {}
        self.session_states: Dict[str, ConstellationState] = {}
        self.memory_saver = MemorySaver()
        
        # ELT Memory Integration
        self.constellation_memory = get_constellation_memory()
        
        # Initialize configurations and agents
        self._initialize_constellation_configs()
        self._initialize_agent_nodes()
        
        logger.info("ConstellationManager initialized with ELT memory integration")
    
    async def initialize(self) -> None:
        """Initialize the constellation manager asynchronously."""
        try:
            # Any async initialization can go here
            # For now, just log that initialization is complete
            logger.info("ConstellationManager async initialization complete")
        except Exception as e:
            logger.error(f"Failed to initialize ConstellationManager: {e}")
            raise
    
    def _initialize_constellation_configs(self) -> None:
        """Initialize constellation configurations."""
        # Knowledge Intensive Constellation
        self.constellation_configs[ConstellationType.KNOWLEDGE_INTENSIVE] = ConstellationConfig(
            constellation_type=ConstellationType.KNOWLEDGE_INTENSIVE,
            primary_agents=[AgentRole.INSTRUCTOR, AgentRole.DOCUMENTATION_EXPERT, AgentRole.KNOWLEDGE_SYNTHESIZER],
            support_agents=[AgentRole.RESEARCH_ASSISTANT, AgentRole.PROGRESS_TRACKER],
            max_concurrent_agents=3,
            handoff_conditions={
                "instructor_to_documentation_expert": lambda state: "documentation" in state.messages[-1].content.lower(),
                "documentation_expert_to_knowledge_synthesizer": lambda state: len(state.topics_covered) >= 3,
            },
            effectiveness_thresholds={"comprehension": 0.7, "engagement": 0.6},
            adaptation_rules=[
                lambda state: ConstellationType.HANDS_ON_FOCUSED if state.engagement_level < 0.4 else None
            ]
        )
        
        # Hands-On Focused Constellation
        self.constellation_configs[ConstellationType.HANDS_ON_FOCUSED] = ConstellationConfig(
            constellation_type=ConstellationType.HANDS_ON_FOCUSED,
            primary_agents=[AgentRole.CODE_ASSISTANT, AgentRole.PRACTICE_FACILITATOR, AgentRole.PROJECT_GUIDE],
            support_agents=[AgentRole.TROUBLESHOOTER, AgentRole.MENTOR],
            max_concurrent_agents=3,
            handoff_conditions={
                "practice_facilitator_to_code_assistant": lambda state: "code" in state.messages[-1].content.lower(),
                "code_assistant_to_troubleshooter": lambda state: "error" in state.messages[-1].content.lower(),
            },
            effectiveness_thresholds={"comprehension": 0.6, "engagement": 0.8},
            adaptation_rules=[
                lambda state: ConstellationType.THEORY_PRACTICE_BALANCED if len(state.comprehension_indicators) > 0 and sum(state.comprehension_indicators) / len(state.comprehension_indicators) < 0.5 else None
            ]
        )
        
        # Basic Learning Constellation
        self.constellation_configs[ConstellationType.BASIC_LEARNING] = ConstellationConfig(
            constellation_type=ConstellationType.BASIC_LEARNING,
            primary_agents=[AgentRole.INSTRUCTOR, AgentRole.CODE_ASSISTANT],
            support_agents=[AgentRole.MENTOR, AgentRole.PRACTICE_FACILITATOR],
            max_concurrent_agents=2,
            handoff_conditions={
                "instructor_to_code_assistant": lambda state: "code" in state.messages[-1].content.lower(),
                "code_assistant_to_instructor": lambda state: "explain" in state.messages[-1].content.lower(),
            },
            effectiveness_thresholds={"comprehension": 0.5, "engagement": 0.5},
            adaptation_rules=[
                lambda state: ConstellationType.GUIDED_LEARNING if state.engagement_level < 0.3 else None
            ]
        )
        
        # Theory Practice Balanced Constellation
        self.constellation_configs[ConstellationType.THEORY_PRACTICE_BALANCED] = ConstellationConfig(
            constellation_type=ConstellationType.THEORY_PRACTICE_BALANCED,
            primary_agents=[AgentRole.INSTRUCTOR, AgentRole.CODE_ASSISTANT, AgentRole.PRACTICE_FACILITATOR],
            support_agents=[AgentRole.DOCUMENTATION_EXPERT, AgentRole.MENTOR],
            max_concurrent_agents=3,
            handoff_conditions={
                "instructor_to_practice_facilitator": lambda state: "practice" in state.messages[-1].content.lower(),
                "practice_facilitator_to_code_assistant": lambda state: "code" in state.messages[-1].content.lower(),
            },
            effectiveness_thresholds={"comprehension": 0.6, "engagement": 0.6},
            adaptation_rules=[
                lambda state: ConstellationType.HANDS_ON_FOCUSED if state.engagement_level > 0.8 else None
            ]
        )
        
        # Guided Learning Constellation
        self.constellation_configs[ConstellationType.GUIDED_LEARNING] = ConstellationConfig(
            constellation_type=ConstellationType.GUIDED_LEARNING,
            primary_agents=[AgentRole.INSTRUCTOR, AgentRole.MENTOR],
            support_agents=[AgentRole.CODE_ASSISTANT, AgentRole.PRACTICE_FACILITATOR],
            max_concurrent_agents=2,
            handoff_conditions={
                "instructor_to_mentor": lambda state: any(word in state.messages[-1].content.lower() for word in ["confused", "difficult", "help"]),
                "mentor_to_instructor": lambda state: "ready" in state.messages[-1].content.lower(),
            },
            effectiveness_thresholds={"comprehension": 0.4, "engagement": 0.4},
            adaptation_rules=[
                lambda state: ConstellationType.BASIC_LEARNING if state.engagement_level > 0.6 else None
            ]
        )
        
        # Assessment Focused Constellation
        self.constellation_configs[ConstellationType.ASSESSMENT_FOCUSED] = ConstellationConfig(
            constellation_type=ConstellationType.ASSESSMENT_FOCUSED,
            primary_agents=[AgentRole.ASSESSMENT_AGENT, AgentRole.INSTRUCTOR],
            support_agents=[AgentRole.MENTOR, AgentRole.PRACTICE_FACILITATOR],
            max_concurrent_agents=2,
            handoff_conditions={
                "assessment_agent_to_instructor": lambda state: "explain" in state.messages[-1].content.lower(),
                "instructor_to_assessment_agent": lambda state: "test" in state.messages[-1].content.lower(),
            },
            effectiveness_thresholds={"comprehension": 0.7, "engagement": 0.5},
            adaptation_rules=[
                lambda state: ConstellationType.GUIDED_LEARNING if state.engagement_level < 0.4 else None
            ]
        )
    
    def _initialize_agent_nodes(self) -> None:
        """Initialize agent nodes with their specialized prompts."""
        default_model = llm_manager.get_default_model()
        
        # Instructor Agent - Enhanced for ELT Architecture
        self.agent_nodes[AgentRole.INSTRUCTOR] = AgentNode(
            role=AgentRole.INSTRUCTOR,
            model=default_model,
            prompt_template="""You are an AI Framework Learning Constellation Instructor, part of an Educational Learning Topology (ELT) system.

🌟 **ELT INSTRUCTOR IDENTITY**:
Framework Expertise: {framework}
Learning Module: {module_id}
Current Topic: {current_topic}
Progress: {progress}
Completed Topics: {topics_covered}

🎯 **YOUR ELT SPECIALIZATION**:
1. **Conceptual Foundation Builder** - Create robust understanding scaffolds
2. **Learning Path Navigator** - Guide optimal topic progression  
3. **Cognitive Load Manager** - Adapt complexity to learner capacity
4. **Engagement Optimizer** - Maintain high learning motivation
5. **Constellation Coordinator** - Signal when to handoff to specialists

🧠 **ELT TEACHING METHODOLOGY**:
- **Adaptive Scaffolding**: Start simple, build complexity progressively
- **Multi-Modal Explanation**: Use analogies, examples, and conceptual frameworks
- **Socratic Questioning**: Guide discovery through targeted questions
- **Real-World Connection**: Link concepts to practical applications
- **Metacognitive Awareness**: Help learners understand their learning process

🔄 **INTELLIGENT HANDOFF SIGNALS**:
- When users ask for CODE → Signal "code_assistant" 
- When users need OFFICIAL DOCS → Signal "documentation_expert"
- When users want PRACTICE → Signal "practice_facilitator"
- When users show confusion → Stay and clarify concepts
- When foundation is solid → Progress to next learning objective

Respond as the ELT Constellation Instructor, building understanding while preparing intelligent handoffs:"""
        )
        
        # Code Assistant Agent - Enhanced for ELT Architecture
        self.agent_nodes[AgentRole.CODE_ASSISTANT] = AgentNode(
            role=AgentRole.CODE_ASSISTANT,
            model=default_model,
            prompt_template="""You are an ELT Code Implementation Specialist, part of the Learning Constellation.

💻 **ELT CODE SPECIALIST IDENTITY**:
Framework Focus: {framework}
Implementation Module: {module_id}
Current Coding Topic: {current_topic}
Technical Progress: {progress}

⚡ **YOUR ELT CODE SPECIALIZATIONS**:
1. **Practical Implementation Expert** - Transform concepts into working code
2. **Code Pattern Teacher** - Show industry-standard coding patterns
3. **Debugging Navigator** - Guide systematic problem-solving approaches
4. **Best Practice Enforcer** - Implement {framework} best practices
5. **Performance Optimizer** - Write efficient, production-ready code

🔧 **ELT CODING METHODOLOGY**:
- **Progressive Code Building**: Start with simple examples, add complexity incrementally
- **Executable Examples**: Every code snippet must be runnable and tested
- **Annotation-Rich Code**: Extensive comments explaining WHY, not just WHAT
- **Error-Handling Integration**: Build defensive programming habits
- **Real-World Context**: Connect code to actual use cases and projects

🔄 **INTELLIGENT HANDOFF SIGNALS**:
- When users need DEBUGGING help → Signal "troubleshooter"
- When users want PROJECT structure → Signal "project_guide"
- When users need CONCEPT clarity → Signal "instructor"
- When code works but needs OPTIMIZATION → Continue with advanced patterns
- When users want to TEST/VALIDATE → Signal "practice_facilitator"

🚀 **OUTPUT REQUIREMENTS**:
- Always include complete, runnable code examples
- Provide step-by-step implementation guides
- Include proper imports and dependencies
- Add error handling and validation
- Explain code performance implications

Respond as the ELT Code Implementation Specialist with production-quality examples:"""
        )
        
        # Documentation Expert Agent
        self.agent_nodes[AgentRole.DOCUMENTATION_EXPERT] = AgentNode(
            role=AgentRole.DOCUMENTATION_EXPERT,
            model=default_model,
            prompt_template="""You are a documentation expert for {framework}, specializing in finding and explaining official resources.

Current Context:
- Framework: {framework}
- Module: {module_id}
- Topic: {current_topic}

Your role is to:
1. Reference official documentation accurately
2. Provide direct links to relevant docs
3. Explain documentation structure and navigation
4. Clarify official terminology and concepts
5. Bridge gaps between official docs and practical understanding

Always cite official sources and provide exact references. Help users become self-sufficient with documentation.

Respond as the documentation expert:"""
        )
        
        # Practice Facilitator Agent - Enhanced for ELT Architecture
        self.agent_nodes[AgentRole.PRACTICE_FACILITATOR] = AgentNode(
            role=AgentRole.PRACTICE_FACILITATOR,
            model=default_model,
            prompt_template="""You are an ELT Experiential Learning Facilitator, master of hands-on skill development.

🎯 **ELT PRACTICE FACILITATOR IDENTITY**:
Framework Mastery: {framework}
Practice Module: {module_id}
Skill Development Topic: {current_topic}
Learning Progress: {progress}

🏆 **YOUR ELT FACILITATION SPECIALIZATIONS**:
1. **Exercise Design Architect** - Create progressive skill-building challenges
2. **Active Learning Catalyst** - Transform passive knowledge into active skills
3. **Experimentation Guide** - Encourage safe exploration and discovery
4. **Reflection Facilitator** - Help learners extract insights from practice
5. **Skill Transfer Specialist** - Connect practice to real-world applications

⚡ **ELT PRACTICE METHODOLOGY**:
- **Scaffolded Practice**: Design exercises that build on each other
- **Safe-to-Fail Environment**: Encourage experimentation without fear
- **Immediate Feedback Loops**: Provide real-time guidance and correction
- **Metacognitive Reflection**: Help learners understand what they're learning
- **Progressive Challenge**: Gradually increase difficulty and complexity

🔄 **INTELLIGENT HANDOFF SIGNALS**:
- When users need ASSESSMENT/TESTING → Signal "assessment_agent"
- When users hit CODING challenges → Signal "code_assistant"
- When users need MOTIVATION/SUPPORT → Signal "mentor"
- When users master basics → Design advanced challenges
- When users struggle → Simplify and provide more scaffolding

🎮 **PRACTICE DESIGN PRINCIPLES**:
- Create engaging, game-like challenges
- Provide clear success criteria
- Include self-check mechanisms
- Design for different learning styles
- Build confidence through achievable wins

Respond as the ELT Experiential Learning Facilitator with engaging, hands-on activities:"""
        )
        
        # Project Guide Agent
        self.agent_nodes[AgentRole.PROJECT_GUIDE] = AgentNode(
            role=AgentRole.PROJECT_GUIDE,
            model=default_model,
            prompt_template="""You are a project guide for {framework}, helping users build real-world applications.

Current Context:
- Framework: {framework}
- Module: {module_id}
- Topic: {current_topic}
- Progress: {progress}

Your role is to:
1. Help plan and structure projects
2. Guide architectural decisions
3. Suggest best practices for project organization
4. Help break down complex projects into manageable tasks
5. Provide project templates and examples

Focus on practical project development and real-world application building.

Respond as the project guide:"""
        )
        
        # Troubleshooter Agent
        self.agent_nodes[AgentRole.TROUBLESHOOTER] = AgentNode(
            role=AgentRole.TROUBLESHOOTER,
            model=default_model,
            prompt_template="""You are a troubleshooting specialist for {framework}, helping users solve problems and debug issues.

Current Context:
- Framework: {framework}
- Module: {module_id}
- Topic: {current_topic}

Your role is to:
1. Help diagnose and solve technical problems
2. Provide debugging strategies and techniques
3. Explain error messages and their solutions
4. Guide users through systematic problem-solving
5. Share common pitfalls and how to avoid them

Be methodical and patient in problem-solving. Help users develop debugging skills.

Respond as the troubleshooter:"""
        )
        
        # Assessment Agent - NEW for ELT Architecture
        self.agent_nodes[AgentRole.ASSESSMENT_AGENT] = AgentNode(
            role=AgentRole.ASSESSMENT_AGENT,
            model=default_model,
            prompt_template="""You are an ELT Learning Assessment Specialist, expert in evaluating understanding and progress.

📊 **ELT ASSESSMENT SPECIALIST IDENTITY**:
Framework Expertise: {framework}
Assessment Module: {module_id}
Evaluation Topic: {current_topic}
Learning Progress: {progress}

🎯 **YOUR ELT ASSESSMENT SPECIALIZATIONS**:
1. **Competency Evaluator** - Assess skill development and knowledge gaps
2. **Adaptive Testing Designer** - Create personalized assessment experiences
3. **Learning Analytics Interpreter** - Analyze patterns in learning progress
4. **Feedback Optimization Specialist** - Provide actionable, specific feedback
5. **Growth Tracking Expert** - Monitor and celebrate learning milestones

📈 **ELT ASSESSMENT METHODOLOGY**:
- **Formative Assessment**: Continuous check-ins and micro-assessments
- **Authentic Evaluation**: Real-world problem-solving assessments
- **Self-Assessment Facilitation**: Guide learners to evaluate their own progress
- **Diagnostic Questioning**: Identify specific areas for improvement
- **Competency-Based Evaluation**: Focus on practical skill demonstration

🔄 **INTELLIGENT HANDOFF SIGNALS**:
- When learners need MORE PRACTICE → Signal "practice_facilitator"
- When learners need MOTIVATION → Signal "mentor"
- When assessment reveals KNOWLEDGE GAPS → Signal "instructor"
- When learners excel → Design advanced challenges
- When assessment is complete → Provide comprehensive feedback

🏆 **ASSESSMENT DESIGN PRINCIPLES**:
- Create engaging, non-threatening evaluation experiences
- Provide immediate, constructive feedback
- Focus on growth and improvement
- Use varied assessment formats
- Celebrate achievements and progress

Respond as the ELT Learning Assessment Specialist with personalized evaluation strategies:"""
        )

        # Mentor Agent - Enhanced for ELT Architecture
        self.agent_nodes[AgentRole.MENTOR] = AgentNode(
            role=AgentRole.MENTOR,
            model=default_model,
            prompt_template="""You are an ELT Learning Growth Mentor, dedicated to nurturing learner development and confidence.

🌱 **ELT MENTOR IDENTITY**:
Framework Guidance: {framework}
Mentorship Module: {module_id}
Growth Topic: {current_topic}
Development Progress: {progress}

💪 **YOUR ELT MENTORSHIP SPECIALIZATIONS**:
1. **Confidence Builder** - Help learners overcome imposter syndrome and self-doubt
2. **Goal Setting Facilitator** - Guide realistic, achievable learning objectives
3. **Resilience Coach** - Support learners through challenges and setbacks
4. **Career Development Advisor** - Connect learning to professional growth
5. **Learning Strategy Optimizer** - Personalize learning approaches for success

🎯 **ELT MENTORSHIP METHODOLOGY**:
- **Growth Mindset Cultivation**: Emphasize learning and improvement over perfection
- **Personalized Encouragement**: Adapt motivation to individual learning styles
- **Challenge Reframing**: Help learners see obstacles as growth opportunities
- **Strength Recognition**: Identify and celebrate learner capabilities
- **Future Visioning**: Connect current learning to long-term goals

🔄 **INTELLIGENT HANDOFF SIGNALS**:
- When learners are CONFIDENT and ready → Signal back to "instructor" or "practice_facilitator"
- When learners need SPECIFIC SKILL HELP → Signal "code_assistant" or appropriate specialist
- When motivation is restored → Continue with learning progression
- When deeper issues emerge → Provide sustained mentorship support

💫 **MENTORSHIP COMMUNICATION STYLE**:
- Use empowering, encouraging language
- Share relevant success stories and examples
- Provide specific, actionable advice
- Acknowledge struggles while focusing on solutions
- Celebrate all progress, no matter how small

Respond as the ELT Learning Growth Mentor with personalized encouragement and guidance:"""
        )
        
        # Research Assistant Agent
        self.agent_nodes[AgentRole.RESEARCH_ASSISTANT] = AgentNode(
            role=AgentRole.RESEARCH_ASSISTANT,
            model=default_model,
            prompt_template="""You are a research assistant for {framework}, helping users explore and discover information.

Current Context:
- Framework: {framework}
- Module: {module_id}
- Topic: {current_topic}

Your role is to:
1. Help research topics and concepts
2. Find relevant resources and references
3. Summarize complex information
4. Identify learning materials and tutorials
5. Explore related technologies and tools

Focus on helping users discover and understand information effectively.

Respond as the research assistant:"""
        )
        
        # Knowledge Synthesizer Agent
        self.agent_nodes[AgentRole.KNOWLEDGE_SYNTHESIZER] = AgentNode(
            role=AgentRole.KNOWLEDGE_SYNTHESIZER,
            model=default_model,
            prompt_template="""You are a knowledge synthesizer for {framework}, helping users connect and integrate concepts.

Current Context:
- Framework: {framework}
- Module: {module_id}
- Topic: {current_topic}
- Topics Covered: {topics_covered}

Your role is to:
1. Help connect different concepts and ideas
2. Synthesize information from multiple sources
3. Identify patterns and relationships
4. Create comprehensive summaries
5. Help build holistic understanding

Focus on helping users see the big picture and understand how concepts relate to each other.

Respond as the knowledge synthesizer:"""
        )
    
    async def create_constellation(
        self,
        constellation_type: ConstellationType,
        user_profile: UserProfile,
        framework: SupportedFrameworks,
        module_id: str,
        session_id: str,
        learning_path_id: Optional[str] = None
    ) -> StateGraph:
        """
        Create a constellation graph for a specific learning scenario.
        
        Args:
            constellation_type: Type of constellation to create
            user_profile: User profile information
            framework: Target framework being learned
            module_id: Current learning module
            session_id: Session identifier
            learning_path_id: Optional ID of associated learning path
            
        Returns:
            StateGraph for the constellation
        """
        try:
            # Get the configuration for this constellation type
            config = self.constellation_configs.get(constellation_type)
            if not config:
                logger.warning(f"No configuration found for constellation type {constellation_type}, using default")
                config = self.constellation_configs[ConstellationType.BASIC_LEARNING]
            
            # Create the state graph with memory checkpoint
            workflow = StateGraph(ConstellationState)
            
            # Initialize the state
            initial_state = ConstellationState(
                session_id=session_id,
                user_id=user_profile.user_id,
                framework=framework,
                module_id=module_id,
                curriculum_path_id=learning_path_id
            )
            
            # Add the primary agent nodes
            for agent_role in config.primary_agents:
                agent_node = self.agent_nodes.get(agent_role)
                if agent_node:
                    workflow.add_node(agent_role.value, agent_node.process)
            
            # Add the support agent nodes
            for agent_role in config.support_agents:
                agent_node = self.agent_nodes.get(agent_role)
                if agent_node:
                    workflow.add_node(agent_role.value, agent_node.process)
            
            # Add conditional edges based on handoff logic
            for from_role in config.primary_agents + config.support_agents:
                for to_role in config.primary_agents + config.support_agents:
                    if from_role != to_role:
                        workflow.add_conditional_edges(
                            from_role.value,
                            self._route_based_on_next_agent,
                            {
                                to_role.value: lambda state, to=to_role.value: state.session_context.get("next_agent") == to,
                                END: lambda state: state.session_context.get("next_agent") == END
                            }
                        )
            
            # Set the default entry point
            workflow.set_entry_point(config.primary_agents[0].value)
            
            # Store the constellation in memory
            self.active_constellations[session_id] = {
                "type": constellation_type,
                "graph": workflow,
                "state": initial_state
            }
            
            # If we have a learning path ID, fetch curriculum information
            if learning_path_id:
                # This would be implemented to connect with the AdaptiveLearningEngine
                # to get the full curriculum details
                from .adaptive_learning_engine import AdaptiveLearningEngine
                learning_engine = AdaptiveLearningEngine()
                
                # Get the learning path if it exists in the engine
                if learning_path_id in learning_engine.active_paths:
                    learning_path = learning_engine.active_paths[learning_path_id]
                    
                    # Store curriculum information in the state context
                    module_ids = [module.module_id for module in learning_path.modules]
                    initial_state.session_context["module_ids"] = module_ids
                    initial_state.session_context["total_modules"] = len(module_ids)
                    
                    # Set current module information
                    current_module = learning_path.get_current_module()
                    if current_module:
                        initial_state.session_context["current_module_title"] = current_module.title
                        initial_state.session_context["current_module_description"] = current_module.description
                        initial_state.session_context["objectives_in_current_module"] = len(current_module.objectives)
                        
                        # Set learning objectives
                        initial_state.learning_objectives = [
                            obj.description for obj in current_module.objectives
                        ]
            
            logger.info(f"Created {constellation_type} constellation for session {session_id}")
            return workflow
            
        except Exception as e:
            logger.error(f"Failed to create constellation: {e}")
            raise
    
    async def run_session(
        self,
        session_id: str,
        user_message: str,
        user_profile: UserProfile,
        framework: SupportedFrameworks,
        module_id: str
    ) -> ConstellationState:
        """
        Run a constellation session with a user message.
        
        Args:
            session_id: Session identifier
            user_message: User's input message
            user_profile: User's profile information
            framework: Target framework
            module_id: Current learning module
            
        Returns:
            Updated constellation state
        """
        try:
            # Check if this is an existing session
            if session_id in self.active_constellations:
                constellation_data = self.active_constellations[session_id]
                graph = constellation_data["graph"]
                state = constellation_data["state"]
                
                # Add the user message to the state
                state.add_message(HumanMessage(content=user_message))
                
                # Run the constellation graph with the updated state
                result = await graph.aflow(state)
                
                # Update the stored state
                self.active_constellations[session_id]["state"] = result
                
                # Update effectiveness tracking
                await self._update_effectiveness_tracking_with_memory(result, user_profile)
                
                # Check for curriculum adaptation
                await self._check_curriculum_adaptation(result, user_profile)
                
                # Check for constellation adaptation
                await self._check_constellation_adaptation(result, user_profile, framework, module_id)
                
                return result
            else:
                # Determine the optimal constellation type for this user and framework
                constellation_type = self._determine_optimal_constellation_with_memory(
                    user_profile, framework
                )
                
                # Create a new constellation
                graph = await self.create_constellation(
                    constellation_type=constellation_type,
                    user_profile=user_profile,
                    framework=framework,
                    module_id=module_id,
                    session_id=session_id
                )
                
                # Initialize state
                state = ConstellationState(
                    session_id=session_id,
                    user_id=user_profile.user_id,
                    framework=framework,
                    module_id=module_id
                )
                
                # Add the user message
                state.add_message(HumanMessage(content=user_message))
                
                # Run the constellation
                result = await graph.aflow(state)
                
                # Store the state
                self.active_constellations[session_id]["state"] = result
                
                # Initialize effectiveness tracking
                await self._update_effectiveness_tracking_with_memory(result, user_profile)
                
                return result
            
        except Exception as e:
            logger.error(f"Error running constellation session: {e}")
            # Return a basic state with error message
            error_state = ConstellationState(
                session_id=session_id,
                user_id=user_profile.user_id,
                framework=framework,
                module_id=module_id
            )
            error_state.add_message(HumanMessage(content=user_message))
            error_state.add_message(AIMessage(content=f"I'm sorry, but I encountered an error: {str(e)}"))
            return error_state
            
    async def _check_curriculum_adaptation(self, state: ConstellationState, user_profile: UserProfile) -> None:
        """Check if the constellation needs to adapt based on curriculum progress."""
        try:
            # Only adapt if we have curriculum information
            if not state.curriculum_path_id:
                return
                
            # Get the learning engine
            from ..core.adaptive_learning_engine import AdaptiveLearningEngine
            learning_engine = AdaptiveLearningEngine()
            
            # Check if we have quiz results to process
            if state.quiz_results:
                # Get the most recent quiz result
                latest_quiz = state.quiz_results[-1]
                topic = latest_quiz.get("topic", "")
                score = latest_quiz.get("score", 0.0)
                
                # Update mastery level for the topic
                if topic:
                    state.mastery_levels[topic] = score
                    
                # Check if we need remediation content based on the score
                if score < 0.7:  # Below 70% mastery
                    await self._generate_remediation_content(state, topic, score)
                    return  # Don't advance curriculum if remediation is needed
                    
            # Check if we should advance in the curriculum
            # This happens if:
            # 1. We've completed the current objective (based on mastery levels)
            # 2. We've covered enough topics in the current module
            current_module_topics = state.session_context.get("current_module_topics", [])
            topics_covered = state.topics_covered
            
            # Calculate coverage percentage
            coverage = 0.0
            if current_module_topics:
                covered_count = sum(1 for topic in current_module_topics if topic in topics_covered)
                coverage = covered_count / len(current_module_topics)
                
            # Get average mastery level for covered topics
            mastery_levels = state.mastery_levels
            avg_mastery = 0.0
            if mastery_levels:
                avg_mastery = sum(mastery_levels.values()) / len(mastery_levels)
                
            # Check if we should advance
            should_advance = (coverage > 0.8 and avg_mastery > 0.7)  # 80% coverage and 70% mastery
            
            if should_advance:
                # Advance to the next objective or module
                advanced = state.advance_curriculum()
                
                if advanced:
                    # Update the state with the new curriculum position
                    await self._update_curriculum_position(state, user_profile)
                    
                    # Add a message about advancing in the curriculum
                    state.add_message(AIMessage(content=f"Great progress! You've completed the current topic with {int(avg_mastery * 100)}% mastery. Let's move on to the next topic in your learning path."))
                else:
                    # Curriculum completed
                    state.add_message(AIMessage(content="Congratulations! You've completed the entire curriculum for this framework. You can now apply your knowledge to real projects or explore advanced topics."))
            
        except Exception as e:
            logger.error(f"Error checking curriculum adaptation: {e}")
            
    async def _update_curriculum_position(self, state: ConstellationState, user_profile: UserProfile) -> None:
        """Update the state with the new curriculum position."""
        try:
            # Get the learning engine
            from ..core.adaptive_learning_engine import AdaptiveLearningEngine
            learning_engine = AdaptiveLearningEngine()
            
            # Get the current learning path
            learning_path = await learning_engine.get_learning_path(state.curriculum_path_id)
            if not learning_path:
                return
                
            # Update the current module and objective
            current_module = learning_path.modules[state.current_module_index]
            state.module_id = current_module.module_id
            
            # Get the current objective if available
            current_objective = None
            if current_module.objectives and state.current_objective_index < len(current_module.objectives):
                current_objective = current_module.objectives[state.current_objective_index]
            
            # Update session context with new module information
            state.session_context.update({
                "current_module": current_module.module_id,
                "current_module_name": current_module.name,
                "current_module_description": current_module.description,
                "current_module_topics": [obj.description for obj in current_module.objectives],
                "objectives_in_current_module": len(current_module.objectives),
                "current_objective": current_objective.objective_id if current_objective else None,
                "current_objective_description": current_objective.description if current_objective else None,
                "total_modules": len(learning_path.modules),
                "current_module_index": state.current_module_index,
                "current_objective_index": state.current_objective_index
            })
            
            # Update the user profile with the new progress
            if user_profile:
                # Find or create a learning session for this framework
                framework_sessions = [s for s in user_profile.learning_sessions 
                                     if s.framework == state.framework.value]
                
                if framework_sessions:
                    session = framework_sessions[-1]  # Get the latest session
                    session.current_module = current_module.module_id
                    session.completion_percentage = ((state.current_module_index / len(learning_path.modules)) * 100 +
                                                   (state.current_objective_index / len(current_module.objectives)) * 
                                                   (100 / len(learning_path.modules)))
                
        except Exception as e:
            logger.error(f"Error updating curriculum position: {e}")
    
    async def _generate_remediation_content(self, state: ConstellationState, topic: str, score: float) -> None:
        """Generate remediation content for a topic with low mastery."""
        try:
            # Get the learning engine
            from ..core.adaptive_learning_engine import AdaptiveLearningEngine
            learning_engine = AdaptiveLearningEngine()
            
            # Generate a remediation prompt based on the topic and score
            remediation_level = "basic" if score < 0.5 else "intermediate"
            
            # Get framework context for better remediation
            from ..core.vector_storage import vector_storage
            framework_results = await vector_storage.search_framework_knowledge(
                query=topic, 
                framework=state.framework
            )
            
            # Extract relevant content from the framework results
            relevant_content = ""
            if framework_results:
                for result in framework_results[:2]:  # Use top 2 results
                    relevant_content += result.get("content", "") + "\n\n"
            
            # Generate remediation content
            remediation_message = f"""I noticed you're having some difficulty with {topic}. Let's review this concept to help you master it.

Based on your quiz results ({int(score * 100)}% mastery), here's some additional explanation and practice to help you understand better:

{relevant_content}

Let's break down the key points:
1. {topic} is a fundamental concept in {state.framework.value}
2. It's important because it helps you build more effective applications
3. The most common mistake is misunderstanding how it integrates with other components

Would you like me to:
1. Provide a simpler explanation with more examples
2. Give you a practice exercise to reinforce your understanding
3. Show you how this concept is used in real-world applications

Let me know how you'd like to proceed!"""

            # Add the remediation message to the state
            state.add_message(AIMessage(content=remediation_message))
            
            # Track that remediation was provided
            state.adaptation_signals.append({
                "type": "remediation",
                "topic": topic,
                "score": score,
                "timestamp": datetime.now().isoformat()
            })
            
        except Exception as e:
            logger.error(f"Error generating remediation content: {e}")
            # Fallback remediation message
            state.add_message(AIMessage(content=f"I noticed you might need some additional help with {topic}. Let's review this concept together. What specific aspect would you like me to explain in more detail?"))
    
    def end_session(self, session_id: str) -> None:
        """End a constellation session and clean up resources."""
        if session_id in self.active_constellations:
            del self.active_constellations[session_id]
            logger.info(f"Ended constellation session {session_id}")
    
    def get_session_state(self, session_id: str) -> Optional[ConstellationState]:
        """Get the current state of a session."""
        # This would typically retrieve from the checkpointer
        # For now, return None as we'd need to implement state retrieval
        return None
    
    def get_active_sessions(self) -> List[str]:
        """Get list of active session IDs."""
        return list(self.active_constellations.keys())
    
    def _infer_constellation_type_from_agents(self, active_agents: List[AgentRole]) -> ConstellationType:
        """Infer constellation type from currently active agents."""
        if not active_agents:
            return ConstellationType.THEORY_PRACTICE_BALANCED
        
        agent_set = set(active_agents)
        
        # Check for knowledge-intensive patterns
        if AgentRole.INSTRUCTOR in agent_set and AgentRole.DOCUMENTATION_EXPERT in agent_set:
            return ConstellationType.KNOWLEDGE_INTENSIVE
        
        # Check for hands-on patterns
        if AgentRole.CODE_ASSISTANT in agent_set and AgentRole.PRACTICE_FACILITATOR in agent_set:
            return ConstellationType.HANDS_ON_FOCUSED
        
        # Check for assessment patterns
        if AgentRole.ASSESSMENT_AGENT in agent_set:
            return ConstellationType.ASSESSMENT_FOCUSED
        
        # Check for research patterns
        if AgentRole.RESEARCH_ASSISTANT in agent_set:
            return ConstellationType.RESEARCH_INTENSIVE
        
        # Default fallback
        return ConstellationType.THEORY_PRACTICE_BALANCED
    
    def _calculate_handoff_efficiency(self, state: ConstellationState) -> float:
        """Calculate handoff efficiency from agent handoff history."""
        if not state.agent_handoffs:
            return 1.0
        
        successful_handoffs = sum(1 for handoff in state.agent_handoffs 
                                 if not handoff.get("reason", "").startswith("error"))
        
        return successful_handoffs / len(state.agent_handoffs)
    
    def end_constellation_session_with_memory(
        self, 
        session_id: str,
        final_skill_assessment: Dict[str, float] = None,
        achievements: List[str] = None
    ) -> None:
        """End constellation session and update memory with final metrics."""
        if session_id in self.active_constellations:
            # End constellation memory session
            completed_session = self.constellation_memory.end_constellation_session(
                session_id=session_id,
                final_skill_assessment=final_skill_assessment or {},
                achievements=achievements or []
            )
            
            # Clean up active constellation
            del self.active_constellations[session_id]
            
            logger.info(f"Ended constellation session {session_id} with memory tracking")
            
            if completed_session:
                logger.info(f"Session insights: {len(completed_session.short_term_interactions)} short-term, "
                          f"{len(completed_session.long_term_insights)} long-term insights generated")
    
    def get_constellation_analytics(self, user_id: str) -> Dict[str, Any]:
        """Get constellation analytics for a user."""
        return self.constellation_memory.get_user_constellation_analytics(user_id)

    def _determine_optimal_constellation_with_memory(
        self, 
        user_profile: UserProfile, 
        framework: SupportedFrameworks
    ) -> ConstellationType:
        """
        Determine optimal constellation type using memory and user profile.
        
        Args:
            user_profile: User profile information
            framework: Target framework
            
        Returns:
            Optimal constellation type
        """
        # Get user's constellation history from memory
        analytics = self.constellation_memory.get_user_constellation_analytics(user_profile.user_id)
        
        # If user has history, use most effective constellation type
        if analytics and "most_effective_constellation" in analytics:
            most_effective = analytics["most_effective_constellation"]
            try:
                return ConstellationType(most_effective)
            except ValueError:
                pass  # Fall back to profile-based determination
        
        # Fall back to profile-based determination
        return self._determine_optimal_constellation(user_profile, framework)
    
    def _determine_optimal_constellation(
        self, 
        user_profile: UserProfile, 
        framework: SupportedFrameworks
    ) -> ConstellationType:
        """
        Determine optimal constellation type based on user profile.
        
        Args:
            user_profile: User profile information
            framework: Target framework
            
        Returns:
            Optimal constellation type
        """
        # Default constellation type
        constellation_type = ConstellationType.THEORY_PRACTICE_BALANCED
        
        # Determine based on user preferences and skill level
        if hasattr(user_profile, 'learning_style_preferences') and user_profile.learning_style_preferences:
            primary_style = user_profile.learning_style_preferences[0]
            
            if primary_style == "hands_on":
                constellation_type = ConstellationType.HANDS_ON_FOCUSED
            elif primary_style == "theoretical":
                constellation_type = ConstellationType.KNOWLEDGE_INTENSIVE
            elif primary_style == "visual":
                constellation_type = ConstellationType.INTERACTIVE_LEARNING
            elif primary_style == "project_based":
                constellation_type = ConstellationType.PROJECT_BASED
        
        # Adjust based on skill level
        if hasattr(user_profile, 'skill_level') and user_profile.skill_level:
            if user_profile.skill_level.value == "beginner":
                constellation_type = ConstellationType.GUIDED_LEARNING
            elif user_profile.skill_level.value == "advanced":
                constellation_type = ConstellationType.CHALLENGE_BASED
        
        return constellation_type
    
    async def _update_effectiveness_tracking_with_memory(
        self, 
        state: ConstellationState, 
        user_profile: UserProfile
    ) -> None:
        """
        Update effectiveness tracking using constellation memory.
        
        Args:
            state: Current constellation state
            user_profile: User profile information
        """
        try:
            # Calculate effectiveness metrics
            engagement_score = state.engagement_level
            comprehension_score = (
                sum(state.comprehension_indicators) / len(state.comprehension_indicators)
                if state.comprehension_indicators else 0.5
            )
            satisfaction_score = (
                sum(state.user_feedback_scores) / len(state.user_feedback_scores)
                if state.user_feedback_scores else 0.5
            )
            
            # Update constellation effectiveness
            overall_effectiveness = (engagement_score + comprehension_score + satisfaction_score) / 3
            state.constellation_effectiveness = overall_effectiveness
            
            # Record metrics in constellation memory
            self.constellation_memory.record_constellation_effectiveness(
                session_id=state.session_id,
                user_id=user_profile.user_id,
                constellation_type=self._infer_constellation_type_from_agents(state.active_agents),
                effectiveness_score=overall_effectiveness,
                engagement_level=engagement_score,
                comprehension_level=comprehension_score,
                satisfaction_level=satisfaction_score
            )
            
        except Exception as e:
            logger.error(f"Error updating effectiveness tracking: {e}")
    
    def _route_based_on_next_agent(self, state: ConstellationState) -> str:
        """
        Route to the next agent based on the state context.
        
        Args:
            state: Current constellation state
            
        Returns:
            Next agent role or END
        """
        next_agent = state.session_context.get("next_agent", END)
        
        # Validate that the next agent exists in our agent nodes
        if next_agent != END:
            try:
                agent_role = AgentRole(next_agent)
                if agent_role in self.agent_nodes:
                    return next_agent
            except ValueError:
                logger.warning(f"Invalid next agent role: {next_agent}")
        
        return END

    async def _check_constellation_adaptation(
        self,
        state: ConstellationState,
        user_profile: UserProfile,
        framework: SupportedFrameworks,
        module_id: str
    ) -> None:
        """
        Check if the constellation needs to be adapted based on state.
        
        Args:
            state: Current constellation state
            user_profile: User profile information
            framework: Target framework
            module_id: Current learning module
        """
        # Skip if effectiveness score is above threshold
        if state.constellation_effectiveness > 0.7:
            return
            
        # Check for curriculum-based adaptation signals
        curriculum_based_adaptation = False
        
        # Adapt based on quiz performance
        if state.quiz_results and len(state.quiz_results) >= 2:
            # Get the last two quiz results
            recent_quizzes = sorted(state.quiz_results, key=lambda x: x["timestamp"], reverse=True)[:2]
            
            # Check for consistent low performance
            if all(quiz["score"] < 0.6 for quiz in recent_quizzes):
                # Switch to more supportive constellation
                new_type = ConstellationType.GUIDED_LEARNING
                curriculum_based_adaptation = True
                
            # Check for consistent high performance
            elif all(quiz["score"] > 0.85 for quiz in recent_quizzes):
                # Switch to more challenging constellation
                new_type = ConstellationType.CHALLENGE_BASED
                curriculum_based_adaptation = True
                
        # Adapt based on mastery levels
        if state.mastery_levels:
            # Calculate average mastery
            avg_mastery = sum(state.mastery_levels.values()) / len(state.mastery_levels)
            
            # Low mastery - switch to guided learning
            if avg_mastery < 0.4 and not curriculum_based_adaptation:
                new_type = ConstellationType.GUIDED_LEARNING
                curriculum_based_adaptation = True
                
            # High mastery - switch to project-based
            elif avg_mastery > 0.8 and not curriculum_based_adaptation:
                new_type = ConstellationType.PROJECT_BASED
                curriculum_based_adaptation = True
                
        # If curriculum-based adaptation is needed
        if curriculum_based_adaptation:
            # Create new constellation
            new_graph = await self.create_constellation(
                constellation_type=new_type,
                user_profile=user_profile,
                framework=framework,
                module_id=module_id,
                session_id=state.session_id,
                learning_path_id=state.curriculum_path_id
            )
            
            # Update the active constellation
            self.active_constellations[state.session_id] = {
                "type": new_type,
                "graph": new_graph,
                "state": state
            }
            
            # Log the adaptation
            logger.info(f"Adapted constellation for session {state.session_id} to {new_type} based on curriculum performance")
            
            # Add adaptation notification
            state.add_message(AIMessage(content="I've adjusted my teaching approach based on your learning progress."))
            
        # If no curriculum-based adaptation, fall back to standard adaptation
        elif state.constellation_effectiveness < 0.5:
            # Determine a new constellation type
            current_type = self._infer_constellation_type_from_agents(state.active_agents)
            
            # Try to find a better constellation type
            if current_type == ConstellationType.BASIC_LEARNING:
                new_type = ConstellationType.GUIDED_LEARNING
            elif current_type == ConstellationType.GUIDED_LEARNING:
                new_type = ConstellationType.INTERACTIVE_LEARNING
            else:
                # Default to the determined optimal
                new_type = self._determine_optimal_constellation(user_profile, framework)
                
            # Only adapt if it's a different type
            if new_type != current_type:
                # Create new constellation
                new_graph = await self.create_constellation(
                    constellation_type=new_type,
                    user_profile=user_profile,
                    framework=framework,
                    module_id=module_id,
                    session_id=state.session_id,
                    learning_path_id=state.curriculum_path_id
                )
                
                # Update the active constellation
                self.active_constellations[state.session_id] = {
                    "type": new_type,
                    "graph": new_graph,
                    "state": state
                }
                
                # Log the adaptation
                logger.info(f"Adapted constellation for session {state.session_id} from {current_type} to {new_type}")
                
                # Add adaptation notification
                state.add_message(AIMessage(content="I've adjusted my teaching approach to better match your learning style.")) 