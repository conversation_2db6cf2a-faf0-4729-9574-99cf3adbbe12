"""
GAAPF - Guidance AI Agent for Python Framework

A multi-agent system for learning Python AI frameworks through personalized, 
interactive experiences using the novel "Adaptive Learning Constellation" architecture.
"""

__version__ = "0.1.0"
__author__ = "GAAPF - Guidance AI Agent Team"
__description__ = "Multi-agent system for AI framework learning"

from . import config, core, agents, tools, memory, interfaces

__all__ = [
    "__version__",
    "__author__", 
    "__description__",
    "config",
    "core", 
    "agents",
    "tools",
    "memory",
    "interfaces"
] 