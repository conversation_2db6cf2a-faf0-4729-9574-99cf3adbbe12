"""
Temporal State Management for Learning Effectiveness Tracking.
This system implements the novel temporal learning patterns analysis
for optimizing constellation configurations over time.
"""

from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import logging
import asyncio
import json
from dataclasses import dataclass, field
import numpy as np
from pydantic import BaseModel, Field

from ..config.user_profiles import (
    UserProfile, 
    LearningSession, 
    TemporalPattern,
    ConstellationEffectiveness
)
from ..config.framework_configs import SupportedFrameworks
from .constellation import ConstellationType

logger = logging.getLogger(__name__)


@dataclass
class EffectivenessMetrics:
    """Metrics for measuring learning effectiveness."""
    comprehension_score: float = 0.0
    engagement_score: float = 0.0
    completion_rate: float = 0.0
    satisfaction_score: float = 0.0
    time_efficiency: float = 0.0
    retention_estimate: float = 0.0
    
    def combined_score(self) -> float:
        """Calculate weighted combined effectiveness score."""
        weights = {
            'comprehension': 0.25,
            'engagement': 0.20,
            'completion': 0.15,
            'satisfaction': 0.15,
            'efficiency': 0.15,
            'retention': 0.10
        }
        
        return (
            self.comprehension_score * weights['comprehension'] +
            self.engagement_score * weights['engagement'] +
            self.completion_rate * weights['completion'] +
            self.satisfaction_score * weights['satisfaction'] +
            self.time_efficiency * weights['efficiency'] +
            self.retention_estimate * weights['retention']
        )


@dataclass
class LearningContext:
    """Context information for a learning session."""
    user_characteristics: Dict[str, Any] = field(default_factory=dict)
    session_timing: Dict[str, Any] = field(default_factory=dict)
    framework_context: Dict[str, Any] = field(default_factory=dict)
    environmental_factors: Dict[str, Any] = field(default_factory=dict)
    
    def to_pattern_key(self) -> str:
        """Convert context to a pattern matching key."""
        key_elements = [
            f"exp_{self.user_characteristics.get('experience_level', 'unknown')}",
            f"style_{self.user_characteristics.get('learning_style', 'unknown')}",
            f"pace_{self.user_characteristics.get('pace', 'unknown')}",
            f"fw_{self.framework_context.get('framework', 'unknown')}",
            f"time_{self.session_timing.get('time_of_day', 'unknown')}"
        ]
        return "_".join(key_elements)


class LearningEffectivenessTracker:
    """Tracks and analyzes learning effectiveness over time."""
    
    def __init__(self):
        """Initialize the effectiveness tracker."""
        self.session_data: Dict[str, List[LearningSession]] = {}
        self.effectiveness_cache: Dict[str, EffectivenessMetrics] = {}
        self.pattern_analysis_enabled = True
        
    def record_session(
        self,
        user_profile: UserProfile,
        session: LearningSession,
        context: LearningContext
    ) -> None:
        """
        Record a learning session for effectiveness analysis.
        
        Args:
            user_profile: User's learning profile
            session: Learning session data
            context: Session context information
        """
        user_id = user_profile.user_id
        
        if user_id not in self.session_data:
            self.session_data[user_id] = []
        
        self.session_data[user_id].append(session)
        
        # Calculate effectiveness metrics for this session
        metrics = self._calculate_session_effectiveness(session, context)
        
        # Cache the metrics
        cache_key = f"{user_id}_{session.session_id}"
        self.effectiveness_cache[cache_key] = metrics
        
        # Update user profile with effectiveness data
        self._update_user_effectiveness(user_profile, session, metrics, context)
        
        logger.info(f"Recorded session {session.session_id} for user {user_id}")
    
    def _calculate_session_effectiveness(
        self,
        session: LearningSession,
        context: LearningContext
    ) -> EffectivenessMetrics:
        """Calculate effectiveness metrics for a session."""
        metrics = EffectivenessMetrics()
        
        # Comprehension score (from session effectiveness if available)
        if session.effectiveness_score is not None:
            metrics.comprehension_score = session.effectiveness_score
        
        # Engagement score (from user satisfaction)
        if session.user_satisfaction is not None:
            metrics.engagement_score = session.user_satisfaction
        
        # Completion rate
        metrics.completion_rate = session.completion_percentage / 100.0
        
        # Satisfaction score (same as engagement for now)
        if session.user_satisfaction is not None:
            metrics.satisfaction_score = session.user_satisfaction
        
        # Time efficiency (topics covered per minute)
        if session.end_time and session.start_time:
            duration_minutes = (session.end_time - session.start_time).total_seconds() / 60
            if duration_minutes > 0:
                metrics.time_efficiency = len(session.topics_covered) / duration_minutes
        
        # Retention estimate (simplified heuristic based on completion and comprehension)
        metrics.retention_estimate = (metrics.comprehension_score + metrics.completion_rate) / 2
        
        return metrics
    
    def _update_user_effectiveness(
        self,
        user_profile: UserProfile,
        session: LearningSession,
        metrics: EffectivenessMetrics,
        context: LearningContext
    ) -> None:
        """Update user profile with effectiveness data."""
        constellation_type = session.constellation_used
        
        # Update constellation effectiveness in user profile
        if constellation_type not in user_profile.progress.constellation_effectiveness:
            user_profile.progress.constellation_effectiveness[constellation_type] = ConstellationEffectiveness(
                constellation_type=constellation_type,
                total_sessions=0,
                average_effectiveness=0.0,
                average_completion_time=0.0,
                user_preference_score=0.0,
                last_updated=datetime.now()
            )
        
        effectiveness_data = user_profile.progress.constellation_effectiveness[constellation_type]
        
        # Update running averages
        total_sessions = effectiveness_data.total_sessions
        new_effectiveness = metrics.combined_score()
        
        effectiveness_data.average_effectiveness = (
            (effectiveness_data.average_effectiveness * total_sessions + new_effectiveness) /
            (total_sessions + 1)
        )
        
        effectiveness_data.total_sessions += 1
        effectiveness_data.last_updated = datetime.now()
        
        # Update session duration if available
        if session.end_time and session.start_time:
            duration = (session.end_time - session.start_time).total_seconds() / 60
            effectiveness_data.average_completion_time = (
                (effectiveness_data.average_completion_time * total_sessions + duration) /
                (total_sessions + 1)
            )
    
    def get_constellation_effectiveness(
        self,
        user_profile: UserProfile,
        constellation_type: ConstellationType
    ) -> float:
        """Get effectiveness score for a constellation type."""
        effectiveness_data = user_profile.progress.constellation_effectiveness.get(
            constellation_type.value
        )
        
        if effectiveness_data:
            return effectiveness_data.average_effectiveness
        
        return 0.5  # Default neutral score
    
    def get_user_effectiveness_trends(
        self,
        user_id: str,
        days: int = 30
    ) -> Dict[str, List[float]]:
        """Get effectiveness trends for a user over time."""
        if user_id not in self.session_data:
            return {}
        
        cutoff_date = datetime.now() - timedelta(days=days)
        recent_sessions = [
            session for session in self.session_data[user_id]
            if session.start_time >= cutoff_date
        ]
        
        trends = {}
        for session in recent_sessions:
            constellation = session.constellation_used
            if constellation not in trends:
                trends[constellation] = []
            
            cache_key = f"{user_id}_{session.session_id}"
            if cache_key in self.effectiveness_cache:
                effectiveness = self.effectiveness_cache[cache_key].combined_score()
                trends[constellation].append(effectiveness)
        
        return trends


class TemporalStateManager:
    """Manages temporal patterns and state optimization."""
    
    def __init__(self):
        """Initialize the temporal state manager."""
        self.effectiveness_tracker = LearningEffectivenessTracker()
        self.pattern_database: Dict[str, List[TemporalPattern]] = {}
        self.optimization_enabled = True
        self.min_pattern_confidence = 0.7
        self.min_sample_size = 5
        
    async def analyze_learning_patterns(
        self,
        user_profile: UserProfile
    ) -> List[TemporalPattern]:
        """
        Analyze learning patterns for temporal optimization.
        
        Args:
            user_profile: User's learning profile
            
        Returns:
            List of identified temporal patterns
        """
        if not user_profile.progress.learning_session_history:
            return []
        
        patterns = []
        sessions = user_profile.progress.learning_session_history
        
        # Group sessions by context characteristics
        context_groups = self._group_sessions_by_context(sessions)
        
        for context_key, session_group in context_groups.items():
            if len(session_group) >= self.min_sample_size:
                pattern = await self._analyze_context_pattern(
                    context_key,
                    session_group,
                    user_profile
                )
                if pattern and pattern.confidence_score >= self.min_pattern_confidence:
                    patterns.append(pattern)
        
        # Update user profile with identified patterns
        user_profile.progress.temporal_patterns = patterns
        
        logger.info(f"Identified {len(patterns)} temporal patterns for user {user_profile.user_id}")
        return patterns
    
    def _group_sessions_by_context(
        self,
        sessions: List[LearningSession]
    ) -> Dict[str, List[LearningSession]]:
        """Group sessions by learning context characteristics."""
        groups = {}
        
        for session in sessions:
            # Create context key based on session characteristics
            context_elements = [
                session.framework,
                # Add more context elements based on session data
                "default_context"  # Placeholder for now
            ]
            context_key = "_".join(context_elements)
            
            if context_key not in groups:
                groups[context_key] = []
            groups[context_key].append(session)
        
        return groups
    
    async def _analyze_context_pattern(
        self,
        context_key: str,
        sessions: List[LearningSession],
        user_profile: UserProfile
    ) -> Optional[TemporalPattern]:
        """Analyze a specific context pattern."""
        if len(sessions) < self.min_sample_size:
            return None
        
        # Calculate effectiveness trends
        effectiveness_scores = []
        constellation_usage = {}
        
        for session in sessions:
            if session.effectiveness_score is not None:
                effectiveness_scores.append(session.effectiveness_score)
            
            constellation = session.constellation_used
            if constellation not in constellation_usage:
                constellation_usage[constellation] = []
            constellation_usage[constellation].append(session.effectiveness_score or 0.5)
        
        if not effectiveness_scores:
            return None
        
        # Find optimal constellation for this context
        optimal_constellation = max(
            constellation_usage.items(),
            key=lambda x: np.mean(x[1]) if x[1] else 0
        )[0] if constellation_usage else "unknown"
        
        # Calculate confidence based on sample size and consistency
        variance = np.var(effectiveness_scores) if len(effectiveness_scores) > 1 else 0
        consistency_score = max(0, 1 - variance)  # Higher consistency = higher confidence
        sample_size_factor = min(1.0, len(sessions) / 20)  # Normalize by expected size
        confidence = (consistency_score * 0.7) + (sample_size_factor * 0.3)
        
        # Extract user characteristics
        user_characteristics = {
            "experience_level": user_profile.python_skill_level.value,
            "learning_style": user_profile.preferred_learning_style.value,
            "learning_pace": user_profile.learning_pace.value,
            "framework": sessions[0].framework if sessions else "unknown"
        }
        
        # Create temporal pattern
        pattern = TemporalPattern(
            pattern_id=f"pattern_{context_key}_{user_profile.user_id}_{datetime.now().strftime('%Y%m%d')}",
            user_characteristics=user_characteristics,
            optimal_constellation=optimal_constellation,
            effectiveness_trend=effectiveness_scores[-10:],  # Last 10 sessions
            optimal_timing={"preferred_session_length": 60},  # Placeholder
            confidence_score=confidence,
            sample_size=len(sessions)
        )
        
        return pattern
    
    def get_optimal_constellation(
        self,
        user_profile: UserProfile,
        framework: SupportedFrameworks,
        current_context: Dict[str, Any]
    ) -> ConstellationType:
        """
        Get optimal constellation based on temporal patterns.
        
        Args:
            user_profile: User's learning profile
            framework: Target framework
            current_context: Current learning context
            
        Returns:
            Optimal constellation type
        """
        # Check temporal patterns first
        for pattern in user_profile.progress.temporal_patterns:
            if (pattern.user_characteristics.get("framework") == framework.value and
                pattern.confidence_score >= self.min_pattern_confidence):
                try:
                    return ConstellationType(pattern.optimal_constellation)
                except ValueError:
                    logger.warning(f"Invalid constellation type in pattern: {pattern.optimal_constellation}")
        
        # Fall back to effectiveness tracking
        best_constellation = ConstellationType.THEORY_PRACTICE_BALANCED
        best_effectiveness = 0.0
        
        for constellation_type in ConstellationType:
            effectiveness = self.effectiveness_tracker.get_constellation_effectiveness(
                user_profile, constellation_type
            )
            if effectiveness > best_effectiveness:
                best_effectiveness = effectiveness
                best_constellation = constellation_type
        
        return best_constellation
    
    async def optimize_constellation_selection(
        self,
        user_profile: UserProfile,
        framework: SupportedFrameworks,
        module_id: str,
        session_context: Dict[str, Any]
    ) -> Tuple[ConstellationType, float]:
        """
        Optimize constellation selection based on temporal patterns.
        
        Args:
            user_profile: User's learning profile
            framework: Target framework
            module_id: Learning module
            session_context: Current session context
            
        Returns:
            Tuple of (optimal_constellation, confidence_score)
        """
        if not self.optimization_enabled:
            return ConstellationType.THEORY_PRACTICE_BALANCED, 0.5
        
        # Analyze current patterns
        patterns = await self.analyze_learning_patterns(user_profile)
        
        # Find matching pattern
        current_characteristics = {
            "experience_level": user_profile.python_skill_level.value,
            "learning_style": user_profile.preferred_learning_style.value,
            "learning_pace": user_profile.learning_pace.value,
            "framework": framework.value
        }
        
        best_match = None
        best_similarity = 0.0
        
        for pattern in patterns:
            similarity = self._calculate_pattern_similarity(
                current_characteristics,
                pattern.user_characteristics
            )
            if similarity > best_similarity and pattern.confidence_score >= self.min_pattern_confidence:
                best_similarity = similarity
                best_match = pattern
        
        if best_match and best_similarity >= 0.8:
            try:
                optimal_constellation = ConstellationType(best_match.optimal_constellation)
                confidence = best_match.confidence_score * best_similarity
                logger.info(f"Selected constellation {optimal_constellation.value} with confidence {confidence:.2f}")
                return optimal_constellation, confidence
            except ValueError:
                logger.warning(f"Invalid constellation type: {best_match.optimal_constellation}")
        
        # Fall back to default optimization
        default_constellation = self.get_optimal_constellation(user_profile, framework, session_context)
        return default_constellation, 0.5
    
    def _calculate_pattern_similarity(
        self,
        characteristics1: Dict[str, Any],
        characteristics2: Dict[str, Any]
    ) -> float:
        """Calculate similarity between two sets of characteristics."""
        if not characteristics1 or not characteristics2:
            return 0.0
        
        matches = 0
        total = 0
        
        for key in set(characteristics1.keys()) | set(characteristics2.keys()):
            total += 1
            if (key in characteristics1 and key in characteristics2 and
                characteristics1[key] == characteristics2[key]):
                matches += 1
        
        return matches / total if total > 0 else 0.0
    
    def record_session_outcome(
        self,
        user_profile: UserProfile,
        session: LearningSession,
        constellation_type: ConstellationType,
        context: LearningContext
    ) -> None:
        """Record the outcome of a learning session."""
        self.effectiveness_tracker.record_session(user_profile, session, context)
        
        # Update pattern database
        pattern_key = context.to_pattern_key()
        if pattern_key not in self.pattern_database:
            self.pattern_database[pattern_key] = []
        
        # This would be expanded to store more detailed pattern data
        logger.info(f"Recorded session outcome for pattern {pattern_key}")
    
    def get_effectiveness_summary(
        self,
        user_profile: UserProfile
    ) -> Dict[str, Any]:
        """Get effectiveness summary for a user."""
        summary = {
            "total_sessions": len(user_profile.progress.learning_session_history),
            "constellation_effectiveness": {},
            "improvement_trends": {},
            "optimization_recommendations": []
        }
        
        # Constellation effectiveness
        for constellation_type, effectiveness_data in user_profile.progress.constellation_effectiveness.items():
            summary["constellation_effectiveness"][constellation_type] = {
                "average_effectiveness": effectiveness_data.average_effectiveness,
                "total_sessions": effectiveness_data.total_sessions,
                "average_completion_time": effectiveness_data.average_completion_time
            }
        
        # Get trends
        trends = self.effectiveness_tracker.get_user_effectiveness_trends(user_profile.user_id)
        summary["improvement_trends"] = trends
        
        # Generate recommendations
        if user_profile.progress.constellation_effectiveness:
            best_constellation = max(
                user_profile.progress.constellation_effectiveness.items(),
                key=lambda x: x[1].average_effectiveness
            )
            summary["optimization_recommendations"].append(
                f"Consider using {best_constellation[0]} constellation more often (avg effectiveness: {best_constellation[1].average_effectiveness:.2f})"
            )
        
        return summary 