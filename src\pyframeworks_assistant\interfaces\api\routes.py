"""
API routes for the GAAPF - Guidance AI Agent for Python Framework.
Provides endpoints for chat, constellation management, memory, and user operations.
"""

from typing import List, Dict, Any, Optional
from datetime import datetime
import uuid
import logging

from fastapi import APIRouter, HTTPException, Depends, status
from pydantic import BaseModel, Field
from langchain_core.messages import HumanMessage, AIMessage

from ...core.constellation import ConstellationManager, ConstellationState, ConstellationType, AgentRole
from ...core.temporal_state import TemporalStateManager, EffectivenessMetrics
from ...memory.conversation_memory import memory_manager
from ...config.user_profiles import UserProfile, SkillLevel, LearningPace, LearningStyle
from ...config.framework_configs import SupportedFrameworks, get_framework_config

logger = logging.getLogger(__name__)

# Request/Response Models

class ChatMessage(BaseModel):
    """Chat message model."""
    content: str = Field(description="Message content")
    role: str = Field(description="Message role (user/assistant)")
    timestamp: Optional[datetime] = Field(default_factory=datetime.now)


class ChatRequest(BaseModel):
    """Chat request model."""
    message: str = Field(description="User message")
    session_id: Optional[str] = Field(default=None, description="Session ID")
    framework: Optional[str] = Field(default="langchain", description="Framework context")
    module_id: Optional[str] = Field(default=None, description="Module context")


class ChatResponse(BaseModel):
    """Chat response model."""
    response: str = Field(description="AI response")
    agent_role: str = Field(description="Responding agent role")
    session_id: str = Field(description="Session ID")
    confidence_score: float = Field(description="Response confidence")
    suggested_handoff: Optional[str] = Field(default=None, description="Suggested next agent")
    metadata: Dict[str, Any] = Field(default_factory=dict)


class ConstellationRequest(BaseModel):
    """Constellation configuration request."""
    user_profile: Dict[str, Any] = Field(description="User profile data")
    framework: str = Field(description="Target framework")
    learning_objectives: List[str] = Field(description="Learning objectives")


class ConstellationResponse(BaseModel):
    """Constellation configuration response."""
    constellation_type: str = Field(description="Recommended constellation type")
    active_agents: List[str] = Field(description="Active agent roles")
    configuration: Dict[str, Any] = Field(description="Constellation configuration")
    reasoning: str = Field(description="Recommendation reasoning")


class MemoryRequest(BaseModel):
    """Memory operation request."""
    session_id: str = Field(description="Session ID")
    operation: str = Field(description="Operation type (get/end/summary)")


class MemoryResponse(BaseModel):
    """Memory operation response."""
    session_id: str = Field(description="Session ID")
    data: Dict[str, Any] = Field(description="Memory data")
    status: str = Field(description="Operation status")


class UserProfileRequest(BaseModel):
    """User profile creation/update request."""
    name: str = Field(description="User name")
    skill_level: str = Field(description="Skill level")
    learning_pace: str = Field(description="Learning pace")
    learning_style: str = Field(description="Learning style")
    preferred_frameworks: List[str] = Field(default_factory=list)
    learning_goals: List[str] = Field(default_factory=list)


class UserProfileResponse(BaseModel):
    """User profile response."""
    user_id: str = Field(description="User ID")
    profile: Dict[str, Any] = Field(description="User profile data")
    recommendations: Dict[str, Any] = Field(description="Learning recommendations")


# Create routers
chat_router = APIRouter()
constellation_router = APIRouter()
memory_router = APIRouter()
framework_router = APIRouter()
user_router = APIRouter()

# Global state managers
constellation_managers: Dict[str, ConstellationManager] = {}
temporal_managers: Dict[str, TemporalStateManager] = {}


# Chat endpoints

@chat_router.post("/send", response_model=ChatResponse)
async def send_chat_message(request: ChatRequest):
    """Send a chat message and get AI response."""
    try:
        # Generate session ID if not provided
        session_id = request.session_id or str(uuid.uuid4())
        
        # Get or create constellation manager
        if session_id not in constellation_managers:
            constellation_managers[session_id] = ConstellationManager()
            temporal_managers[session_id] = TemporalStateManager()
        
        constellation_manager = constellation_managers[session_id]
        temporal_manager = temporal_managers[session_id]
        
        # Get memory for session
        memory = memory_manager.get_or_create_session(session_id)
        
        # Create user message
        user_message = HumanMessage(content=request.message)
        memory.add_message(user_message, {
            "framework": request.framework,
            "module": request.module_id,
            "timestamp": datetime.now().isoformat()
        })
        
        # Get current constellation state
        state = constellation_manager.get_current_state()
        if not state:
            # Initialize state
            try:
                framework = SupportedFrameworks(request.framework)
            except ValueError:
                framework = SupportedFrameworks.LANGCHAIN
            
            state = ConstellationState(
                session_id=session_id,
                framework=framework,
                module_id=request.module_id or "introduction",
                messages=[user_message]
            )
            constellation_manager.initialize_state(state)
        else:
            state.messages.append(user_message)
        
        # Process with constellation
        agent_response = await constellation_manager.process_message(user_message, state)
        
        # Add AI response to memory
        ai_message = AIMessage(content=agent_response.content)
        memory.add_message(ai_message, {
            "agent_role": agent_response.agent_role.value,
            "confidence": agent_response.confidence_score,
            "framework": request.framework
        })
        
        # Track effectiveness
        effectiveness_metrics = EffectivenessMetrics(
            comprehension_score=agent_response.confidence_score,
            engagement_score=0.8,  # Would be calculated from user interaction
            completion_rate=state.progress_percentage / 100,
            satisfaction_score=0.8,  # Would come from user feedback
            time_efficiency=1.0,  # Would be calculated from session timing
            retention_score=0.8  # Would be calculated from long-term tracking
        )
        
        temporal_manager.record_interaction(
            constellation_type=state.constellation_type,
            effectiveness_metrics=effectiveness_metrics,
            context={
                "framework": request.framework,
                "module": request.module_id,
                "agent_role": agent_response.agent_role.value
            }
        )
        
        return ChatResponse(
            response=agent_response.content,
            agent_role=agent_response.agent_role.value,
            session_id=session_id,
            confidence_score=agent_response.confidence_score,
            suggested_handoff=agent_response.suggested_handoff.value if agent_response.suggested_handoff else None,
            metadata=agent_response.metadata
        )
        
    except Exception as e:
        logger.error(f"Error in chat endpoint: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@chat_router.get("/history/{session_id}")
async def get_chat_history(session_id: str):
    """Get chat history for a session."""
    try:
        history = memory_manager.get_session_history(session_id)
        if not history:
            raise HTTPException(status_code=404, detail="Session not found")
        
        return {"session_id": session_id, "history": history}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting chat history: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Constellation endpoints

@constellation_router.post("/recommend", response_model=ConstellationResponse)
async def recommend_constellation(request: ConstellationRequest):
    """Recommend optimal constellation configuration."""
    try:
        # Create user profile from request
        profile_data = request.user_profile
        user_profile = UserProfile(
            name=profile_data.get("name", "User"),
            skill_level=SkillLevel(profile_data.get("skill_level", "beginner")),
            learning_pace=LearningPace(profile_data.get("learning_pace", "moderate")),
            learning_style=LearningStyle(profile_data.get("learning_style", "visual")),
            preferred_frameworks=[request.framework],
            learning_goals=request.learning_objectives
        )
        
        # Get framework config
        try:
            framework = SupportedFrameworks(request.framework)
            framework_config = get_framework_config(framework)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"Unsupported framework: {request.framework}")
        
        # Create constellation manager for recommendation
        constellation_manager = ConstellationManager()
        
        # Recommend constellation type based on user profile
        constellation_type = constellation_manager._recommend_constellation_type(
            user_profile, framework_config, request.learning_objectives
        )
        
        # Get active agents for this constellation
        active_agents = constellation_manager._get_constellation_agents(constellation_type)
        
        return ConstellationResponse(
            constellation_type=constellation_type.value,
            active_agents=[agent.value for agent in active_agents],
            configuration={
                "framework": request.framework,
                "learning_objectives": request.learning_objectives,
                "recommended_modules": framework_config.learning_paths[0].modules[:3] if framework_config.learning_paths else [],
                "estimated_duration": "2-4 hours",
                "difficulty": user_profile.skill_level.value
            },
            reasoning=f"Recommended {constellation_type.value} constellation based on {user_profile.skill_level.value} skill level and {user_profile.learning_style.value} learning style"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in constellation recommendation: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@constellation_router.get("/types")
async def get_constellation_types():
    """Get available constellation types."""
    return {
        "constellation_types": [
            {
                "type": constellation_type.value,
                "description": constellation_type.value.replace("_", " ").title()
            }
            for constellation_type in ConstellationType
        ]
    }


@constellation_router.get("/agents")
async def get_agent_roles():
    """Get available agent roles."""
    return {
        "agent_roles": [
            {
                "role": agent_role.value,
                "description": agent_role.value.replace("_", " ").title()
            }
            for agent_role in AgentRole
        ]
    }


# Memory endpoints

@memory_router.post("/operation", response_model=MemoryResponse)
async def memory_operation(request: MemoryRequest):
    """Perform memory operations."""
    try:
        session_id = request.session_id
        operation = request.operation.lower()
        
        if operation == "get":
            data = memory_manager.get_session_history(session_id)
            if not data:
                raise HTTPException(status_code=404, detail="Session not found")
            return MemoryResponse(
                session_id=session_id,
                data=data,
                status="success"
            )
        
        elif operation == "end":
            summary = await memory_manager.end_session(session_id)
            return MemoryResponse(
                session_id=session_id,
                data={"summary": summary.dict() if summary else None},
                status="session_ended"
            )
        
        elif operation == "summary":
            if session_id in memory_manager.active_sessions:
                memory = memory_manager.active_sessions[session_id]
                summary = await memory.create_session_summary()
                return MemoryResponse(
                    session_id=session_id,
                    data={"summary": summary.dict()},
                    status="summary_created"
                )
            else:
                raise HTTPException(status_code=404, detail="Active session not found")
        
        else:
            raise HTTPException(status_code=400, detail=f"Unknown operation: {operation}")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in memory operation: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@memory_router.get("/sessions")
async def get_all_sessions():
    """Get all memory sessions."""
    try:
        sessions = memory_manager.get_all_sessions()
        return {"sessions": sessions}
    except Exception as e:
        logger.error(f"Error getting sessions: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Framework endpoints

@framework_router.get("/")
async def get_supported_frameworks():
    """Get all supported frameworks with dynamic information."""
    frameworks = []
    for framework in SupportedFrameworks:
        try:
            # Use dynamic configuration (no cache to get fresh data)
            config = get_framework_config(framework, use_cache=False)
            
            # Extract description from primary use cases if available
            description = config.primary_use_cases[0] if config.primary_use_cases else f"{config.display_name} framework"
            
            frameworks.append({
                "name": framework.value,
                "display_name": config.display_name,
                "description": description,
                "latest_version": config.latest_version,
                "documentation_url": config.documentation_url,
                "github_repository": config.github_repository,
                "key_features_count": len(config.key_features),
                "learning_paths": len(config.learning_paths),
                "total_modules": sum(len(path.modules) for path in config.learning_paths),
                "last_updated": config.last_updated,
                "source_urls_count": len(config.source_urls) if config.source_urls else 0
            })
        except Exception as e:
            logger.warning(f"Error loading dynamic config for {framework.value}: {e}")
            frameworks.append({
                "name": framework.value,
                "display_name": framework.value.capitalize(),
                "description": f"{framework.value.capitalize()} framework",
                "latest_version": "latest",
                "documentation_url": f"https://docs.{framework.value}.com",
                "github_repository": f"https://github.com/{framework.value}/{framework.value}",
                "key_features_count": 0,
                "learning_paths": 0,
                "total_modules": 0,
                "last_updated": None,
                "source_urls_count": 0
            })
    
    return {"frameworks": frameworks}


@framework_router.get("/{framework_name}")
async def get_framework_details(framework_name: str):
    """Get detailed information about a specific framework."""
    try:
        framework = SupportedFrameworks(framework_name.lower())
        config = get_framework_config(framework)
        
        return {
            "framework": config.dict(),
            "learning_paths": [path.dict() for path in config.learning_paths]
        }
    except ValueError:
        raise HTTPException(status_code=404, detail=f"Framework {framework_name} not supported")
    except Exception as e:
        logger.error(f"Error getting framework details: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# User endpoints

@user_router.post("/profile", response_model=UserProfileResponse)
async def create_user_profile(request: UserProfileRequest):
    """Create or update user profile."""
    try:
        user_id = str(uuid.uuid4())
        
        # Create user profile
        user_profile = UserProfile(
            name=request.name,
            skill_level=SkillLevel(request.skill_level),
            learning_pace=LearningPace(request.learning_pace),
            learning_style=LearningStyle(request.learning_style),
            preferred_frameworks=request.preferred_frameworks,
            learning_goals=request.learning_goals
        )
        
        # Generate recommendations based on profile
        recommendations = {
            "suggested_frameworks": [],
            "learning_path": [],
            "constellation_type": "knowledge_intensive"  # Default
        }
        
        # Recommend frameworks based on skill level and goals
        if user_profile.skill_level == SkillLevel.BEGINNER:
            recommendations["suggested_frameworks"] = ["langchain", "streamlit"]
            recommendations["constellation_type"] = "knowledge_intensive"
        elif user_profile.skill_level == SkillLevel.INTERMEDIATE:
            recommendations["suggested_frameworks"] = ["langchain", "langraph", "fastapi"]
            recommendations["constellation_type"] = "theory_practice_balanced"
        else:
            recommendations["suggested_frameworks"] = ["langraph", "fastapi", "advanced_patterns"]
            recommendations["constellation_type"] = "project_guided"
        
        return UserProfileResponse(
            user_id=user_id,
            profile=user_profile.dict(),
            recommendations=recommendations
        )
        
    except Exception as e:
        logger.error(f"Error creating user profile: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@user_router.get("/profile/{user_id}")
async def get_user_profile(user_id: str):
    """Get user profile by ID."""
    # Placeholder - in production would retrieve from database
    raise HTTPException(status_code=501, detail="User profile retrieval not implemented")


@user_router.get("/progress/{user_id}")
async def get_user_progress(user_id: str):
    """Get user learning progress."""
    # Placeholder - in production would retrieve from database
    raise HTTPException(status_code=501, detail="User progress tracking not implemented") 