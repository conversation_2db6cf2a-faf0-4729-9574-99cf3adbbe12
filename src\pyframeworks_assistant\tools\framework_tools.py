"""
Framework-specific tools for version checking, API reference, and best practices.
Provides specialized functionality for each supported AI framework using dynamic web search.
"""

import re
import json
from typing import Dict, Any, List, Optional
from datetime import datetime
import logging

from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field

from ..config.framework_configs import SupportedFrameworks, get_framework_config
from .search_tools import TavilySearchTool

logger = logging.getLogger(__name__)


class VersionInfo(BaseModel):
    """Framework version information."""
    framework: str = Field(description="Framework name")
    current_version: str = Field(description="Current installed version")
    latest_version: str = Field(description="Latest available version")
    is_outdated: bool = Field(description="Whether current version is outdated")
    update_required: bool = Field(description="Whether update is strongly recommended")
    changelog_highlights: List[str] = Field(default_factory=list, description="Key changes in latest version")
    update_command: str = Field(description="Command to update the framework")
    source_urls: List[str] = Field(default_factory=list, description="Source URLs for version information")


class ApiReference(BaseModel):
    """API reference information."""
    framework: str = Field(description="Framework name")
    module: str = Field(description="Module or component name")
    description: str = Field(description="Module description")
    key_classes: List[str] = Field(default_factory=list, description="Important classes")
    key_functions: List[str] = Field(default_factory=list, description="Important functions")
    usage_examples: List[str] = Field(default_factory=list, description="Code examples")
    documentation_url: str = Field(description="Official documentation URL")
    common_patterns: List[str] = Field(default_factory=list, description="Common usage patterns")
    source_urls: List[str] = Field(default_factory=list, description="Source URLs for API information")


class BestPractice(BaseModel):
    """Best practice recommendation."""
    title: str = Field(description="Practice title")
    description: str = Field(description="Detailed description")
    category: str = Field(description="Practice category (performance, security, etc.)")
    importance: str = Field(description="Importance level: low, medium, high, critical")
    code_example: Optional[str] = Field(default=None, description="Code example")
    avoid_example: Optional[str] = Field(default=None, description="What to avoid")
    rationale: str = Field(description="Why this practice is important")
    related_docs: List[str] = Field(default_factory=list, description="Related documentation links")
    source_urls: List[str] = Field(default_factory=list, description="Source URLs for best practices")


class ExampleCode(BaseModel):
    """Code example with explanation."""
    title: str = Field(description="Example title")
    description: str = Field(description="Example description")
    code: str = Field(description="Example code")
    explanation: str = Field(description="Code explanation")
    complexity: str = Field(description="Complexity level: beginner, intermediate, advanced")
    use_cases: List[str] = Field(default_factory=list, description="When to use this pattern")
    source_urls: List[str] = Field(default_factory=list, description="Source URLs for examples")


class DynamicFrameworkVersionTool(BaseTool):
    """Dynamic tool for checking framework versions using web search."""
    
    name: str = "dynamic_framework_version"
    description: str = """
    Check framework versions and available updates using real-time web search.
    Uses search tools to find current version information from official sources.
    
    Input: Framework name
    Output: Version information and update recommendations from current sources
    """
    
    def _run(self, framework: str) -> VersionInfo:
        """Check framework version information using web search."""
        try:
            # Get framework config for official sources
            try:
                fw_enum = SupportedFrameworks(framework.lower())
                config = get_framework_config(fw_enum)
            except ValueError:
                return VersionInfo(
                    framework=framework,
                    current_version="unknown",
                    latest_version="unknown",
                    is_outdated=False,
                    update_required=False,
                    changelog_highlights=["Framework not supported"],
                    update_command=f"# {framework} is not supported",
                    source_urls=[]
                )
            
            # Use search to get current version information
            version_info = self._search_version_info(framework, config)
            return version_info
            
        except Exception as e:
            logger.error(f"Error checking version for {framework}: {e}")
            return VersionInfo(
                framework=framework,
                current_version="error",
                latest_version="error",
                is_outdated=False,
                update_required=False,
                changelog_highlights=[f"Error checking version: {str(e)}"],
                update_command=f"pip install --upgrade {framework}",
                source_urls=[]
            )
    
    def _search_version_info(self, framework: str, config) -> VersionInfo:
        """Search for current version information."""
        search_tool = TavilySearchTool()
        
        # Search for latest version information
        version_query = f"{framework} latest version release notes changelog PyPI"
        search_result = search_tool._run(version_query)
        
        # Extract version information from search results
        latest_version = self._extract_latest_version(search_result, framework)
        changelog_highlights = self._extract_changelog_highlights(search_result, framework)
        source_urls = self._extract_source_urls(search_result)
        
        # Try to get current installed version (this would need actual pip/conda checking)
        current_version = latest_version  # Simplified for now
        
        # Determine if update is needed
        is_outdated = self._compare_versions(current_version, latest_version)
        
        return VersionInfo(
            framework=framework,
            current_version=current_version,
            latest_version=latest_version,
            is_outdated=is_outdated,
            update_required=is_outdated,
            changelog_highlights=changelog_highlights,
            update_command=f"pip install --upgrade {framework}",
            source_urls=source_urls
        )
    
    def _extract_latest_version(self, search_result: str, framework: str) -> str:
        """Extract latest version from search results."""
        # Look for version patterns in search results
        version_patterns = [
            r'version\s*([0-9]+\.[0-9]+\.[0-9]+)',
            r'v([0-9]+\.[0-9]+\.[0-9]+)',
            r'release\s*([0-9]+\.[0-9]+\.[0-9]+)',
            r'{}\s*([0-9]+\.[0-9]+\.[0-9]+)'.format(framework)
        ]
        
        for pattern in version_patterns:
            matches = re.findall(pattern, search_result.lower())
            if matches:
                # Return the most recent looking version
                versions = [match for match in matches if match]
                if versions:
                    return sorted(versions, reverse=True)[0]
        
        return "latest"
    
    def _extract_changelog_highlights(self, search_result: str, framework: str) -> List[str]:
        """Extract changelog highlights from search results."""
        highlights = []
        
        # Look for common changelog patterns
        lines = search_result.split('\n')
        for line in lines:
            line = line.strip()
            # Look for bullet points, new features, improvements
            if any(keyword in line.lower() for keyword in ['new', 'added', 'improved', 'fixed', 'enhanced']):
                if len(line) > 10 and len(line) < 200:
                    highlights.append(line)
        
        # Limit to top 5 highlights
        return highlights[:5] if highlights else [f"Check {framework} release notes for latest updates"]
    
    def _extract_source_urls(self, search_result: str) -> List[str]:
        """Extract source URLs from search results."""
        url_pattern = r'https?://[^\s<>"{}|\\^`\[\]]+[^\s.,<>"{}|\\^`\[\]]'
        urls = re.findall(url_pattern, search_result)
        
        # Filter for relevant URLs
        relevant_urls = []
        for url in urls:
            if any(domain in url.lower() for domain in ['github.com', 'pypi.org', 'docs.', 'python.langchain.com']):
                relevant_urls.append(url)
        
        return list(set(relevant_urls))[:5]  # Remove duplicates and limit
    
    def _compare_versions(self, current: str, latest: str) -> bool:
        """Compare version strings to determine if update is needed."""
        if current == "latest" or latest == "latest":
            return False
        
        try:
            current_parts = [int(x) for x in current.split('.')]
            latest_parts = [int(x) for x in latest.split('.')]
            
            # Pad shorter version with zeros
            max_len = max(len(current_parts), len(latest_parts))
            current_parts.extend([0] * (max_len - len(current_parts)))
            latest_parts.extend([0] * (max_len - len(latest_parts)))
            
            return current_parts < latest_parts
        except:
            return False


class DynamicApiReferenceTool(BaseTool):
    """Dynamic tool for providing API reference information using web search."""
    
    name: str = "dynamic_api_reference"
    description: str = """
    Provide API reference information for framework components using web search.
    Searches official documentation and sources for current API information.
    
    Input: Framework and module/component name
    Output: Current API reference with examples from official sources
    """
    
    def _run(self, framework: str, module: str) -> ApiReference:
        """Get API reference for a framework module using web search."""
        try:
            # Get reference data from web search
            reference_data = self._search_api_reference(framework, module)
            return reference_data
            
        except Exception as e:
            logger.error(f"Error getting API reference for {framework}.{module}: {e}")
            return ApiReference(
                framework=framework,
                module=module,
                description=f"API reference for {module} in {framework}",
                key_classes=[],
                key_functions=[],
                usage_examples=[],
                documentation_url=f"https://docs.{framework}.com/",
                common_patterns=[],
                source_urls=[]
            )
    
    def _search_api_reference(self, framework: str, module: str) -> ApiReference:
        """Search for API reference information."""
        search_tool = TavilySearchTool()
        
        # Search for API documentation
        api_query = f"{framework} {module} API documentation class methods examples"
        search_result = search_tool._run(api_query)
        
        # Extract API information
        description = self._extract_description(search_result, framework, module)
        key_classes = self._extract_classes(search_result)
        key_functions = self._extract_functions(search_result)
        usage_examples = self._extract_usage_examples(search_result)
        documentation_url = self._extract_documentation_url(search_result, framework)
        common_patterns = self._extract_common_patterns(search_result)
        source_urls = self._extract_source_urls(search_result)
        
        return ApiReference(
            framework=framework,
            module=module,
            description=description,
            key_classes=key_classes,
            key_functions=key_functions,
            usage_examples=usage_examples,
            documentation_url=documentation_url,
            common_patterns=common_patterns,
            source_urls=source_urls
        )
    
    def _extract_description(self, search_result: str, framework: str, module: str) -> str:
        """Extract module description from search results."""
        lines = search_result.split('\n')
        for line in lines:
            if module.lower() in line.lower() and len(line) > 20 and len(line) < 300:
                if any(word in line.lower() for word in ['class', 'module', 'component', 'used for', 'provides']):
                    return line.strip()
        return f"{module} component in {framework}"
    
    def _extract_classes(self, search_result: str) -> List[str]:
        """Extract class names from search results."""
        class_pattern = r'class\s+([A-Z][a-zA-Z0-9_]*)'
        classes = re.findall(class_pattern, search_result)
        return list(set(classes))[:10]
    
    def _extract_functions(self, search_result: str) -> List[str]:
        """Extract function names from search results."""
        function_patterns = [
            r'def\s+([a-zA-Z_][a-zA-Z0-9_]*)\(',
            r'function\s+([a-zA-Z_][a-zA-Z0-9_]*)',
            r'method\s+([a-zA-Z_][a-zA-Z0-9_]*)'
        ]
        
        functions = []
        for pattern in function_patterns:
            functions.extend(re.findall(pattern, search_result))
        
        return list(set(functions))[:10]
    
    def _extract_usage_examples(self, search_result: str) -> List[str]:
        """Extract usage examples from search results."""
        examples = []
        lines = search_result.split('\n')
        
        in_code_block = False
        current_example = []
        
        for line in lines:
            if '```' in line:
                if in_code_block and current_example:
                    example = '\n'.join(current_example)
                    if len(example) > 10 and len(example) < 500:
                        examples.append(example)
                    current_example = []
                in_code_block = not in_code_block
            elif in_code_block:
                current_example.append(line)
        
        return examples[:5]
    
    def _extract_documentation_url(self, search_result: str, framework: str) -> str:
        """Extract official documentation URL."""
        url_pattern = r'https?://[^\s<>"{}|\\^`\[\]]+[^\s.,<>"{}|\\^`\[\]]'
        urls = re.findall(url_pattern, search_result)
        
        # Look for official documentation URLs
        for url in urls:
            if any(pattern in url.lower() for pattern in [f'docs.{framework}', f'{framework}.com/docs', 'python.langchain.com']):
                return url
        
        # Fallback to common documentation patterns
        doc_urls = {
            'langchain': 'https://python.langchain.com/docs/',
            'langgraph': 'https://langchain-ai.github.io/langgraph/',
            'fastapi': 'https://fastapi.tiangolo.com/',
            'streamlit': 'https://docs.streamlit.io/'
        }
        
        return doc_urls.get(framework, f"https://docs.{framework}.com/")
    
    def _extract_common_patterns(self, search_result: str) -> List[str]:
        """Extract common usage patterns from search results."""
        patterns = []
        lines = search_result.split('\n')
        
        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['pattern', 'best practice', 'common', 'typical', 'usually']):
                if len(line) > 20 and len(line) < 200:
                    patterns.append(line)
        
        return patterns[:5]


class DynamicExampleGeneratorTool(BaseTool):
    """Dynamic tool for generating code examples using web search."""
    
    name: str = "dynamic_example_generator"
    description: str = """
    Generate practical code examples for framework concepts using web search.
    Searches for current examples and patterns from official sources.
    
    Input: Framework, concept, and complexity level
    Output: Current code examples with explanations from official sources
    """
    
    def _run(self, framework: str, concept: str, complexity: str = "beginner") -> ExampleCode:
        """Generate a code example using web search."""
        try:
            example = self._search_examples(framework, concept, complexity)
            return example
            
        except Exception as e:
            logger.error(f"Error generating example for {framework}.{concept}: {e}")
            return ExampleCode(
                title=f"{concept} Example",
                description=f"{complexity.title()} example of {concept} in {framework}",
                code=f"# {concept} example for {framework}\n# Search for current examples in official documentation",
                explanation=f"Search official {framework} documentation for current {concept} examples",
                complexity=complexity,
                use_cases=[f"Learning {concept}", f"Building {concept} applications"],
                source_urls=[]
            )
    
    def _search_examples(self, framework: str, concept: str, complexity: str) -> ExampleCode:
        """Search for code examples."""
        search_tool = TavilySearchTool()
        
        # Search for examples
        example_query = f"{framework} {concept} {complexity} example code tutorial"
        search_result = search_tool._run(example_query)
        
        # Extract example information
        title = f"{concept.title()} Example - {complexity.title()}"
        description = self._extract_example_description(search_result, concept, complexity)
        code = self._extract_example_code(search_result)
        explanation = self._extract_example_explanation(search_result, concept)
        use_cases = self._extract_use_cases(search_result, concept)
        source_urls = self._extract_source_urls(search_result)
        
        return ExampleCode(
            title=title,
            description=description,
            code=code,
            explanation=explanation,
            complexity=complexity,
            use_cases=use_cases,
            source_urls=source_urls
        )
    
    def _extract_example_description(self, search_result: str, concept: str, complexity: str) -> str:
        """Extract example description."""
        lines = search_result.split('\n')
        for line in lines:
            if concept.lower() in line.lower() and complexity.lower() in line.lower():
                if len(line) > 20 and len(line) < 300:
                    return line.strip()
        return f"{complexity.title()} example demonstrating {concept}"
    
    def _extract_example_code(self, search_result: str) -> str:
        """Extract code from search results."""
        examples = []
        lines = search_result.split('\n')
        
        in_code_block = False
        current_code = []
        
        for line in lines:
            if '```' in line:
                if in_code_block and current_code:
                    code = '\n'.join(current_code)
                    if len(code) > 20:
                        examples.append(code)
                    current_code = []
                in_code_block = not in_code_block
            elif in_code_block:
                current_code.append(line)
        
        if examples:
            # Return the longest, most complete example
            return max(examples, key=len)
        
        return "# Example code - check official documentation for current syntax"
    
    def _extract_example_explanation(self, search_result: str, concept: str) -> str:
        """Extract explanation for the example."""
        lines = search_result.split('\n')
        explanations = []
        
        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['explain', 'this example', 'demonstrates', 'shows how']):
                if len(line) > 30 and len(line) < 300:
                    explanations.append(line)
        
        if explanations:
            return explanations[0]
        
        return f"This example demonstrates how to use {concept}. Check official documentation for detailed explanations."
    
    def _extract_use_cases(self, search_result: str, concept: str) -> List[str]:
        """Extract use cases from search results."""
        use_cases = []
        lines = search_result.split('\n')
        
        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['use case', 'when to use', 'useful for', 'good for']):
                if len(line) > 20 and len(line) < 200:
                    use_cases.append(line)
        
        return use_cases[:3] if use_cases else [f"Learning {concept}", f"Building applications with {concept}"]


class DynamicBestPracticesTool(BaseTool):
    """Dynamic tool for providing framework best practices using web search."""
    
    name: str = "dynamic_best_practices"
    description: str = """
    Provide current best practices and recommendations using web search.
    Searches for latest best practices from official sources and community.
    
    Input: Framework and optional category
    Output: Current best practices from official and community sources
    """
    
    def _run(self, framework: str, category: str = "general") -> List[BestPractice]:
        """Get best practices for a framework using web search."""
        try:
            practices = self._search_best_practices(framework, category)
            return practices
            
        except Exception as e:
            logger.error(f"Error getting best practices for {framework}: {e}")
            return [BestPractice(
                title="Search Official Documentation",
                description=f"Check official {framework} documentation for current best practices",
                category="general",
                importance="high",
                rationale=f"Official {framework} documentation provides the most current guidelines",
                related_docs=[],
                source_urls=[]
            )]
    
    def _search_best_practices(self, framework: str, category: str) -> List[BestPractice]:
        """Search for best practices."""
        search_tool = TavilySearchTool()
        
        # Search for best practices
        practices_query = f"{framework} best practices {category} guidelines recommendations 2024"
        search_result = search_tool._run(practices_query)
        
        # Extract practices
        practices = self._extract_practices_from_search(search_result, framework, category)
        
        return practices[:5]  # Limit to top 5
    
    def _extract_practices_from_search(self, search_result: str, framework: str, category: str) -> List[BestPractice]:
        """Extract best practices from search results."""
        practices = []
        lines = search_result.split('\n')
        
        current_practice = {}
        for line in lines:
            line = line.strip()
            
            # Look for practice titles (headings, bullet points, etc.)
            if any(keyword in line.lower() for keyword in ['best practice', 'recommendation', 'should', 'avoid', 'tip']):
                if len(line) > 10 and len(line) < 150:
                    if current_practice:
                        practices.append(self._create_practice_from_data(current_practice, framework, category))
                    
                    current_practice = {
                        'title': line,
                        'description': '',
                        'rationale': ''
                    }
            elif current_practice and len(line) > 20 and len(line) < 300:
                if not current_practice.get('description'):
                    current_practice['description'] = line
                elif not current_practice.get('rationale'):
                    current_practice['rationale'] = line
        
        # Add the last practice
        if current_practice:
            practices.append(self._create_practice_from_data(current_practice, framework, category))
        
        return practices
    
    def _create_practice_from_data(self, data: Dict[str, str], framework: str, category: str) -> BestPractice:
        """Create a BestPractice object from extracted data."""
        return BestPractice(
            title=data.get('title', 'Best Practice'),
            description=data.get('description', 'Follow current best practices'),
            category=category,
            importance="medium",
            rationale=data.get('rationale', f"Important for {framework} development"),
            related_docs=[],
            source_urls=[]
        )