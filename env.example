# LLM API Keys
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
GOOGLE_API_KEY=your_google_genai_api_key_here

# Search API Configuration
# Google Search API (fallback search functionality)
# Step 1: Get Google API Key from https://console.cloud.google.com/apis/credentials
# Step 2: Enable Custom Search API in Google Cloud Console
# Step 3: Create Custom Search Engine at https://programmablesearchengine.google.com/controlpanel/create
# Step 4: Get your Search Engine ID from the control panel
GOOGLE_CSE_ID=your_google_custom_search_engine_id_here

# Tavily API (Primary AI-optimized search - Recommended)
# Get your Tavily API key from https://tavily.com
# Free plan includes 1000 searches per month
TAVILY_API_KEY=your_tavily_api_key_here

# LangSmith Configuration (Optional)
LANGCHAIN_TRACING_V2=true
LANGCHAIN_ENDPOINT=https://api.smith.langchain.com
LANGCHAIN_API_KEY=your_langsmith_api_key_here
LANGCHAIN_PROJECT=pyframeworks-assistant

# Database Configuration
REDIS_URL=redis://localhost:6379/0
POSTGRES_URL=postgresql://user:password@localhost:5432/pyframeworks_db

# ChromaDB Configuration
CHROMA_PERSIST_DIRECTORY=./data/chroma_db

# Application Settings
DEBUG=true
LOG_LEVEL=INFO
SECRET_KEY=your_secret_key_here

# Web Interface Settings
STREAMLIT_PORT=8501
API_PORT=8000

# User Configuration
DEFAULT_USER_TIMEZONE=UTC
DEFAULT_LANGUAGE=english
DEFAULT_LEARNING_PACE=moderate

# Framework Knowledge Sources
GITHUB_TOKEN=your_github_token_for_repo_access
WEB_SEARCH_API_KEY=your_web_search_api_key

# Temporal Learning Settings
EFFECTIVENESS_TRACKING_ENABLED=true
PATTERN_ANALYSIS_ENABLED=true
OPTIMIZATION_AUTO_APPLY=false 