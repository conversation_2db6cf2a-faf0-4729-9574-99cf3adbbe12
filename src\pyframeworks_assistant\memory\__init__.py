"""
Memory management components for the PyFramework Learning Assistant.
Handles conversation memory, user memory, knowledge memory, and constellation memory.
"""

from .conversation_memory import (
    ConversationMemory,
    ConversationSummary,
    ConversationTurn,
    MemoryManager,
    ConversationMemoryManager
)

from .user_memory import (
    UserMemory,
    LearningHistory,
    LearningSession,
    PreferenceManager,
    UserMemoryManager,
    get_user_memory,
    get_user_preferences
)

from .knowledge_memory import (
    KnowledgeMemory,
    ConceptMemory,
    LearningPathMemory,
    KnowledgeMemoryManager,
    get_knowledge_memory
)

from .constellation_memory import (
    ConstellationMemoryManager,
    ConstellationEffectivenessMetric,
    ConstellationFormationPattern,
    ConstellationSession,
    ConstellationPhase,
    get_constellation_memory
)

__all__ = [
    # Conversation Memory
    "ConversationMemory",
    "ConversationSummary", 
    "ConversationTurn",
    "MemoryManager",
    "ConversationMemoryManager",
    
    # User Memory
    "UserMemory",
    "LearningHistory",
    "LearningSession", 
    "PreferenceManager",
    "UserMemoryManager",
    "get_user_memory",
    "get_user_preferences",
    
    # Knowledge Memory
    "KnowledgeMemory",
    "ConceptMemory",
    "LearningPathMemory",
    "KnowledgeMemoryManager", 
    "get_knowledge_memory",
    
    # Constellation Memory
    "ConstellationMemoryManager",
    "ConstellationEffectivenessMetric",
    "ConstellationFormationPattern",
    "ConstellationSession",
    "ConstellationPhase",
    "get_constellation_memory"
] 