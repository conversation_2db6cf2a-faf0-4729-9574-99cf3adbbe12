#!/usr/bin/env python3
"""
Database Export Script for PyFrameworks Assistant
This script exports data from both ChromaDB vector database and SQLite database.
"""

import os
import json
import sqlite3
import csv
import argparse
import chromadb
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple

def setup_export_directory(export_dir: str) -> Path:
    """Create export directory with timestamp subfolder."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    export_path = Path(export_dir) / f"export_{timestamp}"
    export_path.mkdir(parents=True, exist_ok=True)
    return export_path

def export_sqlite_db(db_path: str, export_path: Path) -> Dict[str, int]:
    """Export SQLite database tables to CSV files."""
    print(f"📊 Exporting SQLite database from {db_path}...")
    
    if not os.path.exists(db_path):
        print(f"❌ Database file not found: {db_path}")
        return {}
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Get all tables
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
    tables = [row[0] for row in cursor.fetchall()]
    
    table_counts = {}
    
    for table in tables:
        try:
            # Get table data
            cursor.execute(f"SELECT * FROM {table};")
            rows = cursor.fetchall()
            
            if not rows:
                print(f"  - Table '{table}' is empty, skipping")
                table_counts[table] = 0
                continue
                
            # Get column names
            cursor.execute(f"PRAGMA table_info({table});")
            columns = [col[1] for col in cursor.fetchall()]
            
            # Write to CSV
            csv_path = export_path / f"{table}.csv"
            with open(csv_path, 'w', newline='', encoding='utf-8') as csv_file:
                writer = csv.writer(csv_file)
                writer.writerow(columns)  # Write header
                writer.writerows(rows)    # Write data
            
            table_counts[table] = len(rows)
            print(f"  ✅ Exported table '{table}' ({len(rows)} rows) to {csv_path}")
            
        except Exception as e:
            print(f"  ❌ Error exporting table '{table}': {e}")
    
    conn.close()
    return table_counts

def export_chroma_db(chroma_path: str, export_path: Path) -> Dict[str, int]:
    """Export ChromaDB collections to JSON and CSV files."""
    print(f"🔍 Exporting ChromaDB from {chroma_path}...")
    
    if not os.path.exists(chroma_path):
        print(f"❌ ChromaDB directory not found: {chroma_path}")
        return {}
    
    try:
        # Initialize ChromaDB client
        client = chromadb.PersistentClient(path=chroma_path)
        
        # Create directory for ChromaDB exports
        chroma_export_path = export_path / "chroma_collections"
        chroma_export_path.mkdir(exist_ok=True)
        
        # List all collections
        collections = client.list_collections()
        print(f"  Found {len(collections)} collections")
        
        collection_counts = {}
        
        for collection in collections:
            try:
                coll_name = collection.name
                count = collection.count()
                collection_counts[coll_name] = count
                
                if count == 0:
                    print(f"  - Collection '{coll_name}' is empty, skipping")
                    continue
                
                # Get all documents from the collection
                results = collection.get(limit=count)
                
                # Create collection directory
                coll_dir = chroma_export_path / coll_name
                coll_dir.mkdir(exist_ok=True)
                
                # Export as JSON
                json_path = coll_dir / f"{coll_name}_data.json"
                with open(json_path, 'w', encoding='utf-8') as f:
                    json.dump(results, f, indent=2)
                
                # Export documents to CSV
                csv_path = coll_dir / f"{coll_name}_documents.csv"
                with open(csv_path, 'w', newline='', encoding='utf-8') as csv_file:
                    writer = csv.writer(csv_file)
                    # Write header
                    writer.writerow(['id', 'document', 'metadata'])
                    
                    # Write data
                    for i in range(len(results['ids'])):
                        doc_id = results['ids'][i]
                        document = results['documents'][i] if i < len(results['documents']) else ""
                        metadata = json.dumps(results['metadatas'][i]) if i < len(results['metadatas']) else "{}"
                        writer.writerow([doc_id, document, metadata])
                
                # Export embeddings to CSV if available
                if 'embeddings' in results and results['embeddings']:
                    emb_path = coll_dir / f"{coll_name}_embeddings.csv"
                    with open(emb_path, 'w', newline='', encoding='utf-8') as csv_file:
                        writer = csv.writer(csv_file)
                        # Write header
                        writer.writerow(['id', 'embedding_dimensions'])
                        
                        # Write data
                        for i in range(len(results['ids'])):
                            doc_id = results['ids'][i]
                            embedding = results['embeddings'][i] if i < len(results['embeddings']) else []
                            dimensions = len(embedding)
                            writer.writerow([doc_id, dimensions])
                
                print(f"  ✅ Exported collection '{coll_name}' ({count} documents) to {coll_dir}")
                
            except Exception as e:
                print(f"  ❌ Error exporting collection '{collection.name}': {e}")
        
        return collection_counts
        
    except Exception as e:
        print(f"❌ Error connecting to ChromaDB: {e}")
        return {}

def export_fallback_storage(storage_path: str, export_path: Path) -> Dict[str, int]:
    """Export fallback storage data to JSON files."""
    print(f"📁 Exporting fallback storage from {storage_path}...")
    
    if not os.path.exists(storage_path):
        print(f"❌ Fallback storage directory not found: {storage_path}")
        return {}
    
    try:
        # Create directory for fallback storage exports
        fallback_export_path = export_path / "fallback_storage"
        fallback_export_path.mkdir(exist_ok=True)
        
        file_counts = {}
        
        # Copy all JSON files from fallback storage
        for root, _, files in os.walk(storage_path):
            for file in files:
                if file.endswith('.json'):
                    src_path = os.path.join(root, file)
                    rel_path = os.path.relpath(src_path, storage_path)
                    dst_dir = fallback_export_path / os.path.dirname(rel_path)
                    dst_dir.mkdir(parents=True, exist_ok=True)
                    dst_path = dst_dir / file
                    
                    try:
                        with open(src_path, 'r', encoding='utf-8') as src_file:
                            data = json.load(src_file)
                            
                        with open(dst_path, 'w', encoding='utf-8') as dst_file:
                            json.dump(data, dst_file, indent=2)
                            
                        category = os.path.dirname(rel_path) or "root"
                        file_counts[category] = file_counts.get(category, 0) + 1
                        print(f"  ✅ Exported {rel_path} to {dst_path}")
                        
                    except Exception as e:
                        print(f"  ❌ Error exporting {rel_path}: {e}")
        
        return file_counts
        
    except Exception as e:
        print(f"❌ Error exporting fallback storage: {e}")
        return {}

def create_summary_report(
    export_path: Path, 
    sqlite_counts: Dict[str, int], 
    chroma_counts: Dict[str, int],
    fallback_counts: Dict[str, int]
) -> None:
    """Create a summary report of the exported data."""
    summary_path = export_path / "export_summary.md"
    
    with open(summary_path, 'w', encoding='utf-8') as f:
        f.write("# PyFrameworks Assistant Data Export Summary\n\n")
        f.write(f"**Export Date:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        # SQLite summary
        f.write("## SQLite Database\n\n")
        if sqlite_counts:
            f.write("| Table | Row Count |\n")
            f.write("|-------|----------:|\n")
            for table, count in sqlite_counts.items():
                f.write(f"| {table} | {count} |\n")
            f.write("\n")
        else:
            f.write("No SQLite data exported.\n\n")
        
        # ChromaDB summary
        f.write("## ChromaDB Vector Database\n\n")
        if chroma_counts:
            f.write("| Collection | Document Count |\n")
            f.write("|-----------|---------------:|\n")
            for collection, count in chroma_counts.items():
                f.write(f"| {collection} | {count} |\n")
            f.write("\n")
        else:
            f.write("No ChromaDB data exported.\n\n")
        
        # Fallback storage summary
        f.write("## Fallback Storage\n\n")
        if fallback_counts:
            f.write("| Category | File Count |\n")
            f.write("|----------|----------:|\n")
            for category, count in fallback_counts.items():
                f.write(f"| {category} | {count} |\n")
            f.write("\n")
        else:
            f.write("No fallback storage data exported.\n\n")
        
        # Export statistics
        total_tables = len(sqlite_counts)
        total_collections = len(chroma_counts)
        total_fallback_categories = len(fallback_counts)
        total_sqlite_rows = sum(sqlite_counts.values())
        total_chroma_docs = sum(chroma_counts.values())
        total_fallback_files = sum(fallback_counts.values())
        
        f.write("## Export Statistics\n\n")
        f.write(f"- **Total SQLite Tables:** {total_tables}\n")
        f.write(f"- **Total SQLite Rows:** {total_sqlite_rows}\n")
        f.write(f"- **Total ChromaDB Collections:** {total_collections}\n")
        f.write(f"- **Total ChromaDB Documents:** {total_chroma_docs}\n")
        f.write(f"- **Total Fallback Categories:** {total_fallback_categories}\n")
        f.write(f"- **Total Fallback Files:** {total_fallback_files}\n")
    
    print(f"📝 Summary report created at {summary_path}")

def main():
    """Main export function."""
    parser = argparse.ArgumentParser(description="Export PyFrameworks Assistant databases")
    parser.add_argument("--export-dir", default="exported_data", help="Directory to store exports")
    parser.add_argument("--sqlite-db", default="data/learning_assistant.db", help="Path to SQLite database")
    parser.add_argument("--chroma-db", default="data/chroma_db", help="Path to ChromaDB directory")
    parser.add_argument("--fallback-storage", default="data/fallback_storage", help="Path to fallback storage directory")
    parser.add_argument("--format", choices=["csv", "json", "both"], default="both", help="Export format")
    args = parser.parse_args()
    
    print("🔍 PyFrameworks Assistant - Database Export Tool")
    print("=" * 60)
    print(f"📅 Export Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Create export directory
    export_path = setup_export_directory(args.export_dir)
    print(f"📁 Exporting data to {export_path}")
    
    # Export SQLite database
    sqlite_counts = export_sqlite_db(args.sqlite_db, export_path)
    
    # Export ChromaDB
    chroma_counts = export_chroma_db(args.chroma_db, export_path)
    
    # Export fallback storage
    fallback_counts = export_fallback_storage(args.fallback_storage, export_path)
    
    # Create summary report
    create_summary_report(export_path, sqlite_counts, chroma_counts, fallback_counts)
    
    print("=" * 60)
    print(f"✅ Data export completed! Files saved to {export_path}")
    print("=" * 60)

if __name__ == "__main__":
    main() 