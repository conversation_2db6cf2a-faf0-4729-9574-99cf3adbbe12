#!/usr/bin/env python3
"""
Data Inspection Script for GAAPF - Guidance AI Agent for Python Framework
This script helps you examine all stored data in the project.
"""

import sqlite3
import json
import os
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any
import chromadb

def inspect_sqlite_database():
    """Inspect the main SQLite database."""
    print("=" * 60)
    print("📊 INSPECTING SQLITE DATABASE")
    print("=" * 60)
    
    db_path = Path("data/learning_assistant.db")
    if not db_path.exists():
        print("❌ Database file not found!")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = [row[0] for row in cursor.fetchall()]
        
        print(f"📋 Found {len(tables)} tables: {', '.join(tables)}\n")
        
        for table in tables:
            print(f"🔍 Table: {table}")
            print("-" * 40)
            
            # Get table schema
            cursor.execute(f"PRAGMA table_info({table});")
            columns = cursor.fetchall()
            print("Columns:")
            for col in columns:
                print(f"  • {col[1]} ({col[2]})")
            
            # Get row count
            cursor.execute(f"SELECT COUNT(*) FROM {table};")
            count = cursor.fetchone()[0]
            print(f"Row count: {count}")
            
            # Show sample data if exists
            if count > 0:
                cursor.execute(f"SELECT * FROM {table} LIMIT 3;")
                rows = cursor.fetchall()
                print("Sample data:")
                for i, row in enumerate(rows, 1):
                    print(f"  Row {i}: {row}")
            
            print()
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error inspecting database: {e}")

def inspect_user_profiles():
    """Inspect user profile JSON files."""
    print("=" * 60)
    print("👤 INSPECTING USER PROFILES")
    print("=" * 60)
    
    profiles_dir = Path("user_profiles")
    if not profiles_dir.exists():
        print("❌ User profiles directory not found!")
        return
    
    profile_files = list(profiles_dir.glob("*.json"))
    print(f"📁 Found {len(profile_files)} user profiles\n")
    
    for profile_file in profile_files:
        try:
            with open(profile_file, 'r') as f:
                profile_data = json.load(f)
            
            print(f"👤 User ID: {profile_data.get('user_id', 'Unknown')}")
            print(f"   Created: {profile_data.get('created_at', 'Unknown')}")
            print(f"   Python Level: {profile_data.get('python_skill_level', 'Unknown')}")
            print(f"   Learning Pace: {profile_data.get('learning_pace', 'Unknown')}")
            print(f"   Current Framework: {profile_data.get('current_framework', 'None')}")
            print(f"   Total Learning Time: {profile_data.get('total_learning_time', 0)} hours")
            print(f"   Completed Modules: {len(profile_data.get('completed_modules', []))}")
            print(f"   Recent Sessions: {len(profile_data.get('recent_sessions', []))}")
            
            # Show recent sessions if any
            recent_sessions = profile_data.get('recent_sessions', [])
            if recent_sessions:
                print("   Latest Sessions:")
                for session in recent_sessions[-2:]:  # Show last 2 sessions
                    print(f"     • {session.get('session_id', 'Unknown')} ({session.get('framework', 'Unknown')})")
                    print(f"       Completion: {session.get('completion_percentage', 0)}%")
            
            print()
            
        except Exception as e:
            print(f"❌ Error reading {profile_file}: {e}")

def inspect_chroma_db():
    """Inspect ChromaDB vector storage."""
    print("=" * 60)
    print("🔍 INSPECTING CHROMADB VECTOR STORAGE")
    print("=" * 60)
    
    chroma_path = Path("data/chroma_db")
    if not chroma_path.exists():
        print("❌ ChromaDB directory not found!")
        return
    
    try:
        # Initialize ChromaDB client
        client = chromadb.PersistentClient(path=str(chroma_path))
        
        # List all collections
        collections = client.list_collections()
        print(f"📚 Found {len(collections)} collections\n")
        
        for collection in collections:
            print(f"📂 Collection: {collection.name}")
            print(f"   ID: {collection.id}")
            
            # Get collection details
            count = collection.count()
            print(f"   Document count: {count}")
            
            if count > 0:
                # Get sample documents
                results = collection.get(limit=3)
                print("   Sample documents:")
                for i, (doc_id, document, metadata) in enumerate(zip(
                    results['ids'], 
                    results['documents'], 
                    results['metadatas']
                ), 1):
                    print(f"     Doc {i}: {doc_id}")
                    print(f"       Content: {document[:100]}...")
                    print(f"       Metadata: {metadata}")
            
            print()
            
    except Exception as e:
        print(f"❌ Error inspecting ChromaDB: {e}")

def inspect_file_structure():
    """Inspect overall file structure and sizes."""
    print("=" * 60)
    print("📁 FILE STRUCTURE OVERVIEW")
    print("=" * 60)
    
    data_dirs = ['data', 'user_profiles', 'generated_code', 'monitoring_data']
    
    for dir_name in data_dirs:
        dir_path = Path(dir_name)
        if dir_path.exists():
            files = list(dir_path.rglob('*'))
            total_size = sum(f.stat().st_size for f in files if f.is_file())
            file_count = len([f for f in files if f.is_file()])
            
            print(f"📂 {dir_name}/")
            print(f"   Files: {file_count}")
            print(f"   Total size: {total_size / 1024:.1f} KB")
            
            # List files
            for file_path in sorted([f for f in files if f.is_file()])[:10]:  # Show first 10 files
                size_kb = file_path.stat().st_size / 1024
                print(f"   • {file_path.relative_to(dir_path)} ({size_kb:.1f} KB)")
            
            if file_count > 10:
                print(f"   ... and {file_count - 10} more files")
            
            print()

def inspect_configuration_data():
    """Inspect configuration and framework data."""
    print("=" * 60)
    print("⚙️  CONFIGURATION DATA")
    print("=" * 60)
    
    try:
        # Import configuration modules
        import sys
        sys.path.append('src')
        
        from pyframeworks_assistant.config.framework_configs import (
            SupportedFrameworks, 
            get_all_frameworks,
            get_framework_config
        )
        
        frameworks = get_all_frameworks()
        print(f"🔧 Supported Frameworks: {len(frameworks)}")
        
        for framework in frameworks:
            config = get_framework_config(framework)
            print(f"   • {config.display_name} (v{config.latest_version})")
            print(f"     Use cases: {len(config.primary_use_cases)}")
            print(f"     Learning paths: {len(config.learning_paths)}")
            print(f"     Complexity: {config.learning_complexity}")
        
        print()
        
    except Exception as e:
        print(f"❌ Error inspecting configuration: {e}")

def main():
    """Main inspection function."""
    print("🔍 GAAPF - Guidance AI Agent - Data Inspection")
    print("=" * 60)
    print(f"📅 Inspection Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Run all inspections
    inspect_file_structure()
    inspect_sqlite_database()
    inspect_user_profiles()
    inspect_chroma_db()
    inspect_configuration_data()
    
    print("=" * 60)
    print("✅ Data inspection completed!")
    print("=" * 60)

if __name__ == "__main__":
    main()