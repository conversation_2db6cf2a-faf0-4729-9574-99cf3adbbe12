"""
File management tools for the GAAPF - Guidance AI Agent for Python Framework.
Provides safe code file creation and management capabilities with enhanced interactive practice features.
"""

import os
import re
import uuid
import subprocess
import sys
import json
import pkg_resources
from pathlib import Path
from typing import List, Dict, Optional, Tuple, Any
from datetime import datetime
from langchain.tools import tool
from pydantic import BaseModel, Field
from dataclasses import dataclass, asdict


class CodeBlock(BaseModel):
    """Represents a code block found in text."""
    language: str = Field(description="Programming language of the code")
    content: str = Field(description="The actual code content")
    filename: Optional[str] = Field(default=None, description="Suggested filename")


class FileCreationResult(BaseModel):
    """Result of file creation operation."""
    success: bool = Field(description="Whether the operation was successful")
    filepath: str = Field(description="Path to the created file")
    message: str = Field(description="Status message")


@dataclass
class PracticeSession:
    """Track a user's interactive practice session."""
    session_id: str
    user_id: str
    framework: str
    task_description: str
    generated_files: List[str]
    dependencies_installed: List[str]
    start_time: str
    end_time: Optional[str] = None
    execution_attempts: List[Dict] = None
    errors_encountered: List[Dict] = None
    success_rate: float = 0.0
    user_modifications: List[Dict] = None
    
    def __post_init__(self):
        if self.execution_attempts is None:
            self.execution_attempts = []
        if self.errors_encountered is None:
            self.errors_encountered = []
        if self.user_modifications is None:
            self.user_modifications = []


class DependencyManager:
    """Smart dependency management for AI learning system."""
    
    def __init__(self):
        self.conda_available = self._check_conda_available()
        self.venv_path = self._detect_virtual_env()
        self.pip_available = self._check_pip_available()
        
    def _check_conda_available(self) -> bool:
        """Check if conda is available."""
        try:
            subprocess.run(['conda', '--version'], capture_output=True, check=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            return False
    
    def _check_pip_available(self) -> bool:
        """Check if pip is available."""
        try:
            subprocess.run([sys.executable, '-m', 'pip', '--version'], capture_output=True, check=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            return False
    
    def _detect_virtual_env(self) -> Optional[str]:
        """Detect current virtual environment."""
        if 'CONDA_DEFAULT_ENV' in os.environ:
            return os.environ['CONDA_DEFAULT_ENV']
        elif 'VIRTUAL_ENV' in os.environ:
            return os.environ['VIRTUAL_ENV']
        return None
    
    def extract_dependencies_from_code(self, code: str, language: str = "python") -> List[str]:
        """Extract dependencies from code using intelligent parsing."""
        if language.lower() != "python":
            return []
            
        dependencies = []
        lines = code.split('\n')
        
        # Framework-specific packages to add based on imports
        framework_packages = {
            'langchain': ['langchain'],
            'langchain_core': ['langchain-core'],
            'langchain_openai': ['langchain-openai'],
            'langchain_anthropic': ['langchain-anthropic'],
            'langchain_google_genai': ['langchain-google-genai'],
            'langgraph': ['langgraph'],
            'crewai': ['crewai'],
            'autogen': ['pyautogen'],
            'openai': ['openai']
        }
        
        for line in lines:
            line = line.strip()
            # Standard imports
            if line.startswith('import ') or line.startswith('from '):
                if 'import' in line:
                    # Extract package name
                    if line.startswith('from '):
                        pkg = line.split('from ')[1].split(' import')[0].strip()
                    else:
                        pkg = line.split('import ')[1].split(' as')[0].split(',')[0].strip()
                    
                    # Map common packages to pip names
                    pkg_mapping = {
                        'cv2': 'opencv-python',
                        'PIL': 'Pillow',
                        'sklearn': 'scikit-learn',
                        'yaml': 'PyYAML',
                        'bs4': 'beautifulsoup4',
                        'langchain': 'langchain',
                        'langchain_openai': 'langchain-openai',
                        'langchain_anthropic': 'langchain-anthropic',
                        'langchain_google_genai': 'langchain-google-genai',
                        'fastapi': 'fastapi',
                        'streamlit': 'streamlit',
                        'uvicorn': 'uvicorn',
                        'requests': 'requests',
                        'pandas': 'pandas',
                        'numpy': 'numpy',
                        'matplotlib': 'matplotlib',
                        'seaborn': 'seaborn',
                        'plotly': 'plotly'
                    }
                    
                    # Check for framework-specific packages
                    if pkg in framework_packages:
                        for framework_pkg in framework_packages[pkg]:
                            dependencies.append(framework_pkg)
                    
                    # Map to standard pip name if applicable
                    pkg_pip_name = pkg_mapping.get(pkg, pkg)
                    
                    if self._is_third_party_package(pkg):
                        dependencies.append(pkg_pip_name)
        
        return list(set(dependencies))
    
    def _is_third_party_package(self, package_name: str) -> bool:
        """Check if package is third-party (not built-in)."""
        builtin_packages = {
            'os', 'sys', 'json', 'datetime', 'time', 'random', 'math', 
            'collections', 'itertools', 'functools', 're', 'pathlib',
            'typing', 'dataclasses', 'enum', 'abc', 'asyncio', 'sqlite3',
            'urllib', 'email', 'html', 'xml', 'http', 'socket', 'ssl'
        }
        
        # Handle package with submodules (e.g. langchain.llms)
        base_package = package_name.split('.')[0]
        
        return base_package not in builtin_packages
    
    def check_dependencies_installed(self, dependencies: List[str]) -> Tuple[List[str], List[str]]:
        """Check which dependencies are installed vs missing."""
        installed = []
        missing = []
        
        for dep in dependencies:
            try:
                pkg_resources.get_distribution(dep)
                installed.append(dep)
            except pkg_resources.DistributionNotFound:
                missing.append(dep)
        
        return installed, missing
    
    def install_dependencies(self, dependencies: List[str]) -> Dict[str, bool]:
        """Install missing dependencies automatically."""
        results = {}
        
        if not dependencies:
            return results
            
        # First, check current environment and permissions
        can_install = self._can_install_packages()
        if not can_install:
            return {dep: False for dep in dependencies}
        
        for dep in dependencies:
            try:
                if self.conda_available and self.venv_path:
                    # Try conda first in a detected conda environment
                    result = subprocess.run(
                        ['conda', 'install', '-y', dep], 
                        capture_output=True, 
                        text=True
                    )
                    if result.returncode != 0:
                        # Fallback to pip
                        result = subprocess.run(
                            [sys.executable, '-m', 'pip', 'install', dep],
                            capture_output=True,
                            text=True
                        )
                else:
                    # Use pip directly
                    result = subprocess.run(
                        [sys.executable, '-m', 'pip', 'install', dep],
                        capture_output=True,
                        text=True
                    )
                
                results[dep] = result.returncode == 0
                
            except Exception as e:
                results[dep] = False
        
        return results
        
    def _can_install_packages(self) -> bool:
        """Check if we can install packages in the current environment."""
        # Check if running in a virtual environment
        in_virtualenv = self.venv_path is not None
        
        # If we're in a virtualenv, we assume we can install packages
        if in_virtualenv:
            return True
            
        # For system Python, try to install in user space
        if self.pip_available:
            try:
                # Test if we can install in user mode
                test_result = subprocess.run(
                    [sys.executable, '-m', 'pip', 'install', '--user', '--dry-run', 'pip'],
                    capture_output=True,
                    text=True
                )
                return test_result.returncode == 0
            except:
                return False
                
        return False


class InteractivePracticeManager:
    """Manages interactive coding practice sessions."""
    
    def __init__(self, practice_dir: str = "practice_sessions"):
        self.practice_dir = Path(practice_dir)
        self.practice_dir.mkdir(exist_ok=True)
        self.sessions_file = self.practice_dir / "sessions.json"
        self.dependency_manager = DependencyManager()
        
    def create_practice_session(self, user_id: str, framework: str, task_description: str) -> PracticeSession:
        """Create a new practice session."""
        # Generate a unique session ID
        session_id = str(uuid.uuid4())
        
        # Create a new session
        session = PracticeSession(
            session_id=session_id,
            user_id=user_id,
            framework=framework,
            task_description=task_description,
            generated_files=[],
            dependencies_installed=[],
            start_time=datetime.now().isoformat(),
            end_time=None,
            execution_attempts=[],
            errors_encountered=[],
            success_rate=0.0,
            user_modifications=[]
        )
        
        # Create session directory
        session_dir = self.practice_dir / session_id
        session_dir.mkdir(exist_ok=True)
        
        # Save session
        self.save_session(session)
        
        return session
        
    def save_session(self, session: PracticeSession):
        """Save a practice session."""
        all_sessions = self.load_all_sessions()
        all_sessions[session.session_id] = asdict(session)
        
        with open(self.sessions_file, 'w') as f:
            json.dump(all_sessions, f, indent=2)
        
    def load_all_sessions(self) -> Dict[str, Any]:
        """Load all practice sessions."""
        if not self.sessions_file.exists():
            return {}
            
        try:
            with open(self.sessions_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            return {}
    
    def create_practice_file(self, session: PracticeSession, filename: str, content: str, language: str = "python") -> str:
        """Create a practice file within a session."""
        # Create session directory
        session_dir = self.practice_dir / session.session_id
        session_dir.mkdir(exist_ok=True)
        
        # Ensure filename has extension
        if language == "python" and not filename.lower().endswith(".py"):
            filename = f"{filename}.py"
        
        # Create full file path
        file_path = session_dir / filename
        
        # Write content to file
        with open(file_path, 'w') as f:
            f.write(content)
        
        # Update session
        if file_path.name not in session.generated_files:
            session.generated_files.append(file_path.name)
            
        # Extract and install dependencies
        dependencies = self.dependency_manager.extract_dependencies_from_code(content, language)
        if dependencies:
            installed, missing = self.dependency_manager.check_dependencies_installed(dependencies)
            if missing:
                install_results = self.dependency_manager.install_dependencies(missing)
                for dep, success in install_results.items():
                    if success and dep not in session.dependencies_installed:
                        session.dependencies_installed.append(dep)
            
            # Add already installed dependencies to the list
            for dep in installed:
                if dep not in session.dependencies_installed:
                    session.dependencies_installed.append(dep)
        
        # Save updated session
        self.save_session(session)
        
        return str(file_path)
    
    def execute_practice_file(self, session: PracticeSession, file_path: str) -> Dict[str, Any]:
        """Execute a practice file and capture results."""
        file_path = Path(file_path)
        
        # Make sure session directory exists
        session_dir = self.practice_dir / session.session_id
        session_dir.mkdir(exist_ok=True)
        
        result = {
            "success": False,
            "output": "",
            "error": "",
            "execution_time": 0,
            "timestamp": datetime.now().isoformat()
        }
        
        # Track execution attempt
        execution_attempt = {
            "file": file_path.name,
            "timestamp": datetime.now().isoformat(),
            "success": False
        }
        
        # Check if file exists
        if not file_path.exists():
            result["error"] = f"File {file_path} not found"
            execution_attempt["error"] = result["error"]
            session.execution_attempts.append(execution_attempt)
            session.errors_encountered.append({
                "file": file_path.name,
                "error": result["error"],
                "timestamp": execution_attempt["timestamp"]
            })
            self.save_session(session)
            return result
        
        # Check file extension
        if file_path.suffix != ".py":
            result["error"] = f"Only Python files can be executed (file has extension {file_path.suffix})"
            execution_attempt["error"] = result["error"]
            session.execution_attempts.append(execution_attempt)
            session.errors_encountered.append({
                "file": file_path.name,
                "error": result["error"],
                "timestamp": execution_attempt["timestamp"]
            })
            self.save_session(session)
            return result
        
        # Execute Python file using subprocess
        try:
            start_time = datetime.now()
            process = subprocess.run(
                [sys.executable, str(file_path)],
                capture_output=True, 
                text=True, 
                timeout=30  # 30 second timeout
            )
            end_time = datetime.now()
            
            if process.returncode == 0:
                result["success"] = True
                result["output"] = process.stdout
                execution_attempt["success"] = True
                execution_attempt["output"] = process.stdout[:1000]  # Save first 1000 chars only
            else:
                result["success"] = False
                result["error"] = process.stderr
                execution_attempt["error"] = process.stderr
                
                # Track error
                session.errors_encountered.append({
                    "file": file_path.name,
                    "error": process.stderr,
                    "timestamp": execution_attempt["timestamp"]
                })
            
            execution_time = (end_time - start_time).total_seconds()
            result["execution_time"] = execution_time
            execution_attempt["execution_time"] = execution_time
            
        except subprocess.TimeoutExpired:
            result["error"] = "Execution timed out (30 seconds)"
            execution_attempt["error"] = result["error"]
            session.errors_encountered.append({
                "file": file_path.name,
                "error": result["error"],
                "timestamp": execution_attempt["timestamp"]
            })
            
        except Exception as e:
            result["error"] = f"Execution error: {str(e)}"
            execution_attempt["error"] = result["error"]
            session.errors_encountered.append({
                "file": file_path.name,
                "error": result["error"],
                "timestamp": execution_attempt["timestamp"]
            })
        
        # Update session
        session.execution_attempts.append(execution_attempt)
        session.success_rate = self.calculate_success_rate(session)
        self.save_session(session)
        
        return result
        
    def read_practice_file(self, session_id: str, filename: str) -> str:
        """Read the content of a practice file."""
        # Create session directory path
        session_dir = self.practice_dir / session_id
        
        # Handle filename with or without extension
        if not filename.lower().endswith(".py"):
            file_path = session_dir / f"{filename}.py"
            if not file_path.exists():
                # Try without extension
                file_path = session_dir / filename
        else:
            file_path = session_dir / filename
        
        if not file_path.exists():
            return f"File {filename} not found in session {session_id}"
        
        try:
            with open(file_path, 'r') as f:
                return f.read()
        except Exception as e:
            return f"Error reading file: {str(e)}"
    
    def delete_practice_file(self, session_id: str, filename: str) -> bool:
        """Delete a practice file."""
        # Create session directory path
        session_dir = self.practice_dir / session_id
        
        # Handle filename with or without extension
        if not filename.lower().endswith(".py"):
            file_path = session_dir / f"{filename}.py"
            if not file_path.exists():
                # Try without extension
                file_path = session_dir / filename
        else:
            file_path = session_dir / filename
        
        if not file_path.exists():
            return False
        
        try:
            file_path.unlink()
            
            # Update session if exists
            all_sessions = self.load_all_sessions()
            if session_id in all_sessions:
                session_data = all_sessions[session_id]
                if "generated_files" in session_data and file_path.name in session_data["generated_files"]:
                    session_data["generated_files"].remove(file_path.name)
                    all_sessions[session_id] = session_data
                    with open(self.sessions_file, 'w') as f:
                        json.dump(all_sessions, f, indent=2)
            
            return True
        except Exception as e:
            return False
    
    def list_practice_files(self, session_id: str) -> List[str]:
        """List all practice files in a session."""
        session_dir = self.practice_dir / session_id
        
        if not session_dir.exists():
            return []
            
        return [file.name for file in session_dir.glob("*.*")]
    
    def calculate_success_rate(self, session: PracticeSession) -> float:
        """Calculate success rate based on execution attempts."""
        if not session.execution_attempts:
            return 0.0
        
        successful_attempts = sum(1 for attempt in session.execution_attempts if attempt.get("success", False))
        return (successful_attempts / len(session.execution_attempts)) * 100
    
    def get_practice_summary(self, session: PracticeSession) -> Dict[str, Any]:
        """Get a summary of the practice session."""
        return {
            "session_id": session.session_id,
            "framework": session.framework,
            "task_description": session.task_description,
            "files_created": session.generated_files,
            "execution_attempts": len(session.execution_attempts),
            "successful_executions": sum(1 for attempt in session.execution_attempts if attempt.get("success", False)),
            "errors_encountered": len(session.errors_encountered),
            "success_rate": session.success_rate,
            "dependencies_installed": session.dependencies_installed,
            "duration_minutes": self._calculate_duration(session),
            "active": session.end_time is None
        }
    
    def end_session(self, session_id: str) -> bool:
        """End a practice session."""
        all_sessions = self.load_all_sessions()
        
        if session_id not in all_sessions:
            return False
            
        session_data = all_sessions[session_id]
        session_data["end_time"] = datetime.now().isoformat()
        all_sessions[session_id] = session_data
        
        with open(self.sessions_file, 'w') as f:
            json.dump(all_sessions, f, indent=2)
            
        return True
    
    def _calculate_duration(self, session: PracticeSession) -> float:
        """Calculate session duration in minutes."""
        start_time = datetime.fromisoformat(session.start_time)
        
        if session.end_time:
            end_time = datetime.fromisoformat(session.end_time)
        else:
            end_time = datetime.now()
            
        duration = end_time - start_time
        return duration.total_seconds() / 60


class CodeFileManager:
    """Manages code file creation and organization."""
    
    def __init__(self, base_dir: str = "generated_code"):
        """Initialize the code file manager."""
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(exist_ok=True)
        
        # Safe file extensions
        self.safe_extensions = {
            'python': '.py',
            'py': '.py',
            'javascript': '.js',
            'js': '.js',
            'typescript': '.ts',
            'ts': '.ts',
            'html': '.html',
            'css': '.css',
            'json': '.json',
            'yaml': '.yaml',
            'yml': '.yml',
            'markdown': '.md',
            'md': '.md',
            'txt': '.txt',
            'sql': '.sql',
            'bash': '.sh',
            'shell': '.sh',
            'dockerfile': '.dockerfile',
            'requirements': '.txt'
        }
    
    def extract_code_blocks(self, text: str) -> List[CodeBlock]:
        """Extract code blocks from text."""
        code_blocks = []
        
        # Match code blocks with triple backticks
        # Look for ```language [filename] (optional)
        # pattern = r"```(\w+)(?:\s+([^\s]+))?\n(.*?)```"
        pattern = r"```(\w+)(?:\s+([^\n]+))?\n(.*?)```"
        matches = re.finditer(pattern, text, re.DOTALL)
        
        for match in matches:
            language = match.group(1)
            filename_info = match.group(2)
            content = match.group(3).strip()
            
            # Extract filename from the filename_info if available
            filename = None
            if filename_info:
                # Check for common filename patterns
                if '.' in filename_info and not filename_info.startswith('http'):
                    # This looks like a filename
                    filename = filename_info.strip()
                elif ':' in filename_info:
                    # This might be a "filename:" format
                    potential_filename = filename_info.split(':')[0].strip()
                    if '.' in potential_filename:
                        filename = potential_filename
            
            code_blocks.append(
                CodeBlock(
                    language=language,
                    content=content,
                    filename=filename
                )
            )
        
        # Also look for inline code filenames in the format: `filename.py`
        if not code_blocks:
            # Look for file content descriptions
            file_content_pattern = r"[Cc]ontent(?:s)? of (?:the )?file[:\s]+[`\"']([^`\"']+)[`\"']"
            file_matches = re.finditer(file_content_pattern, text)
            for file_match in file_matches:
                filename = file_match.group(1).strip()
                # Try to find code block after this mention
                code_block_after = re.search(r"```(\w+)\n(.*?)```", text[file_match.end():], re.DOTALL)
                if code_block_after:
                    language = code_block_after.group(1)
                    content = code_block_after.group(2).strip()
                    code_blocks.append(
                        CodeBlock(
                            language=language,
                            content=content,
                            filename=filename
                        )
                    )
        
        return code_blocks
    
    def generate_filename(self, code_block: CodeBlock, context: str = "") -> str:
        """
        Generate a meaningful filename for a code block.
        
        Args:
            code_block: The code block to generate filename for
            context: Additional context for filename generation
            
        Returns:
            Generated filename
        """
        # Get file extension
        extension = self.safe_extensions.get(code_block.language, '.txt')
        
        # Try to extract meaningful name from code content
        content_lower = code_block.content.lower()
        
        # Look for class names
        class_match = re.search(r'class\s+(\w+)', code_block.content)
        if class_match:
            return f"{class_match.group(1).lower()}{extension}"
        
        # Look for function names
        func_match = re.search(r'def\s+(\w+)', code_block.content)
        if func_match:
            return f"{func_match.group(1).lower()}{extension}"
        
        # Look for main patterns
        if 'if __name__' in content_lower:
            return f"main{extension}"
        
        # Use context if available
        if context:
            # Clean context for filename
            clean_context = re.sub(r'[^\w\s-]', '', context.lower())
            clean_context = re.sub(r'\s+', '_', clean_context)[:20]
            return f"{clean_context}{extension}"
        
        # Default with timestamp
        timestamp = datetime.now().strftime("%H%M%S")
        return f"code_{timestamp}{extension}"
    
    def create_file(self, code_block: CodeBlock, filename: Optional[str] = None, 
                   subdirectory: Optional[str] = None) -> FileCreationResult:
        """
        Create a file from a code block.
        
        Args:
            code_block: The code block to save
            filename: Optional custom filename
            subdirectory: Optional subdirectory to organize files
            
        Returns:
            FileCreationResult with operation details
        """
        try:
            # Determine target directory
            target_dir = self.base_dir
            if subdirectory:
                target_dir = target_dir / subdirectory
                target_dir.mkdir(exist_ok=True)
            
            # Generate filename if not provided
            if not filename:
                filename = self.generate_filename(code_block)
            
            # Ensure safe filename
            filename = self._sanitize_filename(filename)
            
            # Check if file already exists and create unique name if needed
            filepath = target_dir / filename
            counter = 1
            original_stem = filepath.stem
            while filepath.exists():
                filepath = target_dir / f"{original_stem}_{counter}{filepath.suffix}"
                counter += 1
            
            # Write file
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(code_block.content)
            
            return FileCreationResult(
                success=True,
                filepath=str(filepath),
                message=f"✅ Code file created: {filepath.name}"
            )
            
        except Exception as e:
            return FileCreationResult(
                success=False,
                filepath="",
                message=f"❌ Failed to create file: {e}"
            )
    
    def _sanitize_filename(self, filename: str) -> str:
        """Sanitize filename to be safe for filesystem."""
        # Remove or replace unsafe characters
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        # Limit length
        if len(filename) > 100:
            name, ext = os.path.splitext(filename)
            filename = name[:90] + ext
        return filename
    
    def list_files(self, subdirectory: Optional[str] = None) -> List[str]:
        """List all generated files."""
        target_dir = self.base_dir
        if subdirectory:
            target_dir = target_dir / subdirectory
        
        if not target_dir.exists():
            return []
        
        return [f.name for f in target_dir.iterdir() if f.is_file()]
    
    def read_file(self, filename: str, subdirectory: Optional[str] = None) -> Optional[str]:
        """Read content of a generated file."""
        target_dir = self.base_dir
        if subdirectory:
            target_dir = target_dir / subdirectory
        
        filepath = target_dir / filename
        if filepath.exists():
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    return f.read()
            except Exception:
                return None
        return None
    
    def delete_file(self, filename: str, subdirectory: Optional[str] = None) -> bool:
        """Delete a generated file."""
        target_dir = self.base_dir
        if subdirectory:
            target_dir = target_dir / subdirectory
        
        filepath = target_dir / filename
        if filepath.exists():
            try:
                filepath.unlink()
                return True
            except Exception:
                return False
        return False


# Global managers
file_manager = CodeFileManager()
dependency_manager = DependencyManager()
practice_manager = InteractivePracticeManager()


@tool
def create_code_file(content: str, filename: str, language: str = "python") -> str:
    """
    Create a code file with the specified content.
    
    Args:
        content: The code content to write to the file
        filename: Name of the file to create
        language: Programming language (python, javascript, etc.)
        
    Returns:
        Status message about file creation
    """
    code_block = CodeBlock(
        language=language.lower(),
        content=content,
        filename=filename
    )
    
    result = file_manager.create_file(code_block, filename)
    return result.message


@tool
def auto_create_files_from_text(text: str, context: str = "") -> str:
    """
    Automatically extract and create files from code blocks in text.
    
    Args:
        text: Text containing code blocks
        context: Optional context to help with filename generation
        
    Returns:
        Summary of created files
    """
    file_manager = CodeFileManager()
    code_blocks = file_manager.extract_code_blocks(text)
    
    if not code_blocks:
        return "No code blocks found in the provided text."
    
    results = []
    for i, block in enumerate(code_blocks):
        # Generate filename if not already extracted
        if not block.filename:
            filename = file_manager.generate_filename(block, context)
        else:
            filename = block.filename
            
        # Create the file
        result = file_manager.create_file(block, filename)
        
        # Add to results
        if result.success:
            results.append(f"✅ Created file: {result.filepath}")
        else:
            results.append(f"❌ Failed to create file: {result.message}")
    
    if results:
        return "\n".join(results)
    else:
        return "No files were created from the provided text."


@tool
def list_generated_files(subdirectory: str = "") -> str:
    """
    List all generated code files.
    
    Args:
        subdirectory: Optional subdirectory to list files from
        
    Returns:
        List of generated files
    """
    files = file_manager.list_files(subdirectory if subdirectory else None)
    
    if not files:
        return "No generated files found."
    
    file_list = "📂 Generated files:\n"
    for i, filename in enumerate(files, 1):
        file_list += f"  {i}. {filename}\n"
    
    return file_list


@tool
def read_generated_file(filename: str, subdirectory: str = "") -> str:
    """
    Read the content of a generated file.
    
    Args:
        filename: Name of the file to read
        subdirectory: Optional subdirectory where the file is located
        
    Returns:
        File content or error message
    """
    content = file_manager.read_file(filename, subdirectory if subdirectory else None)
    
    if content is None:
        return f"❌ File '{filename}' not found."
    
    return f"📄 Content of {filename}:\n\n```\n{content}\n```"


@tool
def delete_generated_file(filename: str, subdirectory: str = "") -> str:
    """
    Delete a generated file.
    
    Args:
        filename: Name of the file to delete
        subdirectory: Optional subdirectory where the file is located
        
    Returns:
        Status message
    """
    success = file_manager.delete_file(filename, subdirectory if subdirectory else None)
    
    if success:
        return f"✅ File '{filename}' deleted successfully."
    else:
        return f"❌ Failed to delete file '{filename}' or file not found."


@tool
def check_and_install_dependencies(code: str, language: str = "python") -> str:
    """
    Automatically check and install dependencies for given code.
    
    Args:
        code: The code to analyze for dependencies
        language: Programming language (default: python)
        
    Returns:
        Installation status and results
    """
    try:
        # Extract dependencies
        dependencies = dependency_manager.extract_dependencies_from_code(code, language)
        
        if not dependencies:
            return "✅ No external dependencies found in the code."
        
        # Check which are installed vs missing
        installed, missing = dependency_manager.check_dependencies_installed(dependencies)
        
        result_lines = []
        result_lines.append(f"📦 **Dependency Analysis for {language.upper()} code:**")
        
        if installed:
            result_lines.append(f"✅ **Already installed:** {', '.join(installed)}")
        
        if missing:
            result_lines.append(f"⚠️ **Missing dependencies:** {', '.join(missing)}")
            result_lines.append("🔧 **Installing missing dependencies...**")
            
            # Install missing dependencies
            install_results = dependency_manager.install_dependencies(missing)
            
            successful = []
            failed = []
            
            for dep, success in install_results.items():
                if success:
                    successful.append(dep)
                else:
                    failed.append(dep)
            
            if successful:
                result_lines.append(f"✅ **Successfully installed:** {', '.join(successful)}")
            
            if failed:
                result_lines.append(f"❌ **Failed to install:** {', '.join(failed)}")
                result_lines.append("💡 **Tip:** You may need to install these manually")
        
        return "\n".join(result_lines)
        
    except Exception as e:
        return f"❌ Error checking dependencies: {e}"


@tool
def start_practice_session(user_id: str, framework: str, task_description: str) -> str:
    """
    Start a new interactive practice session.
    
    Args:
        user_id: Unique identifier for the user
        framework: Framework being practiced (e.g., 'FastAPI', 'LangChain')
        task_description: Description of the practice task
        
    Returns:
        Session details and next steps
    """
    try:
        session = practice_manager.create_practice_session(user_id, framework, task_description)
        practice_manager.save_session(session)
        
        return f"""🎯 **Practice Session Started!**

**Session ID:** {session.session_id}
**Framework:** {framework}
**Task:** {task_description}
**Started:** {session.start_time}

📁 **Session Directory:** `practice_sessions/session_{session.session_id}/`

**What happens next:**
1. I'll create practice files automatically 
2. Install any required dependencies
3. Execute and test your code
4. Track your progress and provide feedback
5. Help you fix errors and improve

Ready to start coding! 🚀"""
        
    except Exception as e:
        return f"❌ Failed to start practice session: {e}"


@tool
def create_practice_file_with_auto_deps(session_id: str, filename: str, code: str, language: str = "python") -> str:
    """
    Create a practice file with automatic dependency installation.
    
    Args:
        session_id: ID of the practice session
        filename: Name for the practice file
        code: Code content to save
        language: Programming language
        
    Returns:
        File creation and dependency status
    """
    try:
        # Load session
        sessions = practice_manager.load_all_sessions()
        if session_id not in sessions:
            return f"❌ Practice session {session_id} not found. Start a new session first."
        
        session_data = sessions[session_id]
        session = PracticeSession(**session_data)
        
        # Check and install dependencies first
        dependencies = dependency_manager.extract_dependencies_from_code(code, language)
        install_results = {}
        
        if dependencies:
            installed, missing = dependency_manager.check_dependencies_installed(dependencies)
            if missing:
                install_results = dependency_manager.install_dependencies(missing)
                session.dependencies_installed.extend([dep for dep, success in install_results.items() if success])
        
        # Create the practice file
        file_path = practice_manager.create_practice_file(session, filename, code, language)
        
        result_lines = []
        result_lines.append(f"📄 **Practice file created:** `{filename}`")
        result_lines.append(f"📁 **Location:** `{file_path}`")
        
        if dependencies:
            result_lines.append(f"\n📦 **Dependencies processed:** {', '.join(dependencies)}")
            if install_results:
                successful = [dep for dep, success in install_results.items() if success]
                failed = [dep for dep, success in install_results.items() if not success]
                
                if successful:
                    result_lines.append(f"✅ **Installed:** {', '.join(successful)}")
                if failed:
                    result_lines.append(f"❌ **Failed:** {', '.join(failed)}")
        
        result_lines.append(f"\n🎯 **Next step:** Use `execute_practice_file` to run and test your code!")
        
        return "\n".join(result_lines)
        
    except Exception as e:
        return f"❌ Failed to create practice file: {e}"


@tool
def execute_practice_file(session_id: str, filename: str) -> str:
    """
    Execute a practice file and track results.
    
    Args:
        session_id: ID of the practice session
        filename: Name of the file to execute
        
    Returns:
        Execution results and feedback
    """
    try:
        # Load session
        sessions = practice_manager.load_all_sessions()
        if session_id not in sessions:
            return f"❌ Practice session {session_id} not found."
        
        session_data = sessions[session_id]
        session = PracticeSession(**session_data)
        
        # Find file path
        session_dir = Path("practice_sessions") / f"session_{session_id}"
        file_path = session_dir / filename
        
        if not file_path.exists():
            return f"❌ File `{filename}` not found in session {session_id}"
        
        # Execute the file
        execution_result = practice_manager.execute_practice_file(session, str(file_path))
        
        result_lines = []
        result_lines.append(f"🚀 **Execution Results for `{filename}`:**")
        result_lines.append(f"⏱️ **Execution time:** {execution_result['execution_time']:.2f} seconds")
        
        if execution_result['success']:
            result_lines.append("✅ **Status:** SUCCESS")
            if execution_result['output']:
                result_lines.append(f"📤 **Output:**\n```\n{execution_result['output']}\n```")
            else:
                result_lines.append("📤 **Output:** No output (code ran silently)")
        else:
            result_lines.append("❌ **Status:** FAILED")
            if execution_result['error']:
                result_lines.append(f"🐛 **Error:**\n```\n{execution_result['error']}\n```")
                
                # Provide helpful error guidance
                error_text = execution_result['error'].lower()
                if 'modulenotfounderror' in error_text:
                    result_lines.append("💡 **Suggestion:** Missing dependency. Use `check_and_install_dependencies` tool.")
                elif 'syntaxerror' in error_text:
                    result_lines.append("💡 **Suggestion:** Check your code syntax. Look for missing colons, brackets, or indentation issues.")
                elif 'indentationerror' in error_text:
                    result_lines.append("💡 **Suggestion:** Fix indentation. Python requires consistent indentation.")
        
        # Calculate and show progress
        success_rate = practice_manager.calculate_success_rate(session)
        result_lines.append(f"\n📊 **Session Progress:** {success_rate:.1f}% success rate")
        result_lines.append(f"🎯 **Total attempts:** {len(session.execution_attempts)}")
        
        return "\n".join(result_lines)
        
    except Exception as e:
        return f"❌ Failed to execute practice file: {e}"


@tool
def get_practice_session_summary(session_id: str) -> str:
    """
    Get a comprehensive summary of a practice session.
    
    Args:
        session_id: ID of the practice session
        
    Returns:
        Detailed session summary and insights
    """
    try:
        # Load session
        sessions = practice_manager.load_all_sessions()
        if session_id not in sessions:
            return f"❌ Practice session {session_id} not found."
        
        session_data = sessions[session_id]
        session = PracticeSession(**session_data)
        
        summary = practice_manager.get_practice_summary(session)
        
        result_lines = []
        result_lines.append(f"📊 **Practice Session Summary - {session_id}**")
        result_lines.append(f"🎯 **Framework:** {summary['framework']}")
        result_lines.append(f"📝 **Task:** {summary['task_description']}")
        result_lines.append(f"⏱️ **Duration:** {summary['duration_minutes']:.1f} minutes")
        result_lines.append(f"📄 **Files Created:** {summary['files_created']}")
        result_lines.append(f"📦 **Dependencies Installed:** {summary['dependencies_installed']}")
        result_lines.append(f"🚀 **Execution Attempts:** {summary['execution_attempts']}")
        result_lines.append(f"🐛 **Errors Encountered:** {summary['errors_encountered']}")
        result_lines.append(f"✅ **Success Rate:** {summary['success_rate']:.1f}%")
        
        # Status with emoji
        status = summary['status']
        if status == 'excellent':
            result_lines.append(f"🌟 **Status:** EXCELLENT - Great job!")
        elif status == 'good':
            result_lines.append(f"👍 **Status:** GOOD - Keep practicing!")
        else:
            result_lines.append(f"💪 **Status:** NEEDS IMPROVEMENT - Don't give up!")
        
        # Show recent errors for learning
        if session.errors_encountered:
            result_lines.append(f"\n🐛 **Recent Errors (for learning):**")
            for error in session.errors_encountered[-3:]:  # Last 3 errors
                result_lines.append(f"   • {error['error_type']}: {error['error_message'][:100]}...")
        
        # Show files created
        if session.generated_files:
            result_lines.append(f"\n📁 **Files Created:**")
            for file_path in session.generated_files:
                filename = Path(file_path).name
                result_lines.append(f"   • {filename}")
        
        return "\n".join(result_lines)
        
    except Exception as e:
        return f"❌ Failed to get session summary: {e}"


@tool
def list_all_practice_sessions(user_id: str = "") -> str:
    """
    List all practice sessions, optionally filtered by user.
    
    Args:
        user_id: Optional user ID to filter sessions
        
    Returns:
        List of all practice sessions
    """
    try:
        sessions = practice_manager.load_all_sessions()
        
        if not sessions:
            return "📭 No practice sessions found. Start your first session!"
        
        filtered_sessions = sessions
        if user_id:
            filtered_sessions = {k: v for k, v in sessions.items() if v.get('user_id') == user_id}
        
        if not filtered_sessions:
            return f"📭 No practice sessions found for user '{user_id}'"
        
        result_lines = []
        result_lines.append("📚 **Practice Sessions:**")
        
        for session_id, session_data in filtered_sessions.items():
            framework = session_data.get('framework', 'Unknown')
            task = session_data.get('task_description', 'No description')[:50]
            files_count = len(session_data.get('generated_files', []))
            attempts = len(session_data.get('execution_attempts', []))
            
            # Calculate success rate
            attempts_data = session_data.get('execution_attempts', [])
            if attempts_data:
                successful = sum(1 for attempt in attempts_data if attempt.get('success', False))
                success_rate = (successful / len(attempts_data)) * 100
            else:
                success_rate = 0
            
            result_lines.append(f"🔹 **{session_id}** - {framework}")
            result_lines.append(f"   📝 Task: {task}")
            result_lines.append(f"   📊 {files_count} files, {attempts} attempts, {success_rate:.0f}% success")
        
        return "\n".join(result_lines)
        
    except Exception as e:
        return f"❌ Failed to list practice sessions: {e}"


def get_enhanced_file_tools() -> List:
    """Get all enhanced file tools including interactive practice tools."""
    return [
        create_code_file,
        auto_create_files_from_text,
        list_generated_files,
        read_generated_file,
        delete_generated_file,
        # Enhanced interactive practice tools
        check_and_install_dependencies,
        start_practice_session,
        create_practice_file_with_auto_deps,
        execute_practice_file,
        get_practice_session_summary,
        list_all_practice_sessions
    ]


def get_file_tools() -> List:
    """Get standard file tools (for backward compatibility)."""
    return [
        create_code_file,
        auto_create_files_from_text,
        list_generated_files,
        read_generated_file,
        delete_generated_file
    ] 