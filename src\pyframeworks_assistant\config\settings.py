"""
Main application settings using Pydantic Settings for configuration management.
Based on the latest LangChain and LangGraph best practices.
"""

import os
from typing import Optional, Dict, Any, List
from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Main application settings with environment variable support."""
    
    # LLM Configuration
    openai_api_key: Optional[str] = Field(default=None, description="OpenAI API key")
    anthropic_api_key: Optional[str] = Field(default=None, description="Anthropic API key")
    google_api_key: Optional[str] = Field(default=None, description="Google GenAI API key")
    
    # LangSmith Configuration (Optional but recommended)
    langchain_tracing_v2: bool = Field(default=True, description="Enable LangSmith tracing")
    langchain_endpoint: str = Field(
        default="https://api.smith.langchain.com", 
        description="LangSmith endpoint"
    )
    langchain_api_key: Optional[str] = Field(default=None, description="LangSmith API key")
    langchain_project: str = Field(
        default="pyframeworks-assistant", 
        description="Lang<PERSON>mith project name"
    )
    
    # Database Configuration
    redis_url: str = Field(
        default="redis://localhost:6379/0", 
        description="Redis connection URL"
    )
    postgres_url: str = Field(
        default="postgresql://user:password@localhost:5432/pyframeworks_db",
        description="PostgreSQL connection URL"
    )
    
    # ChromaDB Configuration
    chroma_persist_directory: str = Field(
        default="./data/chroma_db", 
        description="ChromaDB persistence directory"
    )
    
    # Application Settings
    debug: bool = Field(default=False, description="Debug mode")
    log_level: str = Field(default="INFO", description="Logging level")
    secret_key: str = Field(
        default="your-secret-key-change-in-production", 
        description="Application secret key"
    )
    
    # Web Interface Settings
    streamlit_port: int = Field(default=8501, description="Streamlit port")
    api_port: int = Field(default=8000, description="FastAPI port")
    host: str = Field(default="0.0.0.0", description="Host address")
    
    # User Configuration Defaults
    default_user_timezone: str = Field(default="UTC", description="Default user timezone")
    default_language: str = Field(default="english", description="Default language")
    default_learning_pace: str = Field(default="moderate", description="Default learning pace")
    
    # External APIs
    github_token: Optional[str] = Field(default=None, description="GitHub API token")
    web_search_api_key: Optional[str] = Field(default=None, description="Web search API key")
    
    # Temporal Learning Settings
    effectiveness_tracking_enabled: bool = Field(
        default=True, 
        description="Enable effectiveness tracking"
    )
    pattern_analysis_enabled: bool = Field(
        default=True, 
        description="Enable pattern analysis"
    )
    optimization_auto_apply: bool = Field(
        default=False, 
        description="Auto-apply optimizations"
    )
    
    # Constellation Settings
    max_concurrent_agents: int = Field(
        default=16, 
        description="Maximum concurrent agents in constellation"
    )
    constellation_timeout: int = Field(
        default=300, 
        description="Constellation timeout in seconds"
    )
    role_morphing_enabled: bool = Field(
        default=True, 
        description="Enable dynamic role morphing"
    )
    
    # Memory Management
    max_memory_sessions: int = Field(
        default=1000, 
        description="Maximum memory sessions to retain"
    )
    memory_cleanup_interval: int = Field(
        default=3600, 
        description="Memory cleanup interval in seconds"
    )
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        extra = "allow"
        
    def get_llm_config(self) -> Dict[str, Any]:
        """Get LLM configuration for LangChain initialization."""
        config = {}
        
        if self.openai_api_key:
            config["openai"] = {
                "api_key": self.openai_api_key,
                "model": "gpt-4o-mini",  # Default model as per docs
                "temperature": 0
            }
            
        if self.anthropic_api_key:
            config["anthropic"] = {
                "api_key": self.anthropic_api_key,
                "model": "claude-3-5-sonnet-20241022",
                "temperature": 0
            }
            
        if self.google_api_key:
            config["google"] = {
                "api_key": self.google_api_key,
                "model": "gemini-2.0-flash",
                "temperature": 0
            }
            
        return config
    
    def get_database_config(self) -> Dict[str, str]:
        """Get database configuration."""
        return {
            "redis_url": self.redis_url,
            "postgres_url": self.postgres_url,
            "chroma_persist_directory": self.chroma_persist_directory
        }
    
    def get_langsmith_config(self) -> Dict[str, Any]:
        """Get LangSmith configuration."""
        if not self.langchain_api_key:
            return {"enabled": False}
            
        return {
            "enabled": self.langchain_tracing_v2,
            "endpoint": self.langchain_endpoint,
            "api_key": self.langchain_api_key,
            "project": self.langchain_project
        }
    
    def setup_environment(self) -> None:
        """Setup environment variables for LangChain and other services."""
        # LangSmith setup
        if self.langchain_api_key:
            os.environ["LANGCHAIN_TRACING_V2"] = str(self.langchain_tracing_v2).lower()
            os.environ["LANGCHAIN_ENDPOINT"] = self.langchain_endpoint
            os.environ["LANGCHAIN_API_KEY"] = self.langchain_api_key
            os.environ["LANGCHAIN_PROJECT"] = self.langchain_project
        
        # LLM API keys
        if self.openai_api_key:
            os.environ["OPENAI_API_KEY"] = self.openai_api_key
        if self.anthropic_api_key:
            os.environ["ANTHROPIC_API_KEY"] = self.anthropic_api_key
        if self.google_api_key:
            os.environ["GOOGLE_API_KEY"] = self.google_api_key


# Global settings instance
settings = Settings()

# Setup environment on import
settings.setup_environment() 