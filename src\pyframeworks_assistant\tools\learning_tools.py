"""
Learning tools for quiz generation, exercises, and progress tracking.
Provides educational tools to enhance the learning experience.
"""

import random
import json
from typing import List, Dict, Any, Optional
from datetime import datetime
import logging

from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field

from ..core.models import llm_manager

logger = logging.getLogger(__name__)


class QuizQuestion(BaseModel):
    """A quiz question with multiple choices."""
    question: str = Field(description="The question text")
    choices: List[str] = Field(description="Multiple choice options")
    correct_answer: int = Field(description="Index of correct answer (0-based)")
    explanation: str = Field(description="Explanation of the correct answer")
    difficulty: str = Field(description="Difficulty level: easy, medium, hard")
    topic: str = Field(description="Topic or concept being tested")


class Exercise(BaseModel):
    """A hands-on coding exercise."""
    title: str = Field(description="Exercise title")
    description: str = Field(description="Exercise description")
    instructions: List[str] = Field(description="Step-by-step instructions")
    starter_code: Optional[str] = Field(default=None, description="Starting code template")
    expected_output: Optional[str] = Field(default=None, description="Expected output")
    hints: List[str] = Field(default_factory=list, description="Helpful hints")
    difficulty: str = Field(description="Difficulty level")
    estimated_time: int = Field(description="Estimated completion time in minutes")


class ProgressReport(BaseModel):
    """User progress report."""
    session_id: str = Field(description="Session identifier")
    framework: str = Field(description="Framework being learned")
    topics_covered: List[str] = Field(description="Topics covered")
    quiz_scores: List[float] = Field(description="Quiz scores (0-1)")
    exercises_completed: List[str] = Field(description="Completed exercises")
    time_spent: int = Field(description="Time spent in minutes")
    overall_progress: float = Field(description="Overall progress percentage")
    strengths: List[str] = Field(description="Identified strengths")
    improvement_areas: List[str] = Field(description="Areas for improvement")
    recommendations: List[str] = Field(description="Learning recommendations")


class QuizGeneratorTool(BaseTool):
    """Tool for generating educational quizzes."""
    
    name: str = "quiz_generator"
    description: str = """
    Generate educational quizzes on AI framework topics.
    Use this to assess user understanding and reinforce learning.
    
    Input: Topic and difficulty level
    Output: List of quiz questions with explanations
    """
    
    def _run(self, topic: str, difficulty: str = "medium", num_questions: int = 5) -> List[QuizQuestion]:
        """Generate quiz questions for a topic."""
        try:
            # Create prompt for quiz generation
            prompt = f"""
            Generate {num_questions} multiple choice quiz questions about {topic}.
            
            Requirements:
            - Difficulty level: {difficulty}
            - Each question should have 4 choices
            - Include clear explanations for correct answers
            - Focus on practical understanding, not just memorization
            - Cover different aspects of the topic
            
            Format each question as:
            Question: [question text]
            A) [choice 1]
            B) [choice 2]  
            C) [choice 3]
            D) [choice 4]
            Correct: [A/B/C/D]
            Explanation: [explanation]
            
            Topics to cover: {topic}
            """
            
            # Get LLM to generate questions
            model = llm_manager.get_default_model()
            response = model.invoke(prompt)
            
            # Parse the response into QuizQuestion objects
            questions = self._parse_quiz_response(response.content, topic, difficulty)
            
            return questions
            
        except Exception as e:
            logger.error(f"Error generating quiz: {e}")
            # Return a fallback question
            return [QuizQuestion(
                question=f"What is the main purpose of {topic}?",
                choices=[
                    "To make development easier",
                    "To complicate workflows", 
                    "To replace all other tools",
                    "To confuse developers"
                ],
                correct_answer=0,
                explanation=f"{topic} is designed to simplify and streamline development workflows.",
                difficulty=difficulty,
                topic=topic
            )]
    
    def _parse_quiz_response(self, response: str, topic: str, difficulty: str) -> List[QuizQuestion]:
        """Parse LLM response into QuizQuestion objects."""
        questions = []
        
        # Simple parsing - in production, you'd want more robust parsing
        sections = response.split("Question:")
        
        for i, section in enumerate(sections[1:], 1):  # Skip first empty section
            try:
                lines = section.strip().split('\n')
                question_text = lines[0].strip()
                
                choices = []
                correct_answer = 0
                explanation = ""
                
                for line in lines[1:]:
                    line = line.strip()
                    if line.startswith(('A)', 'B)', 'C)', 'D)')):
                        choices.append(line[2:].strip())
                    elif line.startswith('Correct:'):
                        answer_letter = line.split(':')[1].strip()
                        correct_answer = ord(answer_letter) - ord('A')
                    elif line.startswith('Explanation:'):
                        explanation = line.split(':', 1)[1].strip()
                
                if len(choices) == 4 and explanation:
                    questions.append(QuizQuestion(
                        question=question_text,
                        choices=choices,
                        correct_answer=correct_answer,
                        explanation=explanation,
                        difficulty=difficulty,
                        topic=topic
                    ))
                    
            except Exception as e:
                logger.warning(f"Failed to parse question {i}: {e}")
                continue
        
        # If parsing failed, return a default question
        if not questions:
            questions.append(QuizQuestion(
                question=f"What is a key benefit of using {topic}?",
                choices=[
                    "Improved productivity",
                    "Increased complexity",
                    "More bugs",
                    "Slower development"
                ],
                correct_answer=0,
                explanation=f"{topic} is designed to improve developer productivity and workflow efficiency.",
                difficulty=difficulty,
                topic=topic
            ))
        
        return questions


class ExerciseGeneratorTool(BaseTool):
    """Tool for generating hands-on coding exercises."""
    
    name: str = "exercise_generator"
    description: str = """
    Generate hands-on coding exercises for learning AI frameworks.
    Use this to provide practical, skill-building activities.
    
    Input: Topic, difficulty, and framework
    Output: Detailed coding exercise with instructions
    """
    
    def _run(self, topic: str, framework: str, difficulty: str = "medium") -> Exercise:
        """Generate a coding exercise."""
        try:
            # Create prompt for exercise generation
            prompt = f"""
            Create a hands-on coding exercise for learning {topic} in {framework}.
            
            Requirements:
            - Difficulty: {difficulty}
            - Include clear step-by-step instructions
            - Provide starter code template if helpful
            - Include expected output or behavior
            - Add 2-3 helpful hints
            - Estimate completion time
            
            Focus on practical, real-world applications that reinforce understanding.
            
            Topic: {topic}
            Framework: {framework}
            """
            
            # Generate exercise using LLM
            model = llm_manager.get_default_model()
            response = model.invoke(prompt)
            
            # Parse response into Exercise object
            exercise = self._parse_exercise_response(response.content, topic, framework, difficulty)
            
            return exercise
            
        except Exception as e:
            logger.error(f"Error generating exercise: {e}")
            # Return a fallback exercise
            return Exercise(
                title=f"Basic {topic} Exercise",
                description=f"Learn the fundamentals of {topic} in {framework}",
                instructions=[
                    f"1. Import the necessary {framework} modules",
                    f"2. Create a basic {topic} implementation",
                    "3. Test your implementation with sample data",
                    "4. Verify the output matches expectations"
                ],
                starter_code=f"# TODO: Implement {topic} using {framework}\n",
                expected_output="Expected functionality working correctly",
                hints=[
                    f"Check the {framework} documentation for examples",
                    f"Start with the simplest {topic} implementation"
                ],
                difficulty=difficulty,
                estimated_time=30
            )
    
    def _parse_exercise_response(self, response: str, topic: str, framework: str, difficulty: str) -> Exercise:
        """Parse LLM response into Exercise object."""
        # Simple parsing - extract key components
        lines = response.split('\n')
        
        title = f"{topic} Exercise in {framework}"
        description = f"Hands-on exercise for learning {topic}"
        instructions = []
        starter_code = None
        expected_output = None
        hints = []
        estimated_time = 30
        
        current_section = None
        
        for line in lines:
            line = line.strip()
            if 'title:' in line.lower():
                title = line.split(':', 1)[1].strip()
            elif 'description:' in line.lower():
                description = line.split(':', 1)[1].strip()
            elif 'instructions:' in line.lower() or 'steps:' in line.lower():
                current_section = 'instructions'
            elif 'starter code:' in line.lower() or 'template:' in line.lower():
                current_section = 'starter_code'
            elif 'expected output:' in line.lower() or 'expected:' in line.lower():
                current_section = 'expected_output'
            elif 'hints:' in line.lower():
                current_section = 'hints'
            elif 'time:' in line.lower() and 'minutes' in line.lower():
                try:
                    estimated_time = int(''.join(filter(str.isdigit, line)))
                except:
                    estimated_time = 30
            elif line.startswith(('1.', '2.', '3.', '4.', '5.', '-', '*')):
                if current_section == 'instructions':
                    instructions.append(line)
                elif current_section == 'hints':
                    hints.append(line)
            elif current_section == 'starter_code' and line:
                if starter_code is None:
                    starter_code = line
                else:
                    starter_code += '\n' + line
            elif current_section == 'expected_output' and line:
                if expected_output is None:
                    expected_output = line
                else:
                    expected_output += ' ' + line
        
        # Ensure we have at least basic instructions
        if not instructions:
            instructions = [
                "1. Review the exercise requirements",
                "2. Implement the solution step by step",
                "3. Test your implementation",
                "4. Verify the results"
            ]
        
        return Exercise(
            title=title,
            description=description,
            instructions=instructions,
            starter_code=starter_code,
            expected_output=expected_output,
            hints=hints,
            difficulty=difficulty,
            estimated_time=estimated_time
        )


class ProgressTrackerTool(BaseTool):
    """Tool for tracking and analyzing learning progress."""
    
    name: str = "progress_tracker"
    description: str = """
    Track and analyze user learning progress across sessions.
    Use this to provide insights and recommendations for improvement.
    
    Input: Session data and performance metrics
    Output: Comprehensive progress report with recommendations
    """
    
    def _run(self, session_data: Dict[str, Any]) -> ProgressReport:
        """Generate progress report from session data."""
        try:
            # Extract session information
            session_id = session_data.get('session_id', 'unknown')
            framework = session_data.get('framework', 'general')
            topics_covered = session_data.get('topics_covered', [])
            quiz_scores = session_data.get('quiz_scores', [])
            exercises_completed = session_data.get('exercises_completed', [])
            time_spent = session_data.get('time_spent', 0)
            
            # Calculate overall progress
            overall_progress = self._calculate_overall_progress(
                topics_covered, quiz_scores, exercises_completed
            )
            
            # Analyze strengths and weaknesses
            strengths, improvement_areas = self._analyze_performance(
                topics_covered, quiz_scores, exercises_completed
            )
            
            # Generate recommendations
            recommendations = self._generate_recommendations(
                framework, strengths, improvement_areas, overall_progress
            )
            
            return ProgressReport(
                session_id=session_id,
                framework=framework,
                topics_covered=topics_covered,
                quiz_scores=quiz_scores,
                exercises_completed=exercises_completed,
                time_spent=time_spent,
                overall_progress=overall_progress,
                strengths=strengths,
                improvement_areas=improvement_areas,
                recommendations=recommendations
            )
            
        except Exception as e:
            logger.error(f"Error generating progress report: {e}")
            return ProgressReport(
                session_id="error",
                framework="unknown",
                topics_covered=[],
                quiz_scores=[],
                exercises_completed=[],
                time_spent=0,
                overall_progress=0.0,
                strengths=["Persistence in learning"],
                improvement_areas=["More practice needed"],
                recommendations=["Continue learning at your own pace"]
            )
    
    def _calculate_overall_progress(self, topics: List[str], quiz_scores: List[float], exercises: List[str]) -> float:
        """Calculate overall progress percentage."""
        progress_factors = []
        
        # Topic coverage (weight: 30%)
        if topics:
            topic_progress = min(len(topics) / 10.0, 1.0)  # Assume 10 topics for full coverage
            progress_factors.append(topic_progress * 0.3)
        
        # Quiz performance (weight: 40%)
        if quiz_scores:
            avg_quiz_score = sum(quiz_scores) / len(quiz_scores)
            progress_factors.append(avg_quiz_score * 0.4)
        
        # Exercise completion (weight: 30%)
        if exercises:
            exercise_progress = min(len(exercises) / 5.0, 1.0)  # Assume 5 exercises for full coverage
            progress_factors.append(exercise_progress * 0.3)
        
        return sum(progress_factors) * 100  # Convert to percentage
    
    def _analyze_performance(self, topics: List[str], quiz_scores: List[float], exercises: List[str]) -> tuple:
        """Analyze performance to identify strengths and improvement areas."""
        strengths = []
        improvement_areas = []
        
        # Quiz performance analysis
        if quiz_scores:
            avg_score = sum(quiz_scores) / len(quiz_scores)
            if avg_score >= 0.8:
                strengths.append("Strong quiz performance")
            elif avg_score < 0.6:
                improvement_areas.append("Quiz accuracy needs improvement")
        
        # Topic coverage analysis
        if len(topics) >= 5:
            strengths.append("Good topic coverage")
        elif len(topics) < 3:
            improvement_areas.append("Limited topic exploration")
        
        # Exercise completion analysis
        if len(exercises) >= 3:
            strengths.append("Active hands-on practice")
        elif len(exercises) < 2:
            improvement_areas.append("More hands-on practice needed")
        
        # Default messages if no specific patterns found
        if not strengths:
            strengths.append("Commitment to learning")
        if not improvement_areas:
            improvement_areas.append("Continue current learning pace")
        
        return strengths, improvement_areas
    
    def _generate_recommendations(self, framework: str, strengths: List[str], improvement_areas: List[str], progress: float) -> List[str]:
        """Generate personalized learning recommendations."""
        recommendations = []
        
        # Progress-based recommendations
        if progress < 30:
            recommendations.append("Focus on foundational concepts before moving to advanced topics")
            recommendations.append(f"Spend more time with basic {framework} tutorials")
        elif progress < 70:
            recommendations.append("Great progress! Try more challenging exercises")
            recommendations.append("Consider building a small project to apply your knowledge")
        else:
            recommendations.append("Excellent progress! You're ready for advanced topics")
            recommendations.append("Consider contributing to open source projects")
        
        # Improvement-based recommendations
        if "Quiz accuracy needs improvement" in improvement_areas:
            recommendations.append("Review concepts before taking quizzes")
            recommendations.append("Take time to understand explanations for incorrect answers")
        
        if "More hands-on practice needed" in improvement_areas:
            recommendations.append("Complete more coding exercises")
            recommendations.append("Build small projects to reinforce learning")
        
        if "Limited topic exploration" in improvement_areas:
            recommendations.append(f"Explore more aspects of {framework}")
            recommendations.append("Follow a structured learning path")
        
        return recommendations


class FeedbackCollectorTool(BaseTool):
    """Tool for collecting and analyzing user feedback."""
    
    name: str = "feedback_collector"
    description: str = """
    Collect and analyze user feedback to improve the learning experience.
    Use this to gather insights about content quality and user satisfaction.
    
    Input: Feedback data and session context
    Output: Processed feedback with insights
    """
    
    def _run(self, feedback_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process user feedback."""
        try:
            feedback_type = feedback_data.get('type', 'general')
            rating = feedback_data.get('rating', 0)
            comment = feedback_data.get('comment', '')
            session_context = feedback_data.get('session_context', {})
            
            # Analyze sentiment
            sentiment = self._analyze_sentiment(comment)
            
            # Extract themes
            themes = self._extract_themes(comment, feedback_type)
            
            # Generate insights
            insights = self._generate_insights(rating, sentiment, themes, session_context)
            
            return {
                'feedback_id': feedback_data.get('id', 'unknown'),
                'type': feedback_type,
                'rating': rating,
                'sentiment': sentiment,
                'themes': themes,
                'insights': insights,
                'processed_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error processing feedback: {e}")
            return {
                'error': str(e),
                'processed_at': datetime.now().isoformat()
            }
    
    def _analyze_sentiment(self, comment: str) -> str:
        """Analyze sentiment of feedback comment."""
        if not comment:
            return 'neutral'
        
        # Simple keyword-based sentiment analysis
        positive_words = ['good', 'great', 'excellent', 'helpful', 'useful', 'clear', 'easy']
        negative_words = ['bad', 'difficult', 'confusing', 'hard', 'unclear', 'frustrating']
        
        comment_lower = comment.lower()
        positive_count = sum(1 for word in positive_words if word in comment_lower)
        negative_count = sum(1 for word in negative_words if word in comment_lower)
        
        if positive_count > negative_count:
            return 'positive'
        elif negative_count > positive_count:
            return 'negative'
        else:
            return 'neutral'
    
    def _extract_themes(self, comment: str, feedback_type: str) -> List[str]:
        """Extract themes from feedback comment."""
        themes = []
        
        if not comment:
            return themes
        
        comment_lower = comment.lower()
        
        # Content-related themes
        if any(word in comment_lower for word in ['content', 'material', 'lesson']):
            themes.append('content_quality')
        
        # Difficulty-related themes
        if any(word in comment_lower for word in ['difficult', 'hard', 'easy', 'challenging']):
            themes.append('difficulty_level')
        
        # Interface-related themes
        if any(word in comment_lower for word in ['interface', 'ui', 'design', 'navigation']):
            themes.append('user_interface')
        
        # Learning experience themes
        if any(word in comment_lower for word in ['learning', 'understand', 'explain']):
            themes.append('learning_experience')
        
        return themes
    
    def _generate_insights(self, rating: int, sentiment: str, themes: List[str], context: Dict[str, Any]) -> List[str]:
        """Generate insights from feedback analysis."""
        insights = []
        
        # Rating-based insights
        if rating >= 4:
            insights.append("High satisfaction - user found the experience valuable")
        elif rating <= 2:
            insights.append("Low satisfaction - requires immediate attention")
        
        # Sentiment-based insights
        if sentiment == 'positive':
            insights.append("Positive user experience - content is well-received")
        elif sentiment == 'negative':
            insights.append("Negative experience - investigate pain points")
        
        # Theme-based insights
        if 'difficulty_level' in themes:
            insights.append("User has concerns about content difficulty")
        if 'content_quality' in themes:
            insights.append("Feedback relates to content quality")
        if 'user_interface' in themes:
            insights.append("UI/UX improvements may be needed")
        
        # Context-based insights
        if context.get('framework') == 'langchain' and rating <= 2:
            insights.append("LangChain content may need improvement")
        
        return insights 