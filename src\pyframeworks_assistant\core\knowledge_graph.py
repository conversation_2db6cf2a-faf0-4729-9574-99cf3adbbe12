"""
Knowledge Graph Manager - Manages a graph of concepts, relationships, and learning paths.
"""

import logging
import asyncio
from typing import Dict, List, Any, Optional, Set, Tuple
from datetime import datetime
import json
from enum import Enum
from dataclasses import dataclass, field

from pydantic import BaseModel, Field
import networkx as nx

from ..config.framework_configs import SupportedFrameworks
from .intelligent_context import IntelligentContext  # Updated import

logger = logging.getLogger(__name__)


class ConceptType(str, Enum):
    """Types of concepts in the knowledge graph."""
    FUNDAMENTAL = "fundamental"
    FRAMEWORK_SPECIFIC = "framework_specific"
    PRACTICAL = "practical"
    ADVANCED = "advanced"
    TOOL = "tool"
    PATTERN = "pattern"
    BEST_PRACTICE = "best_practice"


class RelationType(str, Enum):
    """Types of relationships between concepts."""
    PREREQUISITE = "prerequisite"
    BUILDS_ON = "builds_on"
    RELATED_TO = "related_to"
    IMPLEMENTS = "implements"
    USES = "uses"
    PART_OF = "part_of"
    EXAMPLE_OF = "example_of"
    ALTERNATIVE_TO = "alternative_to"


class DifficultyLevel(str, Enum):
    """Difficulty levels for concepts."""
    BEGINNER = "beginner"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"
    EXPERT = "expert"


@dataclass
class ConceptNode:
    """Represents a concept node in the knowledge graph."""
    concept_id: str
    name: str
    description: str
    concept_type: ConceptType
    framework: Optional[SupportedFrameworks]
    difficulty_level: DifficultyLevel
    keywords: List[str] = field(default_factory=list)
    learning_objectives: List[str] = field(default_factory=list)
    code_examples: List[str] = field(default_factory=list)
    resources: List[Dict[str, str]] = field(default_factory=list)
    mastery_indicators: List[str] = field(default_factory=list)
    common_mistakes: List[str] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.now)
    last_updated: datetime = field(default_factory=datetime.now)
    
    # Learning analytics
    times_accessed: int = 0
    average_mastery_time: float = 0.0  # in minutes
    success_rate: float = 0.0
    user_feedback_score: float = 0.0


@dataclass
class LearningRelation:
    """Represents a relationship between concepts."""
    relation_id: str
    source_concept_id: str
    target_concept_id: str
    relation_type: RelationType
    strength: float  # 0-1, how strong the relationship is
    description: str
    learning_order: Optional[int] = None  # For prerequisite relationships
    created_at: datetime = field(default_factory=datetime.now)


@dataclass
class LearningPath:
    """Represents a path through the knowledge graph."""
    path_id: str
    concept_sequence: List[str]
    total_difficulty: float
    estimated_duration: int  # in minutes
    success_probability: float
    personalization_factors: Dict[str, Any] = field(default_factory=dict)


class KnowledgeGraphManager:
    """
    Manages the knowledge graph for structured learning.
    """
    
    def __init__(self):
        """Initialize the knowledge graph manager."""
        self.concepts: Dict[str, ConceptNode] = {}
        self.relations: Dict[str, LearningRelation] = {}
        self.framework_graphs: Dict[SupportedFrameworks, Set[str]] = {}
        self.user_progress: Dict[str, Dict[str, float]] = {}  # user_id -> concept_id -> mastery_level
        
        # Analytics
        self.graph_metrics: Dict[str, Any] = {
            "total_concepts": 0,
            "total_relations": 0,
            "average_concept_difficulty": 0.0,
            "most_accessed_concepts": [],
            "learning_bottlenecks": []
        }
        
        logger.info("Knowledge Graph Manager initialized")
    
    async def initialize(self) -> None:
        """Initialize the knowledge graph with base concepts."""
        try:
            # Initialize base concepts for each framework
            await self._initialize_base_concepts()
            
            # Create fundamental relationships
            await self._create_base_relationships()
            
            # Update metrics
            await self._update_graph_metrics()
            
            logger.info("Knowledge Graph Manager fully initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize Knowledge Graph Manager: {e}")
            raise
    
    async def add_concept(
        self,
        concept_id: str,
        name: str,
        description: str,
        concept_type: ConceptType,
        framework: Optional[SupportedFrameworks] = None,
        difficulty_level: DifficultyLevel = DifficultyLevel.INTERMEDIATE,
        **kwargs
    ) -> ConceptNode:
        """Add a new concept to the knowledge graph."""
        try:
            concept = ConceptNode(
                concept_id=concept_id,
                name=name,
                description=description,
                concept_type=concept_type,
                framework=framework,
                difficulty_level=difficulty_level,
                **kwargs
            )
            
            self.concepts[concept_id] = concept
            
            # Add to framework graph if applicable
            if framework:
                if framework not in self.framework_graphs:
                    self.framework_graphs[framework] = set()
                self.framework_graphs[framework].add(concept_id)
            
            # Update metrics
            self.graph_metrics["total_concepts"] += 1
            
            logger.info(f"Added concept: {concept_id}")
            return concept
            
        except Exception as e:
            logger.error(f"Failed to add concept {concept_id}: {e}")
            raise
    
    async def add_relation(
        self,
        relation_id: str,
        source_concept_id: str,
        target_concept_id: str,
        relation_type: RelationType,
        strength: float = 1.0,
        description: str = "",
        learning_order: Optional[int] = None
    ) -> LearningRelation:
        """Add a relationship between concepts."""
        try:
            # Validate that concepts exist
            if source_concept_id not in self.concepts:
                raise ValueError(f"Source concept {source_concept_id} not found")
            if target_concept_id not in self.concepts:
                raise ValueError(f"Target concept {target_concept_id} not found")
            
            relation = LearningRelation(
                relation_id=relation_id,
                source_concept_id=source_concept_id,
                target_concept_id=target_concept_id,
                relation_type=relation_type,
                strength=strength,
                description=description,
                learning_order=learning_order
            )
            
            self.relations[relation_id] = relation
            self.graph_metrics["total_relations"] += 1
            
            logger.info(f"Added relation: {relation_id}")
            return relation
            
        except Exception as e:
            logger.error(f"Failed to add relation {relation_id}: {e}")
            raise
    
    async def update_from_interaction(
        self,
        user_message: str,
        agent_response: str,
        framework: SupportedFrameworks,
        context: Any  # TODO: Define IntelligentContext
    ) -> None:
        """Update knowledge graph based on learning interaction."""
        try:
            # Extract concepts mentioned in the interaction
            mentioned_concepts = await self._extract_concepts_from_text(
                user_message + " " + agent_response,
                framework
            )
            
            # Update concept access counts
            for concept_id in mentioned_concepts:
                if concept_id in self.concepts:
                    self.concepts[concept_id].times_accessed += 1
                    self.concepts[concept_id].last_updated = datetime.now()
            
            # Analyze learning patterns
            await self._analyze_learning_patterns(mentioned_concepts, context)
            
            # Update user progress if user context is available
            if hasattr(context, 'user_id'):
                await self._update_user_progress(context.user_id, mentioned_concepts, context)
            
        except Exception as e:
            logger.error(f"Failed to update knowledge graph from interaction: {e}")
    
    async def get_learning_insights(
        self,
        framework: SupportedFrameworks,
        topics_covered: List[str]
    ) -> Dict[str, Any]:
        """Get learning insights based on covered topics."""
        try:
            insights = {
                "concept_mastery": {},
                "knowledge_gaps": [],
                "recommended_next_concepts": [],
                "learning_efficiency": 0.0,
                "concept_relationships": []
            }
            
            # Analyze concept mastery
            framework_concepts = self.framework_graphs.get(framework, set())
            covered_concept_ids = await self._map_topics_to_concepts(topics_covered, framework)
            
            for concept_id in covered_concept_ids:
                if concept_id in self.concepts:
                    concept = self.concepts[concept_id]
                    insights["concept_mastery"][concept.name] = {
                        "difficulty": concept.difficulty_level.value,
                        "access_count": concept.times_accessed,
                        "success_rate": concept.success_rate
                    }
            
            # Identify knowledge gaps
            insights["knowledge_gaps"] = await self._identify_knowledge_gaps(
                covered_concept_ids,
                framework_concepts
            )
            
            # Recommend next concepts
            insights["recommended_next_concepts"] = await self._recommend_next_concepts(
                covered_concept_ids,
                framework
            )
            
            # Calculate learning efficiency
            insights["learning_efficiency"] = await self._calculate_learning_efficiency(
                covered_concept_ids
            )
            
            # Get concept relationships
            insights["concept_relationships"] = await self._get_concept_relationships(
                covered_concept_ids
            )
            
            return insights
            
        except Exception as e:
            logger.error(f"Failed to get learning insights: {e}")
            return {}
    
    async def generate_personalized_path(
        self,
        user_id: str,
        target_concepts: List[str],
        framework: SupportedFrameworks,
        time_constraint: Optional[int] = None
    ) -> LearningPath:
        """Generate a personalized learning path through the knowledge graph."""
        try:
            # Get user's current progress
            user_progress = self.user_progress.get(user_id, {})
            
            # Find optimal path through concepts
            concept_sequence = await self._find_optimal_path(
                target_concepts,
                user_progress,
                framework,
                time_constraint
            )
            
            # Calculate path metrics
            total_difficulty = sum(
                self.concepts[concept_id].difficulty_level.value == "advanced" and 3 or
                self.concepts[concept_id].difficulty_level.value == "intermediate" and 2 or 1
                for concept_id in concept_sequence
                if concept_id in self.concepts
            )
            
            estimated_duration = sum(
                self.concepts[concept_id].average_mastery_time or 30
                for concept_id in concept_sequence
                if concept_id in self.concepts
            )
            
            success_probability = await self._calculate_path_success_probability(
                concept_sequence,
                user_progress
            )
            
            path = LearningPath(
                path_id=f"path_{user_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                concept_sequence=concept_sequence,
                total_difficulty=total_difficulty,
                estimated_duration=int(estimated_duration),
                success_probability=success_probability
            )
            
            return path
            
        except Exception as e:
            logger.error(f"Failed to generate personalized path: {e}")
            raise
    
    async def get_concept_prerequisites(self, concept_id: str) -> List[str]:
        """Get prerequisites for a concept."""
        prerequisites = []
        
        for relation in self.relations.values():
            if (relation.target_concept_id == concept_id and 
                relation.relation_type == RelationType.PREREQUISITE):
                prerequisites.append(relation.source_concept_id)
        
        # Sort by learning order if available
        prerequisites.sort(key=lambda x: next(
            (r.learning_order for r in self.relations.values() 
             if r.source_concept_id == x and r.target_concept_id == concept_id),
            0
        ) or 0)
        
        return prerequisites
    
    async def get_concept_dependents(self, concept_id: str) -> List[str]:
        """Get concepts that depend on this concept."""
        dependents = []
        
        for relation in self.relations.values():
            if (relation.source_concept_id == concept_id and 
                relation.relation_type in [RelationType.PREREQUISITE, RelationType.BUILDS_ON]):
                dependents.append(relation.target_concept_id)
        
        return dependents
    
    async def _initialize_base_concepts(self) -> None:
        """Initialize base concepts for each framework."""
        # LangChain concepts
        langchain_concepts = [
            ("langchain_basics", "LangChain Basics", "Fundamental LangChain concepts", ConceptType.FUNDAMENTAL, DifficultyLevel.BEGINNER),
            ("langchain_prompts", "LangChain Prompts", "Prompt templates and engineering", ConceptType.FRAMEWORK_SPECIFIC, DifficultyLevel.BEGINNER),
            ("langchain_chains", "LangChain Chains", "Chain composition and LCEL", ConceptType.FRAMEWORK_SPECIFIC, DifficultyLevel.INTERMEDIATE),
            ("langchain_memory", "LangChain Memory", "Memory systems and conversation state", ConceptType.FRAMEWORK_SPECIFIC, DifficultyLevel.INTERMEDIATE),
            ("langchain_agents", "LangChain Agents", "Agent frameworks and tool integration", ConceptType.FRAMEWORK_SPECIFIC, DifficultyLevel.ADVANCED),
            ("langchain_rag", "RAG with LangChain", "Retrieval Augmented Generation", ConceptType.FRAMEWORK_SPECIFIC, DifficultyLevel.ADVANCED),
            ("langchain_vectorstores", "Vector Stores", "Vector database integration", ConceptType.FRAMEWORK_SPECIFIC, DifficultyLevel.INTERMEDIATE),
        ]
        
        for concept_id, name, description, concept_type, difficulty in langchain_concepts:
            await self.add_concept(
                concept_id=concept_id,
                name=name,
                description=description,
                concept_type=concept_type,
                framework=SupportedFrameworks.LANGCHAIN,
                difficulty_level=difficulty
            )
        
        # LangGraph concepts
        langgraph_concepts = [
            ("langgraph_basics", "LangGraph Basics", "Fundamental LangGraph concepts", ConceptType.FUNDAMENTAL, DifficultyLevel.BEGINNER),
            ("langgraph_state", "State Management", "Graph state and data flow", ConceptType.FRAMEWORK_SPECIFIC, DifficultyLevel.INTERMEDIATE),
            ("langgraph_nodes", "Graph Nodes", "Creating and configuring nodes", ConceptType.FRAMEWORK_SPECIFIC, DifficultyLevel.INTERMEDIATE),
            ("langgraph_edges", "Conditional Edges", "Dynamic graph routing", ConceptType.FRAMEWORK_SPECIFIC, DifficultyLevel.ADVANCED),
            ("langgraph_persistence", "Graph Persistence", "Checkpointing and state persistence", ConceptType.FRAMEWORK_SPECIFIC, DifficultyLevel.ADVANCED),
            ("langgraph_streaming", "Streaming", "Real-time graph execution", ConceptType.FRAMEWORK_SPECIFIC, DifficultyLevel.ADVANCED),
        ]
        
        for concept_id, name, description, concept_type, difficulty in langgraph_concepts:
            await self.add_concept(
                concept_id=concept_id,
                name=name,
                description=description,
                concept_type=concept_type,
                framework=SupportedFrameworks.LANGGRAPH,
                difficulty_level=difficulty
            )
        
        # CrewAI concepts
        crewai_concepts = [
            ("crewai_basics", "CrewAI Basics", "Fundamental CrewAI concepts", ConceptType.FUNDAMENTAL, DifficultyLevel.BEGINNER),
            ("crewai_agents", "CrewAI Agents", "Agent creation and configuration", ConceptType.FRAMEWORK_SPECIFIC, DifficultyLevel.INTERMEDIATE),
            ("crewai_tasks", "CrewAI Tasks", "Task definition and management", ConceptType.FRAMEWORK_SPECIFIC, DifficultyLevel.INTERMEDIATE),
            ("crewai_crews", "CrewAI Crews", "Crew orchestration and coordination", ConceptType.FRAMEWORK_SPECIFIC, DifficultyLevel.ADVANCED),
            ("crewai_tools", "CrewAI Tools", "Tool integration and custom tools", ConceptType.FRAMEWORK_SPECIFIC, DifficultyLevel.ADVANCED),
        ]
        
        for concept_id, name, description, concept_type, difficulty in crewai_concepts:
            await self.add_concept(
                concept_id=concept_id,
                name=name,
                description=description,
                concept_type=concept_type,
                framework=SupportedFrameworks.CREWAI,
                difficulty_level=difficulty
            )
    
    async def _create_base_relationships(self) -> None:
        """Create fundamental relationships between concepts."""
        # LangChain relationships
        langchain_relations = [
            ("langchain_basics_to_prompts", "langchain_basics", "langchain_prompts", RelationType.PREREQUISITE, 1.0, "Basic LangChain knowledge required for prompts"),
            ("langchain_prompts_to_chains", "langchain_prompts", "langchain_chains", RelationType.PREREQUISITE, 0.9, "Prompt knowledge needed for chains"),
            ("langchain_chains_to_memory", "langchain_chains", "langchain_memory", RelationType.BUILDS_ON, 0.8, "Chains often use memory"),
            ("langchain_chains_to_agents", "langchain_chains", "langchain_agents", RelationType.PREREQUISITE, 0.8, "Chain knowledge helpful for agents"),
            ("langchain_memory_to_agents", "langchain_memory", "langchain_agents", RelationType.BUILDS_ON, 0.7, "Agents often use memory"),
            ("langchain_basics_to_vectorstores", "langchain_basics", "langchain_vectorstores", RelationType.PREREQUISITE, 0.7, "Basic knowledge needed for vector stores"),
            ("langchain_vectorstores_to_rag", "langchain_vectorstores", "langchain_rag", RelationType.PREREQUISITE, 0.9, "Vector stores essential for RAG"),
        ]
        
        for relation_id, source, target, rel_type, strength, description in langchain_relations:
            await self.add_relation(relation_id, source, target, rel_type, strength, description)
        
        # LangGraph relationships
        langgraph_relations = [
            ("langgraph_basics_to_state", "langgraph_basics", "langgraph_state", RelationType.PREREQUISITE, 1.0, "Basic LangGraph knowledge required for state management"),
            ("langgraph_state_to_nodes", "langgraph_state", "langgraph_nodes", RelationType.BUILDS_ON, 0.9, "State management needed for nodes"),
            ("langgraph_nodes_to_edges", "langgraph_nodes", "langgraph_edges", RelationType.PREREQUISITE, 0.8, "Node knowledge needed for conditional edges"),
            ("langgraph_state_to_persistence", "langgraph_state", "langgraph_persistence", RelationType.BUILDS_ON, 0.7, "State management builds to persistence"),
            ("langgraph_basics_to_streaming", "langgraph_basics", "langgraph_streaming", RelationType.BUILDS_ON, 0.6, "Basic concepts build to streaming"),
        ]
        
        for relation_id, source, target, rel_type, strength, description in langgraph_relations:
            await self.add_relation(relation_id, source, target, rel_type, strength, description)
        
        # CrewAI relationships
        crewai_relations = [
            ("crewai_basics_to_agents", "crewai_basics", "crewai_agents", RelationType.PREREQUISITE, 1.0, "Basic CrewAI knowledge required for agents"),
            ("crewai_agents_to_tasks", "crewai_agents", "crewai_tasks", RelationType.BUILDS_ON, 0.9, "Agent knowledge builds to task management"),
            ("crewai_tasks_to_crews", "crewai_tasks", "crewai_crews", RelationType.PREREQUISITE, 0.8, "Task knowledge needed for crew orchestration"),
            ("crewai_agents_to_tools", "crewai_agents", "crewai_tools", RelationType.BUILDS_ON, 0.7, "Agent knowledge builds to tool integration"),
        ]
        
        for relation_id, source, target, rel_type, strength, description in crewai_relations:
            await self.add_relation(relation_id, source, target, rel_type, strength, description)
    
    async def _extract_concepts_from_text(
        self,
        text: str,
        framework: SupportedFrameworks
    ) -> List[str]:
        """Extract mentioned concepts from text."""
        mentioned_concepts = []
        text_lower = text.lower()
        
        # Get concepts for the framework
        framework_concepts = self.framework_graphs.get(framework, set())
        
        for concept_id in framework_concepts:
            if concept_id in self.concepts:
                concept = self.concepts[concept_id]
                
                # Check if concept name or keywords are mentioned
                if concept.name.lower() in text_lower:
                    mentioned_concepts.append(concept_id)
                    continue
                
                # Check keywords
                for keyword in concept.keywords:
                    if keyword.lower() in text_lower:
                        mentioned_concepts.append(concept_id)
                        break
        
        return mentioned_concepts
    
    async def _analyze_learning_patterns(
        self,
        mentioned_concepts: List[str],
        context: Any  # TODO: Define IntelligentContext
    ) -> None:
        """Analyze learning patterns from concept interactions."""
        # Update concept success rates based on engagement and comprehension
        for concept_id in mentioned_concepts:
            if concept_id in self.concepts:
                concept = self.concepts[concept_id]
                
                # Update success rate based on engagement
                if context.engagement_level > 0.7:
                    concept.success_rate = min(1.0, concept.success_rate + 0.1)
                elif context.engagement_level < 0.3:
                    concept.success_rate = max(0.0, concept.success_rate - 0.05)
    
    async def _update_user_progress(
        self,
        user_id: str,
        mentioned_concepts: List[str],
        context: Any  # TODO: Define IntelligentContext
    ) -> None:
        """Update user progress for mentioned concepts."""
        if user_id not in self.user_progress:
            self.user_progress[user_id] = {}
        
        for concept_id in mentioned_concepts:
            current_mastery = self.user_progress[user_id].get(concept_id, 0.0)
            
            # Update mastery based on context
            mastery_increase = 0.1 * context.engagement_level * context.topic_familiarity
            new_mastery = min(1.0, current_mastery + mastery_increase)
            
            self.user_progress[user_id][concept_id] = new_mastery
    
    async def _map_topics_to_concepts(
        self,
        topics: List[str],
        framework: SupportedFrameworks
    ) -> List[str]:
        """Map topic names to concept IDs."""
        concept_ids = []
        framework_concepts = self.framework_graphs.get(framework, set())
        
        for topic in topics:
            topic_lower = topic.lower()
            
            # Find matching concepts
            for concept_id in framework_concepts:
                if concept_id in self.concepts:
                    concept = self.concepts[concept_id]
                    if (topic_lower in concept.name.lower() or 
                        any(keyword.lower() in topic_lower for keyword in concept.keywords)):
                        concept_ids.append(concept_id)
        
        return concept_ids
    
    async def _identify_knowledge_gaps(
        self,
        covered_concepts: List[str],
        framework_concepts: Set[str]
    ) -> List[str]:
        """Identify knowledge gaps based on covered concepts."""
        gaps = []
        
        for concept_id in covered_concepts:
            # Check if prerequisites are covered
            prerequisites = await self.get_concept_prerequisites(concept_id)
            
            for prereq in prerequisites:
                if prereq not in covered_concepts and prereq in framework_concepts:
                    if prereq not in gaps:
                        gaps.append(prereq)
        
        return gaps
    
    async def _recommend_next_concepts(
        self,
        covered_concepts: List[str],
        framework: SupportedFrameworks
    ) -> List[str]:
        """Recommend next concepts to learn."""
        recommendations = []
        framework_concepts = self.framework_graphs.get(framework, set())
        
        for concept_id in covered_concepts:
            # Get concepts that build on this one
            dependents = await self.get_concept_dependents(concept_id)
            
            for dependent in dependents:
                if dependent not in covered_concepts and dependent in framework_concepts:
                    # Check if all prerequisites are met
                    prereqs = await self.get_concept_prerequisites(dependent)
                    if all(prereq in covered_concepts for prereq in prereqs):
                        if dependent not in recommendations:
                            recommendations.append(dependent)
        
        # Sort by difficulty and success rate
        recommendations.sort(key=lambda x: (
            self.concepts[x].difficulty_level.value == "advanced" and 3 or
            self.concepts[x].difficulty_level.value == "intermediate" and 2 or 1,
            -self.concepts[x].success_rate
        ))
        
        return recommendations[:5]  # Return top 5 recommendations
    
    async def _calculate_learning_efficiency(self, covered_concepts: List[str]) -> float:
        """Calculate learning efficiency based on covered concepts."""
        if not covered_concepts:
            return 0.0
        
        total_efficiency = 0.0
        for concept_id in covered_concepts:
            if concept_id in self.concepts:
                concept = self.concepts[concept_id]
                # Efficiency based on success rate and access patterns
                efficiency = concept.success_rate * (1.0 / max(1, concept.times_accessed / 10))
                total_efficiency += efficiency
        
        return total_efficiency / len(covered_concepts)
    
    async def _get_concept_relationships(self, concept_ids: List[str]) -> List[Dict[str, Any]]:
        """Get relationships between concepts."""
        relationships = []
        
        for relation in self.relations.values():
            if (relation.source_concept_id in concept_ids and 
                relation.target_concept_id in concept_ids):
                
                source_name = self.concepts[relation.source_concept_id].name
                target_name = self.concepts[relation.target_concept_id].name
                
                relationships.append({
                    "source": source_name,
                    "target": target_name,
                    "type": relation.relation_type.value,
                    "strength": relation.strength,
                    "description": relation.description
                })
        
        return relationships
    
    async def _find_optimal_path(
        self,
        target_concepts: List[str],
        user_progress: Dict[str, float],
        framework: SupportedFrameworks,
        time_constraint: Optional[int]
    ) -> List[str]:
        """Find optimal learning path through concepts."""
        # Simple implementation - in practice, this would use graph algorithms
        path = []
        remaining_concepts = set(target_concepts)
        
        while remaining_concepts:
            # Find concept with all prerequisites met
            next_concept = None
            for concept_id in remaining_concepts:
                prereqs = await self.get_concept_prerequisites(concept_id)
                if all(prereq in path or user_progress.get(prereq, 0) > 0.7 for prereq in prereqs):
                    next_concept = concept_id
                    break
            
            if next_concept:
                path.append(next_concept)
                remaining_concepts.remove(next_concept)
            else:
                # Add missing prerequisites
                for concept_id in list(remaining_concepts):
                    prereqs = await self.get_concept_prerequisites(concept_id)
                    for prereq in prereqs:
                        if prereq not in path and user_progress.get(prereq, 0) <= 0.7:
                            path.append(prereq)
                            if prereq in remaining_concepts:
                                remaining_concepts.remove(prereq)
        
        return path
    
    async def _calculate_path_success_probability(
        self,
        concept_sequence: List[str],
        user_progress: Dict[str, float]
    ) -> float:
        """Calculate probability of success for a learning path."""
        if not concept_sequence:
            return 1.0
        
        total_probability = 1.0
        for concept_id in concept_sequence:
            if concept_id in self.concepts:
                concept = self.concepts[concept_id]
                user_mastery = user_progress.get(concept_id, 0.0)
                
                # Probability based on concept success rate and user's current mastery
                concept_probability = (concept.success_rate + user_mastery) / 2
                total_probability *= concept_probability
        
        return total_probability
    
    async def _update_graph_metrics(self) -> None:
        """Update knowledge graph metrics."""
        self.graph_metrics["total_concepts"] = len(self.concepts)
        self.graph_metrics["total_relations"] = len(self.relations)
        
        if self.concepts:
            difficulty_sum = sum(
                3 if concept.difficulty_level == DifficultyLevel.ADVANCED else
                2 if concept.difficulty_level == DifficultyLevel.INTERMEDIATE else 1
                for concept in self.concepts.values()
            )
            self.graph_metrics["average_concept_difficulty"] = difficulty_sum / len(self.concepts)
            
            # Most accessed concepts
            sorted_concepts = sorted(
                self.concepts.items(),
                key=lambda x: x[1].times_accessed,
                reverse=True
            )
            self.graph_metrics["most_accessed_concepts"] = [
                {"concept_id": concept_id, "name": concept.name, "access_count": concept.times_accessed}
                for concept_id, concept in sorted_concepts[:10]
            ]
    
    async def search_knowledge_base(
        self,
        query: str,
        framework: SupportedFrameworks,
        limit: int = 5
    ) -> Dict[str, Any]:
        """Search the knowledge base for relevant concepts."""
        try:
            # Extract relevant concepts from the query
            relevant_concepts = await self._extract_concepts_from_text(query, framework)
            
            # Get framework-specific concepts
            framework_concepts = self.framework_graphs.get(framework, set())
            
            # Filter to framework-specific concepts
            framework_relevant = [c for c in relevant_concepts if c in framework_concepts]
            
            # If no framework-specific concepts found, use general concepts
            if not framework_relevant and relevant_concepts:
                framework_relevant = relevant_concepts[:limit]
            
            # Get the concept nodes
            results = []
            for concept_id in framework_relevant[:limit]:
                # Add error handling for missing concept IDs
                if concept_id not in self.concepts:
                    logger.warning(f"Concept ID {concept_id} not found in knowledge graph")
                    continue
                    
                concept = self.concepts[concept_id]
                
                # Get related concepts
                related = []
                for relation_id, relation in self.relations.items():
                    if relation.source_concept_id == concept_id:
                        # Add error handling for missing target concepts
                        target_id = relation.target_concept_id
                        if target_id not in self.concepts:
                            logger.warning(f"Target concept ID {target_id} not found in knowledge graph")
                            continue
                            
                        target = self.concepts[target_id]
                        related.append({
                            "concept_id": target_id,
                            "name": target.name,
                            "relation_type": relation.relation_type.value
                        })
                
                # Build result object with safe access to properties
                result = {
                    "concept_id": concept_id,
                    "name": getattr(concept, 'name', 'Unknown'),
                    "description": getattr(concept, 'description', ''),
                    "concept_type": getattr(concept, 'concept_type', ConceptType.FUNDAMENTAL).value,
                    "difficulty_level": getattr(concept, 'difficulty_level', DifficultyLevel.INTERMEDIATE).value,
                    "keywords": getattr(concept, 'keywords', []),
                    "related_concepts": related
                }
                
                # Add optional fields if they exist
                if hasattr(concept, 'learning_objectives') and concept.learning_objectives:
                    result["learning_objectives"] = concept.learning_objectives
                if hasattr(concept, 'code_examples') and concept.code_examples:
                    result["code_examples"] = concept.code_examples
                if hasattr(concept, 'resources') and concept.resources:
                    result["resources"] = concept.resources
                
                results.append(result)
            
            return {
                "query": query,
                "framework": framework.value,
                "results": results,
                "total_matches": len(results),
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error searching knowledge base: {e}")
            return {
                "query": query,
                "framework": framework.value if framework else "unknown",
                "results": [],
                "total_matches": 0,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            } 