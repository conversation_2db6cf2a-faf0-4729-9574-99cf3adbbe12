{"user_id": "user_a4434c15", "programming_experience_years": 2, "python_skill_level": "beginner", "learning_pace": "moderate", "preferred_learning_style": "hands-on", "learning_goals": ["Learn framework fundamentals", "Build practical applications", "Master best practices"], "created_at": "2025-06-17T11:40:07.295617", "last_updated": "2025-06-17T11:43:35.630060", "preferences": {"notification_frequency": "daily", "difficulty_progression": "gradual", "feedback_style": "encouraging", "code_complexity": "medium", "interactive_mode": true, "save_progress": true}, "total_learning_time": 0.0, "current_streak": 0, "completed_modules": [], "current_framework": null, "achievements": [], "recent_sessions": [{"session_id": "cli_session_ab4cb18c", "start_time": "2025-06-17T11:43:35.626239", "end_time": null, "framework": "langchain", "topics_covered": [], "completion_percentage": 70.0, "effectiveness_score": 0.8, "user_satisfaction": 4.0, "constellation_used": "unknown", "notes": null}]}