"""
Enhanced Action Display System for GAAPF CLI.
Provides beautiful, interactive action feedback using Rich library.
"""

import time
from typing import Dict, Any, Optional, List
from datetime import datetime
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn
from rich.panel import Panel
from rich.table import Table
from rich.live import Live
from rich.text import Text
from rich.align import Align
from rich.padding import Padding
from rich import box
from rich.status import Status

console = Console()


class ActionDisplayManager:
    """Manages beautiful action displays for CLI operations."""
    
    def __init__(self):
        self.active_actions: Dict[str, Any] = {}
        self.console = console
        
    def show_action_start(self, action_type: str, description: str, details: Optional[Dict] = None) -> str:
        """Show the start of an action with beautiful formatting."""
        action_id = f"action_{int(time.time() * 1000)}"
        
        # Create action display
        if action_type == "search":
            icon = "🔍"
            color = "cyan"
            panel_title = "Web Search"
        elif action_type == "file_creation":
            icon = "📝"
            color = "green"
            panel_title = "File Creation"
        elif action_type == "tool_execution":
            icon = "🔧"
            color = "yellow"
            panel_title = "Tool Execution"
        elif action_type == "dependency_install":
            icon = "📦"
            color = "blue"
            panel_title = "Package Installation"
        elif action_type == "code_execution":
            icon = "⚡"
            color = "magenta"
            panel_title = "Code Execution"
        else:
            icon = "🤖"
            color = "white"
            panel_title = "Agent Action"
        
        # Create rich display
        text = Text()
        text.append(f"{icon} ", style=f"bold {color}")
        text.append(description, style=f"{color}")
        
        if details:
            details_text = Text()
            for key, value in details.items():
                details_text.append(f"\n  • {key}: ", style="dim")
                details_text.append(str(value), style="bold")
            text.append(details_text)
        
        panel = Panel(
            text,
            title=f"[bold {color}]{panel_title}[/bold {color}]",
            border_style=color,
            box=box.ROUNDED,
            padding=(0, 1)
        )
        
        self.console.print(panel)
        
        # Store action for later updates
        self.active_actions[action_id] = {
            "type": action_type,
            "description": description,
            "start_time": datetime.now(),
            "color": color,
            "icon": icon
        }
        
        return action_id
    
    def show_action_progress(self, action_id: str, progress_text: str, percentage: Optional[float] = None):
        """Show progress update for an action."""
        if action_id not in self.active_actions:
            return
            
        action = self.active_actions[action_id]
        
        text = Text()
        text.append(f"{action['icon']} ", style=f"bold {action['color']}")
        text.append(progress_text, style=action['color'])
        
        if percentage is not None:
            # Add progress bar
            bar_length = 20
            filled_length = int(bar_length * percentage / 100)
            bar = "█" * filled_length + "░" * (bar_length - filled_length)
            text.append(f"\n  {bar} {percentage:.1f}%", style="dim")
        
        self.console.print(f"  {text}")
    
    def show_action_success(self, action_id: str, result_message: str, details: Optional[Dict] = None):
        """Show successful completion of an action."""
        if action_id not in self.active_actions:
            return
            
        action = self.active_actions[action_id]
        duration = (datetime.now() - action['start_time']).total_seconds()
        
        text = Text()
        text.append("✅ ", style="bold green")
        text.append(result_message, style="green")
        text.append(f" ({duration:.1f}s)", style="dim green")
        
        if details:
            details_text = Text()
            for key, value in details.items():
                details_text.append(f"\n  • {key}: ", style="dim")
                details_text.append(str(value), style="bold green")
            text.append(details_text)
        
        panel = Panel(
            text,
            title="[bold green]Success[/bold green]",
            border_style="green",
            box=box.ROUNDED,
            padding=(0, 1)
        )
        
        self.console.print(panel)
        
        # Remove from active actions
        del self.active_actions[action_id]
    
    def show_action_error(self, action_id: str, error_message: str, error_details: Optional[str] = None):
        """Show error for an action."""
        if action_id not in self.active_actions:
            return
            
        action = self.active_actions[action_id]
        duration = (datetime.now() - action['start_time']).total_seconds()
        
        text = Text()
        text.append("❌ ", style="bold red")
        text.append(error_message, style="red")
        text.append(f" ({duration:.1f}s)", style="dim red")
        
        if error_details:
            text.append(f"\n  Details: {error_details}", style="dim red")
        
        panel = Panel(
            text,
            title="[bold red]Error[/bold red]",
            border_style="red",
            box=box.ROUNDED,
            padding=(0, 1)
        )
        
        self.console.print(panel)
        
        # Remove from active actions
        del self.active_actions[action_id]
    
    def show_proactive_actions_start(self, actions_count: int):
        """Show the start of proactive actions execution."""
        text = Text()
        text.append("🤖 ", style="bold blue")
        text.append("Executing Proactive Actions", style="bold blue")
        text.append(f" ({actions_count} actions)", style="dim blue")
        
        panel = Panel(
            text,
            title="[bold blue]AI Assistant[/bold blue]",
            border_style="blue",
            box=box.ROUNDED,
            padding=(0, 1)
        )
        
        self.console.print(panel)
    
    def show_tool_execution_start(self, tool_name: str, parameters: Dict[str, Any]) -> str:
        """Show the start of tool execution with parameters."""
        action_id = self.show_action_start(
            "tool_execution",
            f"Executing {tool_name}",
            {"parameters": str(parameters)[:100] + "..." if len(str(parameters)) > 100 else str(parameters)}
        )
        return action_id
    
    def show_file_creation_result(self, filename: str, success: bool, details: Optional[str] = None):
        """Show file creation result."""
        if success:
            self.console.print(f"✅ [green]Created file:[/green] [bold]{filename}[/bold]")
            if details:
                self.console.print(f"   [dim]{details}[/dim]")
        else:
            self.console.print(f"❌ [red]Failed to create file:[/red] [bold]{filename}[/bold]")
            if details:
                self.console.print(f"   [dim red]{details}[/dim red]")
    
    def show_dependency_installation(self, dependencies: List[str], results: Dict[str, bool]):
        """Show dependency installation results."""
        if not dependencies:
            return
            
        table = Table(title="Dependency Installation", box=box.ROUNDED)
        table.add_column("Package", style="cyan")
        table.add_column("Status", justify="center")
        table.add_column("Result", style="green")
        
        for dep in dependencies:
            status = "✅" if results.get(dep, False) else "❌"
            result = "Installed" if results.get(dep, False) else "Failed"
            result_style = "green" if results.get(dep, False) else "red"
            table.add_row(dep, status, f"[{result_style}]{result}[/{result_style}]")
        
        self.console.print(table)
    
    def show_code_execution_result(self, filename: str, success: bool, output: str = "", error: str = ""):
        """Show code execution result with output."""
        if success:
            panel_title = "[bold green]Execution Success[/bold green]"
            border_style = "green"
            icon = "✅"
        else:
            panel_title = "[bold red]Execution Failed[/bold red]"
            border_style = "red"
            icon = "❌"
        
        content = Text()
        content.append(f"{icon} ", style=f"bold {border_style}")
        content.append(f"File: {filename}", style="bold")
        
        if output.strip():
            content.append("\n\nOutput:", style="bold")
            content.append(f"\n{output}", style="dim")
        
        if error.strip():
            content.append("\n\nError:", style="bold red")
            content.append(f"\n{error}", style="red")
        
        panel = Panel(
            content,
            title=panel_title,
            border_style=border_style,
            box=box.ROUNDED,
            padding=(1, 1)
        )
        
        self.console.print(panel)
    
    def show_session_summary(self, session_data: Dict[str, Any]):
        """Show practice session summary."""
        table = Table(title="Practice Session Summary", box=box.ROUNDED)
        table.add_column("Metric", style="cyan")
        table.add_column("Value", style="green")
        
        for key, value in session_data.items():
            if key == "success_rate":
                value = f"{value:.1%}"
            elif key == "duration":
                value = f"{value:.1f}s"
            table.add_row(key.replace("_", " ").title(), str(value))
        
        self.console.print(table)


# Global instance
action_display = ActionDisplayManager() 