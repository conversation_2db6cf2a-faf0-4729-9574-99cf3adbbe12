"""
Instructor Agent - Primary teaching agent focused on clear explanations and learning guidance.
"""

from typing import Optional
from langchain_core.messages import HumanMessage
from langchain_core.language_models.chat_models import BaseChatModel

from .base_agent import BaseAgent
from ..core.constellation import ConstellationState, AgentRole


class InstructorAgent(BaseAgent):
    """Primary teaching agent focused on clear explanations and learning guidance."""
    
    def __init__(self, model: Optional[BaseChatModel] = None):
        super().__init__(AgentRole.INSTRUCTOR, model, "teaching")
    
    def get_system_prompt(self, state: ConstellationState) -> str:
        return f"""You are an expert AI framework instructor specializing in {state.framework.value}.

Current Context:
- Framework: {state.framework.value}
- Module: {state.module_id}
- Current Topic: {state.current_topic or "general learning"}
- Progress: {state.progress_percentage:.1f}%
- Topics Covered: {', '.join(state.topics_covered) if state.topics_covered else 'none yet'}

Your role is to:
1. Provide clear, structured explanations of concepts
2. Break down complex topics into digestible parts
3. Use analogies and examples relevant to the user's experience
4. Guide the learning progression logically
5. Ask engaging questions to check understanding
6. Adapt explanations to the user's skill level

Be encouraging, patient, and adaptive to the user's learning style. Focus on building solid foundational understanding before moving to advanced topics.

Respond as the instructor agent with clear, educational content."""
    
    def should_activate(self, state: ConstellationState) -> bool:
        # Activate for general explanations and teaching moments
        if not state.messages:
            return True
        
        latest_message = state.messages[-1]
        if isinstance(latest_message, HumanMessage):
            content_lower = latest_message.content.lower()
            teaching_keywords = ["explain", "what is", "how does", "why", "concept", "understand"]
            return any(keyword in content_lower for keyword in teaching_keywords)
        
        return False 