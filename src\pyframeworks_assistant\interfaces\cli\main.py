#!/usr/bin/env python3
"""
Command Line Interface for GAAPF - Guidance AI Agent for Python Framework
Real-time conversation with AI constellation agents using actual LLM API calls.
"""

import asyncio
import sys
import os
from typing import Optional, List
from datetime import datetime
import uuid
import signal
from rich.console import Console
import logging
import traceback
import time

# Add src to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, "..", "..", "..")
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

from pyframeworks_assistant.config.settings import settings
from pyframeworks_assistant.config.user_profiles import (
    UserProfile, SkillLevel, LearningPace, LearningStyle
)
from pyframeworks_assistant.config.user_persistence import (
    load_user_profile, save_user_profile, user_profile_exists, get_user_profile_summary
)
from pyframeworks_assistant.config.framework_configs import (
    SupportedFrameworks, get_all_frameworks, get_framework_config
)
from pyframeworks_assistant.core.constellation import (
    ConstellationType, ConstellationManager, ConstellationState
)
from pyframeworks_assistant.core.temporal_state import TemporalStateManager
from pyframeworks_assistant.core.models import llm_manager
from pyframeworks_assistant.core.intelligent_agent_manager import IntelligentAgentManager
from pyframeworks_assistant.agents import (
    InstructorAgent, CodeAssistantAgent, DocumentationExpertAgent,
    PracticeFacilitatorAgent, AssessmentAgent, MentorAgent,
    ResearchAssistantAgent, ProjectGuideAgent, TroubleshooterAgent,
    MotivationalCoachAgent, KnowledgeSynthesizerAgent, ProgressTrackerAgent
)
from pyframeworks_assistant.core.constellation import AgentRole
from pyframeworks_assistant.memory.conversation_memory import memory_manager
from langchain_core.messages import HumanMessage, AIMessage

# Setup debug logging
DEBUG_MODE = True
logging.basicConfig(
    level=logging.DEBUG if DEBUG_MODE else logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('cli_debug.log')
    ]
)
logger = logging.getLogger("CLI")

class Colors:
    """ANSI color codes for terminal output."""
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

# Add debug decorator function
def debug_step(func):
    """Decorator to add debug output to methods."""
    def wrapper(*args, **kwargs):
        if DEBUG_MODE:
            print(f"\n{Colors.WARNING}DEBUG: Entering {func.__name__}{Colors.ENDC}")
            start_time = time.time()
        
        result = func(*args, **kwargs)
        
        if DEBUG_MODE:
            elapsed = time.time() - start_time
            print(f"{Colors.WARNING}DEBUG: Exiting {func.__name__} (took {elapsed:.2f}s){Colors.ENDC}")
        
        return result
    return wrapper

def async_debug_step(func):
    """Decorator to add debug output to async methods."""
    async def wrapper(*args, **kwargs):
        if DEBUG_MODE:
            print(f"\n{Colors.WARNING}DEBUG: Entering async {func.__name__}{Colors.ENDC}")
            start_time = time.time()
        
        result = await func(*args, **kwargs)
        
        if DEBUG_MODE:
            elapsed = time.time() - start_time
            print(f"{Colors.WARNING}DEBUG: Exiting async {func.__name__} (took {elapsed:.2f}s){Colors.ENDC}")
        
        return result
    return wrapper

class CLILearningSystem:
    """Main CLI interface for the learning system."""
    
    def __init__(self):
        """Initialize the CLI learning system."""
        if DEBUG_MODE:
            print(f"{Colors.WARNING}DEBUG: Initializing CLILearningSystem{Colors.ENDC}")
        self.user_profile: Optional[UserProfile] = None
        self.constellation_manager = ConstellationManager()
        self.temporal_manager = TemporalStateManager()
        self.session_id = f"cli_session_{uuid.uuid4().hex[:8]}"
        self.current_state: Optional[ConstellationState] = None
        self.conversation_memory = memory_manager.get_or_create_session(self.session_id)
        
        # Initialize agents
        self.agents = {
            AgentRole.INSTRUCTOR: InstructorAgent(),
            AgentRole.CODE_ASSISTANT: CodeAssistantAgent(),
            AgentRole.DOCUMENTATION_EXPERT: DocumentationExpertAgent(),
            AgentRole.PRACTICE_FACILITATOR: PracticeFacilitatorAgent(),
            AgentRole.ASSESSMENT_AGENT: AssessmentAgent(),
            AgentRole.MENTOR: MentorAgent(),
            AgentRole.RESEARCH_ASSISTANT: ResearchAssistantAgent(),
            AgentRole.PROJECT_GUIDE: ProjectGuideAgent(),
            AgentRole.TROUBLESHOOTER: TroubleshooterAgent(),
            AgentRole.MOTIVATIONAL_COACH: MotivationalCoachAgent(),
            AgentRole.KNOWLEDGE_SYNTHESIZER: KnowledgeSynthesizerAgent(),
            AgentRole.PROGRESS_TRACKER: ProgressTrackerAgent(),
        }
        
        # Setup signal handler for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle Ctrl+C gracefully with session saving."""
        print(f"\n{Colors.WARNING}Saving session progress...{Colors.ENDC}")
        
        # Save user profile if it exists and has been modified
        if self.user_profile and self.current_state:
            try:
                # Update learning session data
                from pyframeworks_assistant.config.user_profiles import LearningSession
                session_data = LearningSession(
                    session_id=self.session_id,
                    start_time=datetime.now(),  # Would be better to track actual start time
                    framework=self.current_state.framework.value,
                    topics_covered=self.current_state.topics_covered if hasattr(self.current_state, 'topics_covered') else [],
                    completion_percentage=len(self.current_state.messages) * 10,  # Rough estimate
                    effectiveness_score=0.8,  # Would be calculated from actual metrics
                    user_satisfaction=4.0,  # Default value
                    constellation_used=getattr(self.current_state, 'constellation_type', 'unknown')
                )
                
                # Add session to user profile
                self.user_profile.add_learning_session(session_data)
                
                # Save updated profile
                if save_user_profile(self.user_profile):
                    print(f"{Colors.OKGREEN}✅ Session progress saved!{Colors.ENDC}")
                else:
                    print(f"{Colors.WARNING}⚠️ Failed to save session progress{Colors.ENDC}")
                    
            except Exception as e:
                print(f"{Colors.WARNING}⚠️ Error saving session: {e}{Colors.ENDC}")
        
        print(f"{Colors.WARNING}Goodbye! Thanks for learning with us!{Colors.ENDC}")
        sys.exit(0)
    
    def print_banner(self):
        """Print the application banner."""
        banner = f"""
{Colors.HEADER}{Colors.BOLD}
╔══════════════════════════════════════════════════════════════════╗
║             GAAPF - Guidance AI Agent for Python Framework      ║
║           Adaptive Multi-Agent Learning System                  ║
║                     CLI Interface v1.0                          ║
╚══════════════════════════════════════════════════════════════════╝
{Colors.ENDC}

{Colors.OKCYAN}🤖 Real-time AI constellation agents with LLM integration{Colors.ENDC}
{Colors.OKGREEN}🎯 Personalized learning paths for Python frameworks{Colors.ENDC}
{Colors.OKBLUE}⚡ Temporal optimization and adaptive responses{Colors.ENDC}
"""
        print(banner)
    
    @debug_step
    def setup_user_profile(self) -> UserProfile:
        """Interactive user profile setup with persistence."""
        print(f"\n{Colors.BOLD}👤 User Profile Setup{Colors.ENDC}")
        print("Let's create your learning profile for personalized assistance.")
        
        # User ID
        default_id = f"user_{uuid.uuid4().hex[:8]}"
        user_id = input(f"\nEnter your user ID (default: {default_id}): ").strip()
        if not user_id:
            user_id = default_id
        
        # Check if user profile already exists
        if user_profile_exists(user_id):
            print(f"\n{Colors.OKGREEN}✅ Found existing profile for user: {user_id}{Colors.ENDC}")
            
            # Show profile summary
            summary = get_user_profile_summary(user_id)
            if summary:
                print(f"{Colors.OKCYAN}📊 Profile Summary:{Colors.ENDC}")
                print(f"   • Python Level: {summary.get('python_skill_level', 'Unknown').title()}")
                print(f"   • Learning Style: {summary.get('preferred_learning_style', 'unknown').replace('_', ' ').title()}")
                print(f"   • Learning Pace: {summary.get('learning_pace', 'unknown').title()}")
                print(f"   • Total Sessions: {summary.get('total_sessions', 0)}")
                frameworks_learned = summary.get('frameworks_learned', [])
                if frameworks_learned:
                    print(f"   • Frameworks: {', '.join(frameworks_learned)}")
                print(f"   • Last Updated: {summary.get('last_updated', 'Unknown')[:10]}")
            
            # Ask if user wants to continue with existing profile
            choice = input(f"\n{Colors.BOLD}Use existing profile? (y/n, default: y): {Colors.ENDC}").strip().lower()
            if choice in ['', 'y', 'yes']:
                existing_profile = load_user_profile(user_id)
                if existing_profile:
                    print(f"{Colors.OKGREEN}✅ Profile loaded successfully!{Colors.ENDC}")
                    return existing_profile
                else:
                    print(f"{Colors.WARNING}⚠️ Failed to load profile, creating new one...{Colors.ENDC}")
            else:
                print(f"{Colors.WARNING}⚠️ Creating new profile (existing will be overwritten)...{Colors.ENDC}")
        else:
            print(f"\n{Colors.OKCYAN}🆕 Creating new profile for user: {user_id}{Colors.ENDC}")
        
        # Programming experience
        while True:
            try:
                years = input("Programming experience in years (0-20): ").strip()
                years = int(years) if years else 2
                if 0 <= years <= 20:
                    break
                print(f"{Colors.WARNING}Please enter a number between 0 and 20{Colors.ENDC}")
            except ValueError:
                print(f"{Colors.WARNING}Please enter a valid number{Colors.ENDC}")
        
        # Python skill level
        print("\nPython Skill Levels:")
        for i, skill in enumerate(SkillLevel, 1):
            print(f"  {i}. {skill.value.title()}")
        
        while True:
            try:
                choice = input("Select your Python skill level (1-4): ").strip()
                choice = int(choice) if choice else 2
                if 1 <= choice <= len(SkillLevel):
                    python_skill = list(SkillLevel)[choice - 1]
                    break
                print(f"{Colors.WARNING}Please enter a number between 1 and {len(SkillLevel)}{Colors.ENDC}")
            except ValueError:
                print(f"{Colors.WARNING}Please enter a valid number{Colors.ENDC}")
        
        # Learning pace
        print("\nLearning Pace:")
        for i, pace in enumerate(LearningPace, 1):
            print(f"  {i}. {pace.value.title()}")
        
        while True:
            try:
                choice = input("Select your learning pace (1-3): ").strip()
                choice = int(choice) if choice else 2
                if 1 <= choice <= len(LearningPace):
                    learning_pace = list(LearningPace)[choice - 1]
                    break
                print(f"{Colors.WARNING}Please enter a number between 1 and {len(LearningPace)}{Colors.ENDC}")
            except ValueError:
                print(f"{Colors.WARNING}Please enter a valid number{Colors.ENDC}")
        
        # Learning style
        print("\nLearning Style:")
        for i, style in enumerate(LearningStyle, 1):
            print(f"  {i}. {style.value.replace('_', ' ').title()}")
        
        while True:
            try:
                choice = input("Select your learning style (1-4): ").strip()
                choice = int(choice) if choice else 2
                if 1 <= choice <= len(LearningStyle):
                    learning_style = list(LearningStyle)[choice - 1]
                    break
                print(f"{Colors.WARNING}Please enter a number between 1 and {len(LearningStyle)}{Colors.ENDC}")
            except ValueError:
                print(f"{Colors.WARNING}Please enter a valid number{Colors.ENDC}")
        
        # Learning goals
        print("\nLearning Goals (press Enter after each goal, empty line to finish):")
        goals = []
        while True:
            goal = input("Goal: ").strip()
            if not goal:
                break
            goals.append(goal)
        
        if not goals:
            goals = ["Learn framework fundamentals", "Build practical applications", "Master best practices"]
        
        # Create profile
        profile = UserProfile(
            user_id=user_id,
            programming_experience_years=years,
            python_skill_level=python_skill,
            learning_pace=learning_pace,
            preferred_learning_style=learning_style,
            learning_goals=goals
        )
        
        # Save profile to file
        if save_user_profile(profile):
            print(f"\n{Colors.OKGREEN}✅ Profile created and saved successfully!{Colors.ENDC}")
        else:
            print(f"\n{Colors.WARNING}✅ Profile created successfully! (Save failed, but continuing...){Colors.ENDC}")
        
        return profile
    
    async def process_user_message(self, message: str) -> None:
        """Process user message with intelligent agent selection and proactive actions."""
        if not self.current_state:
            print(f"{Colors.FAIL}Error: No active learning session{Colors.ENDC}")
            return

        # Add user message to state
        user_message = HumanMessage(content=message)
        self.current_state.messages.append(user_message)
        self.conversation_memory.add_message(user_message, {"timestamp": datetime.now().isoformat()})

        print(f"\n{Colors.OKCYAN}🧠 Analyzing your request intelligently...{Colors.ENDC}")

        try:
            # Build intelligent context
            context = IntelligentAgentManager.build_intelligent_context(
                self.current_state, 
                self.user_profile, 
                message
            )
            
            # Display intelligent analysis
            print(f"{Colors.OKBLUE}📊 Intent: {context.user_intent.value.title()}{Colors.ENDC}")
            print(f"{Colors.OKBLUE}📈 Engagement: {context.engagement_level:.1f} | Learning Velocity: {context.learning_velocity:.1f}{Colors.ENDC}")
            
            # Determine proactive actions
            proactive_actions = IntelligentAgentManager.determine_proactive_actions(context, self.current_state)
            
            # Execute proactive actions
            if proactive_actions:
                action_results = await IntelligentAgentManager.execute_proactive_actions(
                    proactive_actions, self.current_state
                )

            # Select optimal agent using intelligent analysis
            best_agent, confidence = IntelligentAgentManager.select_optimal_agent(
                self.agents, context, self.current_state
            )
            
            print(f"\n{Colors.OKGREEN}🎯 Selected Agent: {best_agent.role.value.replace('_', ' ').title()} (Confidence: {confidence:.2f}){Colors.ENDC}")

            # Get response from the selected agent
            response = await best_agent.process(self.current_state)

            # Add AI response to state and memory
            ai_message = AIMessage(content=response.content)
            self.current_state.messages.append(ai_message)
            self.conversation_memory.add_message(
                ai_message, 
                {
                    "agent_role": response.agent_role.value,
                    "confidence": response.confidence_score,
                    "user_intent": context.user_intent.value,
                    "engagement_level": context.engagement_level,
                    "timestamp": datetime.now().isoformat()
                }
            )

            # Display response with enhanced information
            agent_name = response.agent_role.value.replace('_', ' ').title()
            print(f"\n{Colors.BOLD}{Colors.OKGREEN}🤖 {agent_name}:{Colors.ENDC}")
            print(f"{response.content}")

            # Show enhanced metrics
            confidence_color = Colors.OKGREEN if response.confidence_score > 0.7 else Colors.WARNING
            print(f"\n{confidence_color}Agent Confidence: {response.confidence_score:.2f}{Colors.ENDC}")
            
            # Show intelligent insights
            if context.knowledge_gaps:
                print(f"{Colors.WARNING}💡 Knowledge Gaps Identified: {', '.join(context.knowledge_gaps[:2])}{Colors.ENDC}")
            
            if context.recent_struggles:
                print(f"{Colors.WARNING}⚠️ Recent Challenges: {', '.join(context.recent_struggles)}{Colors.ENDC}")

            # Show suggested handoff with reasoning
            if response.suggested_handoff:
                suggested_agent = response.suggested_handoff.value.replace('_', ' ').title()
                print(f"{Colors.OKCYAN}💡 Suggested Next Agent: {suggested_agent}{Colors.ENDC}")
                
                # Provide intelligent reasoning for handoff
                if context.user_intent.value == "coding" and response.suggested_handoff.value == "code_assistant":
                    print(f"{Colors.OKCYAN}   Reason: You seem ready for hands-on coding practice{Colors.ENDC}")
                elif context.engagement_level < 0.5:
                    print(f"{Colors.OKCYAN}   Reason: Let's try a different approach to keep you engaged{Colors.ENDC}")

            # Show proactive suggestions based on context
            await self._show_proactive_suggestions(context, self.current_state)

        except Exception as e:
            print(f"{Colors.FAIL}❌ Error processing message: {e}{Colors.ENDC}")
            print(f"{Colors.WARNING}Please try rephrasing your question or check your API configuration.{Colors.ENDC}")
            import traceback
            traceback.print_exc()

    @debug_step
    def _display_framework_overview(self, framework_context):
        """Display framework overview to the user."""
        print(f"\n{Colors.BOLD}📚 {framework_context.framework.value.upper()} Framework Overview{Colors.ENDC}")
        
        if DEBUG_MODE:
            print(f"{Colors.WARNING}DEBUG: Displaying framework overview for {framework_context.framework.value}{Colors.ENDC}")
            print(f"{Colors.WARNING}DEBUG: Version: {framework_context.version}{Colors.ENDC}")
        
        # Key concepts
        print(f"\n{Colors.OKBLUE}🎯 Key Concepts to Master:{Colors.ENDC}")
        for i, concept in enumerate(framework_context.key_concepts[:6], 1):
            print(f"   {i}. {concept}")
        
        # Learning path preview
        print(f"\n{Colors.OKBLUE}📖 Your Learning Journey:{Colors.ENDC}")
        for i, step in enumerate(framework_context.learning_path[:4], 1):
            print(f"   {i}. {step}")
        if len(framework_context.learning_path) > 4:
            print(f"   ... and {len(framework_context.learning_path) - 4} more steps")
        
        # Quick start info
        print(f"\n{Colors.OKBLUE}⚡ Quick Start:{Colors.ENDC}")
        if framework_context.syntax.common_imports:
            print(f"   Essential imports: {framework_context.syntax.common_imports[0]}")
        
        print(f"\n{Colors.OKCYAN}💡 I have comprehensive context about {framework_context.framework.value} including:")
        print(f"   • Syntax patterns and examples • Core modules and functions")
        print(f"   • Practice exercises • Integration patterns • Troubleshooting guide{Colors.ENDC}")

        print(f"\n{Colors.OKBLUE}🎓 Curriculum-Driven Learning:{Colors.ENDC}")
        print(f"   • Your learning path adapts to your progress")
        print(f"   • Quiz results influence topic progression")
        print(f"   • The system tracks mastery of each concept")
        
        # Enhanced curriculum display
        print(f"\n{Colors.BOLD}📋 Detailed Curriculum:{Colors.ENDC}")
        print(f"\n{Colors.OKBLUE}Phase 1: Foundation{Colors.ENDC}")
        for i, step in enumerate(framework_context.learning_path[:3], 1):
            print(f"   {i}. {step}")
        
        print(f"\n{Colors.OKBLUE}Phase 2: Application{Colors.ENDC}")
        for i, step in enumerate(framework_context.learning_path[3:6], 4):
            if i-1 < len(framework_context.learning_path):
                print(f"   {i}. {step}")
        
        print(f"\n{Colors.OKBLUE}Phase 3: Mastery{Colors.ENDC}")
        for i, step in enumerate(framework_context.learning_path[6:9], 7):
            if i-1 < len(framework_context.learning_path):
                print(f"   {i}. {step}")
                
        # Display practice examples
        print(f"\n{Colors.BOLD}🧪 Practice Examples:{Colors.ENDC}")
        for i, example in enumerate(framework_context.practice_examples[:2], 1):
            print(f"\n   Example {i}: {example.title}")
            print(f"   Difficulty: {example.difficulty_level}")
            print(f"   Concepts: {', '.join(example.concepts_demonstrated[:3])}")
        
        print(f"\n{Colors.BOLD}Ready to start learning? Ask me anything about {framework_context.framework.value}!{Colors.ENDC}")
    
        if DEBUG_MODE:
            print(f"\n{Colors.WARNING}DEBUG: Curriculum information:{Colors.ENDC}")
            print(f"   Learning path length: {len(framework_context.learning_path)} steps")
            print(f"   Practice examples: {len(framework_context.practice_examples)} available")
            print(f"   Core modules: {len(framework_context.core_modules)} modules")
    
    @debug_step
    def show_help(self):
        """Show available commands and tips."""
        help_text = f"""
{Colors.BOLD}📚 Available Commands:{Colors.ENDC}
• Type any question about your selected framework
• {Colors.OKCYAN}/help{Colors.ENDC} - Show this help message
• {Colors.OKCYAN}/status{Colors.ENDC} - Show current session status
• {Colors.OKCYAN}/agents{Colors.ENDC} - List available agents
• {Colors.OKCYAN}/curriculum{Colors.ENDC} or {Colors.OKCYAN}/p{Colors.ENDC} - View curriculum progress and mastery levels
• {Colors.OKCYAN}/clear{Colors.ENDC} - Clear conversation history
• {Colors.OKCYAN}/quit{Colors.ENDC} or {Colors.OKCYAN}/exit{Colors.ENDC} - Exit the application
• {Colors.OKCYAN}Ctrl+C{Colors.ENDC} - Quick exit

{Colors.BOLD}💡 Tips:{Colors.ENDC}
• Ask specific questions about concepts, code examples, or implementation
• Request hands-on exercises or practice problems
• Ask for explanations at different complexity levels
• Request documentation references or troubleshooting help
• The AI will automatically select the best agent for your question

{Colors.BOLD}📝 Learning Features:{Colors.ENDC}
• Type "quiz me" or "test my knowledge" to generate a quiz on the current topic
• Quizzes adapt to your skill level and track your progress
• Your quiz performance helps customize your learning path
"""
        print(help_text)
    
    @debug_step
    def show_status(self):
        """Show current session status."""
        if not self.current_state:
            print(f"{Colors.WARNING}No active learning session{Colors.ENDC}")
            return
        
        status = f"""
{Colors.BOLD}📊 Session Status:{Colors.ENDC}
• Session ID: {self.session_id}
• User: {self.user_profile.user_id}
• Framework: {self.current_state.framework.value}
• Constellation: {self.current_state.constellation_type.value.replace('_', ' ').title()}
• Module: {self.current_state.module_id}
• Messages: {len(self.current_state.messages)}
• Progress: {self.current_state.progress_percentage:.1f}%
• Active Agents: {len(self.current_state.active_agents)}

{Colors.BOLD}🎯 Learning Goals:{Colors.ENDC}
"""
        for i, goal in enumerate(self.user_profile.learning_goals, 1):
            status += f"  {i}. {goal}\n"
        
    @debug_step
    def select_framework(self) -> SupportedFrameworks:
        """Interactive framework selection."""
        print(f"\n{Colors.BOLD}🔧 Framework Selection{Colors.ENDC}")
        
        frameworks = get_all_frameworks()
        print("Available frameworks:")
        for i, framework in enumerate(frameworks, 1):
            config = get_framework_config(framework)
            print(f"  {i}. {config.display_name} ({config.latest_version}) - {config.learning_complexity.value.title()}")
        
        while True:
            try:
                choice = input(f"Select a framework (1-{len(frameworks)}): ").strip()
                choice = int(choice) if choice else 1
                if 1 <= choice <= len(frameworks):
                    selected_framework = frameworks[choice - 1]
                    break
                print(f"{Colors.WARNING}Please enter a number between 1 and {len(frameworks)}{Colors.ENDC}")
            except ValueError:
                print(f"{Colors.WARNING}Please enter a valid number{Colors.ENDC}")
        
        config = get_framework_config(selected_framework)
        print(f"\n{Colors.OKGREEN}Selected: {config.display_name}{Colors.ENDC}")
        print(f"Use Cases: {', '.join(config.primary_use_cases[:3])}")
        
        print(f"\n{Colors.OKCYAN}🔍 Framework Discovery:{Colors.ENDC}")
        print(f"   The system will use Tavily tools to gather the latest information about {selected_framework.value}")
        print(f"   This includes documentation, syntax patterns, and best practices")
        print(f"   All information will be stored for your personalized learning experience")
        
        if DEBUG_MODE:
            print(f"\n{Colors.WARNING}DEBUG: Framework Selection Details:{Colors.ENDC}")
            print(f"   Framework ID: {selected_framework.value}")
            print(f"   Latest Version: {config.latest_version}")
            print(f"   Complexity: {config.learning_complexity.value}")
            print(f"   Documentation URL: {config.documentation_url}")
            if hasattr(config, 'github_url'):
                print(f"   GitHub URL: {config.github_url}")
            else:
                print(f"   GitHub URL: Not available")
        
        return selected_framework
    
    @debug_step
    def get_optimal_constellation(self, framework: SupportedFrameworks) -> ConstellationType:
        """Get optimal constellation for user and framework."""
        optimal_constellation = self.temporal_manager.get_optimal_constellation(
            self.user_profile, framework, {}
        )
        
        print(f"\n{Colors.OKBLUE}🌟 Recommended Learning Constellation:{Colors.ENDC}")
        print(f"   {optimal_constellation.value.replace('_', ' ').title()}")
        print("   This constellation is optimized for your learning style and experience level.")
        
        return optimal_constellation
    
    @async_debug_step
    async def initialize_learning_session(self, framework: SupportedFrameworks, constellation_type: ConstellationType):
        """Initialize the learning session state with comprehensive framework context."""
        print(f"\n{Colors.OKCYAN}🔧 Initializing comprehensive framework context...{Colors.ENDC}")
        
        # Import framework initializer
        from ...core.framework_initializer import dynamic_initializer as framework_initializer
        
        try:
            if DEBUG_MODE:
                print(f"{Colors.WARNING}DEBUG: Starting framework context initialization{Colors.ENDC}")
                print(f"{Colors.WARNING}DEBUG: Framework: {framework.value}{Colors.ENDC}")
            
            # Generate comprehensive framework context
            try:
                framework_context = await framework_initializer.initialize_framework_context(framework)
            except Exception as e:
                if DEBUG_MODE:
                    print(f"{Colors.WARNING}DEBUG: Error with dynamic initialization, using fallback: {str(e)}{Colors.ENDC}")
                # Use fallback context for testing when Tavily API fails
                framework_context = framework_initializer._get_fallback_context(framework)
            
            if DEBUG_MODE:
                print(f"{Colors.WARNING}DEBUG: Framework context initialized successfully{Colors.ENDC}")
            
            print(f"{Colors.OKGREEN}✅ Framework context generated successfully!{Colors.ENDC}")
            print(f"   • Key concepts: {len(framework_context.key_concepts)} identified")
            print(f"   • Core modules: {len(framework_context.core_modules)} analyzed")
            print(f"   • Practice examples: {len(framework_context.practice_examples)} prepared")
            print(f"   • Learning path: {len(framework_context.learning_path)} steps defined")
            
            if DEBUG_MODE:
                print(f"{Colors.WARNING}DEBUG: Framework Context Details:{Colors.ENDC}")
                print(f"   • Version: {framework_context.version}")
                print(f"   • Source URLs: {len(framework_context.source_urls)} sources")
                print(f"   • Created at: {framework_context.created_at}")
            
        except Exception as e:
            print(f"{Colors.WARNING}⚠️ Using basic framework context due to initialization error: {e}{Colors.ENDC}")
            if DEBUG_MODE:
                print(f"{Colors.FAIL}DEBUG: Framework initialization error:{Colors.ENDC}")
                traceback.print_exc()
            # Create a minimal framework context for testing
            from ...core.framework_initializer import FrameworkContext, FrameworkSyntax, CoreModule, PracticeExample
            from datetime import datetime
            
            framework_context = FrameworkContext(
                framework=framework,
                version="latest",
                description=f"{framework.value} - AI Framework for Python",
                key_concepts=["Agents", "Chains", "Memory", "Tools", "Callbacks", "Document Loaders"],
                syntax=FrameworkSyntax(
                    basic_patterns=["import pattern", "basic usage"],
                    advanced_patterns=["advanced pattern"],
                    common_imports=[f"from {framework.value} import X"],
                    boilerplate_code="# Sample code",
                    naming_conventions={},
                    source_urls=[]
                ),
                core_modules=[
                    CoreModule(
                        name="Core Module 1",
                        description="Core functionality",
                        primary_functions=["function1", "function2"],
                        usage_examples=[],
                        best_practices=[],
                        common_pitfalls=[],
                        source_urls=[]
                    )
                ],
                practice_examples=[
                    PracticeExample(
                        title="Hello World Example",
                        description="Basic example to get started",
                        difficulty_level="beginner",
                        code_example="print('Hello world')",
                        explanation="Simple example to demonstrate the framework",
                        concepts_demonstrated=["Basic usage"],
                        next_steps=["Try more advanced features"],
                        source_urls=[]
                    ),
                    PracticeExample(
                        title="Advanced Example",
                        description="More complex example",
                        difficulty_level="intermediate",
                        code_example="# Advanced code here",
                        explanation="Demonstrates advanced features",
                        concepts_demonstrated=["Advanced usage"],
                        next_steps=["Explore integration patterns"],
                        source_urls=[]
                    )
                ],
                learning_path=[
                    "Introduction to basics",
                    "Core concepts",
                    "Building your first application",
                    "Working with advanced features",
                    "Integration patterns",
                    "Best practices",
                    "Performance optimization",
                    "Real-world applications",
                    "Contributing to the ecosystem"
                ],
                common_use_cases=["Use case 1", "Use case 2"],
                integration_patterns=[],
                troubleshooting_guide={},
                resources={},
                created_at=datetime.now(),
                source_urls=[]
            )
        
        # Create learning session state
        self.current_state = ConstellationState(
            session_id=self.session_id,
            user_id=self.user_profile.user_id,
            framework=framework,
            module_id="introduction",
            constellation_type=constellation_type,
            messages=[]
        )
        
        # Store framework context in the session for agents to use
        if framework_context:
            self.current_state.framework_context = framework_context
            
            # Add framework context summary to conversation memory for agents
            context_summary = framework_initializer.get_context_summary(framework)
            if context_summary:
                from langchain_core.messages import SystemMessage
                system_message = SystemMessage(content=f"Framework Context:\n{context_summary}")
                self.current_state.messages.append(system_message)
                
                if DEBUG_MODE:
                    print(f"{Colors.WARNING}DEBUG: Added framework context summary to conversation memory{Colors.ENDC}")
        
        # Initialize constellation manager with state
        self.constellation_manager.current_state = self.current_state
        
        print(f"\n{Colors.OKGREEN}🚀 Learning session initialized!{Colors.ENDC}")
        print(f"Session ID: {self.session_id}")
        print(f"Framework: {framework.value}")
        print(f"Constellation: {constellation_type.value}")
        
        # Show framework overview to user
        if framework_context:
            self._display_framework_overview(framework_context)
    
    @async_debug_step
    async def _show_proactive_suggestions(self, context, state: ConstellationState) -> None:
        """Show proactive suggestions based on intelligent analysis."""
        suggestions = []
        
        # Learning velocity suggestions
        if context.learning_velocity < 0.3:
            suggestions.append("💡 Try asking for a hands-on exercise to boost your learning")
        
        # Engagement suggestions
        if context.engagement_level < 0.5:
            suggestions.append("💡 Consider switching to a different topic or asking for a practical example")
        
        # Progress-based suggestions
        if state.progress_percentage > 30 and context.user_intent.value == "learning":
            suggestions.append("💡 You're making good progress! Ready to try building something?")
        
        # Framework-specific suggestions
        if state.framework.value == "langchain" and context.topic_familiarity < 0.4:
            suggestions.append("💡 Ask me about the latest LangChain features or best practices")
        
        # Show suggestions
        if suggestions:
            print(f"\n{Colors.BOLD}🎯 Smart Suggestions:{Colors.ENDC}")
            for suggestion in suggestions[:2]:  # Show top 2
                print(f"   {suggestion}")
                
        if DEBUG_MODE:
            print(f"{Colors.WARNING}DEBUG: Generated {len(suggestions)} proactive suggestions{Colors.ENDC}")

    @debug_step
    def list_agents(self):
        """List all available agents and their specializations."""
        print(f"\n{Colors.BOLD}🤖 Available AI Agents:{Colors.ENDC}")
        for agent_role, agent in self.agents.items():
            name = agent_role.value.replace('_', ' ').title()
            specialization = agent.specialization.title()
            activations = agent.activation_count
            print(f"  • {Colors.OKBLUE}{name}{Colors.ENDC} - {specialization} (Used: {activations} times)")
    
    @debug_step
    def clear_conversation(self):
        """Clear conversation history."""
        if self.current_state:
            self.current_state.messages = []
        print(f"{Colors.OKGREEN}✅ Conversation history cleared{Colors.ENDC}")
    
    @debug_step
    def show_curriculum_status(self):
        """Show curriculum progress and learning path."""
        if not self.current_state or not hasattr(self.current_state, 'curriculum_path_id') or not self.current_state.curriculum_path_id:
            print(f"{Colors.WARNING}No active curriculum{Colors.ENDC}")
            
            if DEBUG_MODE:
                print(f"{Colors.WARNING}DEBUG: Curriculum status check failed{Colors.ENDC}")
                print(f"   Has current_state: {self.current_state is not None}")
                if self.current_state:
                    print(f"   Has curriculum_path_id attribute: {hasattr(self.current_state, 'curriculum_path_id')}")
                    if hasattr(self.current_state, 'curriculum_path_id'):
                        print(f"   curriculum_path_id value: {self.current_state.curriculum_path_id}")
                    
                    # Show what attributes are available
                    print(f"   Available attributes: {dir(self.current_state)}")
            
            # Show framework context if available
            if self.current_state and hasattr(self.current_state, 'framework_context') and self.current_state.framework_context:
                print(f"\n{Colors.OKGREEN}Framework information is available but no curriculum has been generated yet.{Colors.ENDC}")
                print(f"Let's show what we know about {self.current_state.framework.value}:")
                self._display_framework_overview(self.current_state.framework_context)
            return
        
        print(f"\n{Colors.BOLD}🎓 Curriculum Progress:{Colors.ENDC}")
        print(f"• Current Module: {self.current_state.current_module_index + 1}")
        print(f"• Current Objective: {self.current_state.current_objective_index + 1}")
        
        # Show mastery levels
        if self.current_state.mastery_levels:
            print(f"\n{Colors.BOLD}📊 Topic Mastery:{Colors.ENDC}")
            for topic, level in self.current_state.mastery_levels.items():
                print(f"• {topic}: {int(level * 100)}%")
        
        # Show quiz results
        if self.current_state.quiz_results:
            print(f"\n{Colors.BOLD}📝 Recent Quiz Results:{Colors.ENDC}")
            for i, result in enumerate(self.current_state.quiz_results[-3:]):
                print(f"• {result['topic']}: {int(result['score'] * 100)}% ({result['correct']}/{result['questions']})")
    
    @async_debug_step
    async def run_conversation_loop(self):
        """Main conversation loop."""
        print(f"\n{Colors.BOLD}💬 Learning Session Started{Colors.ENDC}")
        print("Type your questions or use /help for available commands.")
        print(f"{Colors.WARNING}Press Ctrl+C to exit anytime{Colors.ENDC}")
        
        while True:
            try:
                # Get user input
                user_input = input(f"\n{Colors.BOLD}You: {Colors.ENDC}").strip()
                
                if not user_input:
                    continue
                
                # Handle commands
                if user_input.startswith('/'):
                    command = user_input[1:].lower()
                    
                    if command in ['help', 'h']:
                        self.show_help()
                    elif command in ['status', 's']:
                        self.show_status()
                    elif command in ['agents', 'a']:
                        self.list_agents()
                    elif command in ['curriculum', 'progress', 'p']:
                        self.show_curriculum_status()
                    elif command in ['clear', 'c']:
                        self.clear_conversation()
                    elif command in ['quit', 'exit', 'q']:
                        print(f"{Colors.OKGREEN}Goodbye! Thanks for learning with us!{Colors.ENDC}")
                        break
                    else:
                        print(f"{Colors.WARNING}Unknown command. Type /help for available commands.{Colors.ENDC}")
                else:
                    # Process as regular message
                    await self.process_user_message(user_input)
                
            except KeyboardInterrupt:
                print(f"\n{Colors.WARNING}Goodbye! Thanks for learning with us!{Colors.ENDC}")
                break
            except Exception as e:
                print(f"{Colors.FAIL}Unexpected error: {e}{Colors.ENDC}")
                if DEBUG_MODE:
                    traceback.print_exc()
    
    @async_debug_step
    async def start(self):
        """Start the CLI learning system."""
        self.print_banner()
        
        # Check LLM availability
        available_models = llm_manager.get_available_models()
        if not available_models:
            print(f"{Colors.FAIL}❌ No LLM models available. Please check your API keys in .env file.{Colors.ENDC}")
            return
        
        print(f"{Colors.OKGREEN}✅ LLM Available: {', '.join(available_models)}{Colors.ENDC}")
        
        # Setup user profile
        self.user_profile = self.setup_user_profile()
        
        # Display welcome with tools information
        self.display_welcome()
        
        # Select framework
        framework = self.select_framework()
        
        # Get optimal constellation
        constellation = self.get_optimal_constellation(framework)
        
        # Initialize learning session
        await self.initialize_learning_session(framework, constellation)
        
        # Start conversation loop
        await self.run_conversation_loop()

    @debug_step
    def display_welcome(self) -> None:
        """Display welcome message and system information."""
        console = Console()
        
        # Welcome banner
        console.print("\n" + "="*60, style="bold blue")
        console.print("🤖 GAAPF - Guidance AI Agent for Python Framework CLI", style="bold blue", justify="center")
        console.print("Real-time AI conversation with specialized agents", style="dim", justify="center")
        console.print("="*60 + "\n", style="bold blue")
        
        # System status
        console.print("📊 System Status:", style="bold green")
        console.print(f"   • LLM Provider: {llm_manager.get_provider_status()}")
        console.print(f"   • Active Model: {llm_manager.get_current_model_name()}")
        
        # Check tool availability
        from pyframeworks_assistant.tools.search_tools import check_tavily_availability
        from pyframeworks_assistant.tools.file_tools import file_manager
        
        console.print("\n🔧 Available Tools:", style="bold green")
        
        # Tavily Search status
        tavily_status = check_tavily_availability()
        if tavily_status["ready"]:
            console.print("   ✅ Tavily AI Search - Advanced internet access enabled")
        else:
            console.print("   ❌ Tavily AI Search - Configure TAVILY_API_KEY")
        
        # File creation status
        console.print("   ✅ Auto File Creation - Code files will be created automatically")
        console.print(f"   📁 Generated files location: {file_manager.base_dir}")
        
        # Tool execution information
        console.print("\n🛠️ Tool Execution:", style="bold green")
        console.print("   • Agents execute tools directly to assist you")
        console.print("   • Internet search tools find the latest information")
        console.print("   • Code execution tools run examples for you")
        console.print("   • File management tools save examples automatically")
        
        # User profile
        console.print(f"\n👤 User Profile:", style="bold green")
        console.print(f"   • Experience: {self.user_profile.programming_experience_years} years")
        console.print(f"   • Python Level: {self.user_profile.python_skill_level.value}")
        console.print(f"   • Learning Style: {self.user_profile.preferred_learning_style.value}")
        
        # Learning features
        console.print("\n🧠 Learning Features:", style="bold green")
        console.print("   • Personalized curriculum based on your profile")
        console.print("   • Adaptive quizzes to test your knowledge")
        console.print("   • Progress tracking across learning sessions")
        console.print("   • Multiple specialized agents for different learning needs")
        
        # Quick help
        console.print(f"\n💡 Quick Tips:", style="bold yellow")
        console.print("   • Ask about any Python framework (LangChain, LangGraph, FastAPI, etc.)")
        console.print("   • Request code examples - they'll be automatically saved as files")
        console.print("   • Ask for current information - agents can search the internet")
        console.print("   • Type 'help' for commands, 'exit' to quit")
        console.print("   • The AI will choose the best specialized agent for your question")
        
        console.print("\n" + "="*60 + "\n", style="bold blue")

        if DEBUG_MODE:
            console.print("[yellow]DEBUG MODE ENABLED - Detailed logging will be shown[/yellow]")


@async_debug_step
async def main():
    """Main entry point."""
    try:
        print(f"{Colors.WARNING}DEBUG: Starting CLI Learning System{Colors.ENDC}")
        cli_system = CLILearningSystem()
        await cli_system.start()
    except KeyboardInterrupt:
        print(f"\n{Colors.WARNING}Interrupted by user{Colors.ENDC}")
    except Exception as e:
        print(f"{Colors.FAIL}Fatal error: {e}{Colors.ENDC}")
        if DEBUG_MODE:
            traceback.print_exc()


if __name__ == "__main__":
    # Ensure we have an event loop
    try:
        print(f"{Colors.WARNING}DEBUG: Initializing event loop{Colors.ENDC}")
        asyncio.run(main())
    except KeyboardInterrupt:
        print(f"\n{Colors.WARNING}Goodbye!{Colors.ENDC}") 