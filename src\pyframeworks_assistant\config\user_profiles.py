"""
User profile models for personalization and progress tracking.
Follows the JSON schema defined in the original plan.
"""

from typing import Dict, List, Optional, Literal
from datetime import datetime
from pydantic import BaseModel, Field
from enum import Enum


class SkillLevel(str, Enum):
    """Skill level enumeration."""
    NONE = "none"
    BEGINNER = "beginner"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"
    EXPERT = "expert"


class LearningPace(str, Enum):
    """Learning pace options."""
    SLOW = "slow"
    MODERATE = "moderate"
    FAST = "fast"
    INTENSIVE = "intensive"


class LearningStyle(str, Enum):
    """Learning style preferences."""
    VISUAL = "visual"
    HANDS_ON = "hands-on"
    THEORETICAL = "theoretical"
    MIXED = "mixed"


class NotificationFrequency(str, Enum):
    """Notification frequency options."""
    NONE = "none"
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"


class DifficultyProgression(str, Enum):
    """Difficulty progression preferences."""
    GRADUAL = "gradual"
    MODERATE = "moderate"
    AGGRESSIVE = "aggressive"


class FeedbackStyle(str, Enum):
    """Feedback style preferences."""
    ENCOURAGING = "encouraging"
    DIRECT = "direct"
    DETAILED = "detailed"
    MINIMAL = "minimal"


class FrameworkExperience(BaseModel):
    """Experience with specific AI frameworks."""
    langchain: SkillLevel = Field(default=SkillLevel.NONE, description="LangChain experience")
    crewai: SkillLevel = Field(default=SkillLevel.NONE, description="CrewAI experience")
    langgraph: SkillLevel = Field(default=SkillLevel.NONE, description="LangGraph experience")
    autogen: SkillLevel = Field(default=SkillLevel.NONE, description="AutoGen experience")
    llamaindex: SkillLevel = Field(default=SkillLevel.NONE, description="LlamaIndex experience")


class UserPreferences(BaseModel):
    """User learning and interface preferences."""
    notification_frequency: NotificationFrequency = Field(
        default=NotificationFrequency.DAILY,
        description="How often to send notifications"
    )
    difficulty_progression: DifficultyProgression = Field(
        default=DifficultyProgression.GRADUAL,
        description="How quickly to increase difficulty"
    )
    feedback_style: FeedbackStyle = Field(
        default=FeedbackStyle.ENCOURAGING,
        description="Preferred feedback style"
    )
    code_complexity: Literal["simple", "medium", "complex"] = Field(
        default="medium",
        description="Preferred code complexity level"
    )
    interactive_mode: bool = Field(
        default=True,
        description="Enable interactive learning features"
    )
    save_progress: bool = Field(
        default=True,
        description="Save learning progress"
    )


class LearningSession(BaseModel):
    """Individual learning session record."""
    session_id: str = Field(description="Unique session identifier")
    start_time: datetime = Field(description="Session start time")
    end_time: Optional[datetime] = Field(default=None, description="Session end time")
    framework: str = Field(description="Framework being learned")
    topics_covered: List[str] = Field(default_factory=list, description="Topics covered")
    completion_percentage: float = Field(default=0.0, description="Session completion percentage")
    effectiveness_score: Optional[float] = Field(default=None, description="Learning effectiveness score")
    user_satisfaction: Optional[float] = Field(default=None, description="User satisfaction rating")
    constellation_used: str = Field(description="Constellation configuration used")
    notes: Optional[str] = Field(default=None, description="Session notes")


class ConstellationEffectiveness(BaseModel):
    """Effectiveness metrics for constellation configurations."""
    constellation_type: str = Field(description="Type of constellation")
    total_sessions: int = Field(default=0, description="Total sessions using this constellation")
    average_effectiveness: float = Field(default=0.0, description="Average effectiveness score")
    average_completion_time: float = Field(default=0.0, description="Average completion time")
    user_preference_score: float = Field(default=0.0, description="User preference score")
    last_updated: datetime = Field(default_factory=datetime.now, description="Last update time")


class TemporalPattern(BaseModel):
    """Temporal learning patterns for optimization."""
    pattern_id: str = Field(description="Unique pattern identifier")
    user_characteristics: Dict[str, str] = Field(description="User characteristics pattern")
    optimal_constellation: str = Field(description="Optimal constellation for this pattern")
    effectiveness_trend: List[float] = Field(description="Effectiveness over time")
    optimal_timing: Dict[str, int] = Field(description="Optimal timing patterns")
    confidence_score: float = Field(description="Pattern confidence score")
    sample_size: int = Field(description="Number of samples used")


class UserProgress(BaseModel):
    """User learning progress tracking."""
    completed_modules: List[str] = Field(
        default_factory=list,
        description="List of completed learning modules"
    )
    current_framework: Optional[str] = Field(
        default=None,
        description="Currently learning framework"
    )
    learning_session_history: List[LearningSession] = Field(
        default_factory=list,
        description="History of learning sessions"
    )
    constellation_effectiveness: Dict[str, ConstellationEffectiveness] = Field(
        default_factory=dict,
        description="Effectiveness metrics by constellation"
    )
    temporal_patterns: List[TemporalPattern] = Field(
        default_factory=list,
        description="Identified temporal learning patterns"
    )
    total_learning_time: float = Field(
        default=0.0,
        description="Total learning time in hours"
    )
    current_streak: int = Field(
        default=0,
        description="Current daily learning streak"
    )
    achievements: List[str] = Field(
        default_factory=list,
        description="Unlocked achievements"
    )


class UserProfile(BaseModel):
    """Complete user profile for personalization."""
    user_id: str = Field(description="Unique user identifier")
    
    # Basic Information
    programming_experience_years: int = Field(
        ge=0,
        le=50,
        description="Years of programming experience"
    )
    python_skill_level: SkillLevel = Field(
        default=SkillLevel.BEGINNER,
        description="Python programming skill level"
    )
    ai_framework_experience: FrameworkExperience = Field(
        default_factory=FrameworkExperience,
        description="Experience with AI frameworks"
    )
    
    # Learning Goals and Context
    learning_goals: List[str] = Field(
        default_factory=list,
        description="User's learning objectives"
    )
    learning_pace: LearningPace = Field(
        default=LearningPace.MODERATE,
        description="Preferred learning pace"
    )
    preferred_learning_style: LearningStyle = Field(
        default=LearningStyle.HANDS_ON,
        description="Preferred learning style"
    )
    project_context: Optional[str] = Field(
        default=None,
        description="Real-world project context"
    )
    time_availability: str = Field(
        default="2-3 hours per week",
        description="Available time for learning"
    )
    
    # Technical Preferences
    timezone: str = Field(
        default="UTC",
        description="User's timezone"
    )
    language_preference: str = Field(
        default="english",
        description="Preferred language"
    )
    
    # Progress and Metrics
    progress: UserProgress = Field(
        default_factory=UserProgress,
        description="Learning progress and history"
    )
    preferences: UserPreferences = Field(
        default_factory=UserPreferences,
        description="User preferences and settings"
    )
    
    # Metadata
    created_at: datetime = Field(
        default_factory=datetime.now,
        description="Profile creation timestamp"
    )
    last_active: datetime = Field(
        default_factory=datetime.now,
        description="Last activity timestamp"
    )
    
    def update_last_active(self) -> None:
        """Update the last active timestamp."""
        self.last_active = datetime.now()
    
    def add_learning_session(self, session: LearningSession) -> None:
        """Add a new learning session to the history."""
        self.progress.learning_session_history.append(session)
        self.update_last_active()
    
    def get_current_skill_level(self, framework: str) -> SkillLevel:
        """Get current skill level for a specific framework."""
        return getattr(self.ai_framework_experience, framework, SkillLevel.NONE)
    
    def update_framework_experience(self, framework: str, level: SkillLevel) -> None:
        """Update experience level for a specific framework."""
        setattr(self.ai_framework_experience, framework, level)
        self.update_last_active()
    
    def get_learning_effectiveness(self, constellation_type: str) -> float:
        """Get learning effectiveness for a specific constellation type."""
        effectiveness = self.progress.constellation_effectiveness.get(constellation_type)
        return effectiveness.average_effectiveness if effectiveness else 0.0
    
    def to_json_config(self) -> Dict:
        """Export to JSON configuration format as per original plan."""
        return {
            "user_id": self.user_id,
            "profile": {
                "programming_experience_years": self.programming_experience_years,
                "python_skill_level": self.python_skill_level.value,
                "ai_framework_experience": {
                    "langchain": self.ai_framework_experience.langchain.value,
                    "crewai": self.ai_framework_experience.crewai.value,
                    "langgraph": self.ai_framework_experience.langgraph.value,
                    "autogen": self.ai_framework_experience.autogen.value,
                    "llamaindex": self.ai_framework_experience.llamaindex.value,
                },
                "learning_goals": self.learning_goals,
                "learning_pace": self.learning_pace.value,
                "preferred_learning_style": self.preferred_learning_style.value,
                "project_context": self.project_context,
                "time_availability": self.time_availability,
                "timezone": self.timezone,
                "language_preference": self.language_preference,
            },
            "progress": {
                "completed_modules": self.progress.completed_modules,
                "current_framework": self.progress.current_framework,
                "learning_session_history": [
                    session.dict() for session in self.progress.learning_session_history
                ],
                "constellation_effectiveness": {
                    k: v.dict() for k, v in self.progress.constellation_effectiveness.items()
                },
                "temporal_patterns": [
                    pattern.dict() for pattern in self.progress.temporal_patterns
                ]
            },
            "preferences": self.preferences.dict()
        } 