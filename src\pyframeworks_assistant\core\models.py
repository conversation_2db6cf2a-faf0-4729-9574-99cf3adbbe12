"""
LLM model management using LangChain's modern init_chat_model approach.
Based on the latest LangChain documentation and best practices.
"""

from typing import Dict, Any, Optional, Type, Union
import logging
from langchain.chat_models import init_chat_model
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_openai import ChatOpenAI
from langchain_anthropic import ChatAnthropic
from langchain_google_genai import ChatGoogleGenerativeAI

from ..config.settings import settings

logger = logging.getLogger(__name__)


class LLMManager:
    """Manages LLM instances for the constellation system."""
    
    def __init__(self):
        """Initialize the LLM manager."""
        self._models: Dict[str, BaseChatModel] = {}
        self._default_model: Optional[BaseChatModel] = None
        self._initialize_models()
    
    def _initialize_models(self) -> None:
        """Initialize available LLM models based on configuration."""
        llm_config = settings.get_llm_config()
        
        # Initialize Google GenAI models if API key is available
        if "google" in llm_config:
            try:
                google_model = ChatGoogleGenerativeAI(
                    model="gemini-2.0-flash",
                    temperature=0,
                    google_api_key=settings.google_api_key
                )
                self._models["gemini-2.5-flash-preview-05-20"] = google_model
                self._models["gemini-2.5-flash-preview-05-20"] = google_model
                
                # Also initialize the lite version
                google_lite_model = ChatGoogleGenerativeAI(
                    model="gemini-2.0-flash-lite",
                    temperature=0,
                    google_api_key=settings.google_api_key
                )
                self._models["gemini-2.0-flash-lite"] = google_lite_model
                
                # Set Google as default if no default is set
                if not self._default_model:
                    self._default_model = google_model
                    
                logger.info("Initialized Google GenAI models successfully")
                
            except Exception as e:
                logger.warning(f"Failed to initialize Google GenAI models: {e}")
        
        # Initialize OpenAI models if API key is available
        if "openai" in llm_config:
            try:
                openai_model = init_chat_model(
                    model="gpt-4o-mini",
                    model_provider="openai",
                    temperature=0
                )
                self._models["openai_default"] = openai_model
                self._models["gpt-4o-mini"] = openai_model
                
                # Set as default if no default is set
                if not self._default_model:
                    self._default_model = openai_model
                    
                logger.info("Initialized OpenAI models successfully")
                
            except Exception as e:
                logger.warning(f"Failed to initialize OpenAI models: {e}")
        
        # Initialize Anthropic models if API key is available
        if "anthropic" in llm_config:
            try:
                anthropic_model = init_chat_model(
                    model="claude-3-5-sonnet-20241022",
                    model_provider="anthropic",
                    temperature=0
                )
                self._models["anthropic_default"] = anthropic_model
                self._models["claude-3-5-sonnet"] = anthropic_model
                
                # Set as default if no default is set
                if not self._default_model:
                    self._default_model = anthropic_model
                    
                logger.info("Initialized Anthropic models successfully")
                
            except Exception as e:
                logger.warning(f"Failed to initialize Anthropic models: {e}")
        
        if not self._models:
            logger.error("No LLM models initialized. Please check your API keys.")
            
    def get_model(self, model_name: Optional[str] = None) -> BaseChatModel:
        """
        Get a specific model instance.
        
        Args:
            model_name: Name of the model to get. If None, returns default model.
            
        Returns:
            BaseChatModel instance
            
        Raises:
            ValueError: If model not found and no default available
        """
        if model_name is None:
            if self._default_model is None:
                raise ValueError("No default model available and no model specified")
            return self._default_model
            
        if model_name not in self._models:
            # Try to create the model dynamically
            try:
                model = self._create_model_dynamically(model_name)
                self._models[model_name] = model
                return model
            except Exception as e:
                logger.error(f"Failed to create model {model_name}: {e}")
                if self._default_model is None:
                    raise ValueError(f"Model '{model_name}' not found and no default available")
                logger.warning(f"Falling back to default model")
                return self._default_model
                
        return self._models[model_name]
    
    def _create_model_dynamically(self, model_name: str) -> BaseChatModel:
        """
        Dynamically create a model based on name.
        
        Args:
            model_name: Name of the model to create
            
        Returns:
            BaseChatModel instance
        """
        # Google GenAI models
        if any(name in model_name.lower() for name in ["gemini", "google"]):
            if not settings.google_api_key:
                raise ValueError("Google API key not configured")
            return ChatGoogleGenerativeAI(
                model=model_name,
                temperature=0,
                google_api_key=settings.google_api_key
            )
        
        # OpenAI models
        if any(name in model_name.lower() for name in ["gpt", "openai"]):
            if not settings.openai_api_key:
                raise ValueError("OpenAI API key not configured")
            return init_chat_model(
                model=model_name,
                model_provider="openai",
                temperature=0
            )
        
        # Anthropic models
        if any(name in model_name.lower() for name in ["claude", "anthropic"]):
            if not settings.anthropic_api_key:
                raise ValueError("Anthropic API key not configured")
            return init_chat_model(
                model=model_name,
                model_provider="anthropic", 
                temperature=0
            )
        
        raise ValueError(f"Unknown model provider for model: {model_name}")
    
    def get_available_models(self) -> list[str]:
        """Get list of available model names."""
        return list(self._models.keys())
    
    def get_provider_status(self) -> str:
        """Get the status of available LLM providers."""
        providers = []
        
        # Check which providers are available
        if any("google" in model or "gemini" in model for model in self._models.keys()):
            providers.append("Google GenAI")
        if any("openai" in model or "gpt" in model for model in self._models.keys()):
            providers.append("OpenAI")
        if any("anthropic" in model or "claude" in model for model in self._models.keys()):
            providers.append("Anthropic")
        
        if providers:
            return ", ".join(providers)
        else:
            return "No providers configured"
    
    def get_current_model_name(self) -> str:
        """Get the name of the current default model."""
        if self._default_model is None:
            return "No default model"
        
        # Find the model name by looking for the default model in our models dict
        for name, model in self._models.items():
            if model is self._default_model:
                return name
        
        # Fallback - try to get model name from the model itself
        if hasattr(self._default_model, 'model'):
            return self._default_model.model
        elif hasattr(self._default_model, 'model_name'):
            return self._default_model.model_name
        else:
            return "Unknown model"
    
    def get_default_model(self) -> Optional[BaseChatModel]:
        """Get the default model."""
        return self._default_model
    
    def set_default_model(self, model_name: str) -> None:
        """
        Set the default model.
        
        Args:
            model_name: Name of the model to set as default
        """
        model = self.get_model(model_name)
        self._default_model = model
    
    def create_specialized_model(
        self, 
        base_model: str, 
        temperature: float = 0.0,
        max_tokens: Optional[int] = None,
        **kwargs
    ) -> BaseChatModel:
        """
        Create a specialized model configuration.
        
        Args:
            base_model: Base model name
            temperature: Model temperature
            max_tokens: Maximum tokens
            **kwargs: Additional model parameters
            
        Returns:
            Configured BaseChatModel instance
        """
        base = self.get_model(base_model)
        
        # Create a copy with new parameters
        if isinstance(base, ChatGoogleGenerativeAI):
            return ChatGoogleGenerativeAI(
                model=base.model,
                temperature=temperature,
                max_tokens=max_tokens,
                google_api_key=settings.google_api_key,
                **kwargs
            )
        elif isinstance(base, ChatOpenAI):
            return ChatOpenAI(
                model=base.model_name,
                temperature=temperature,
                max_tokens=max_tokens,
                **kwargs
            )
        elif isinstance(base, ChatAnthropic):
            return ChatAnthropic(
                model=base.model,
                temperature=temperature,
                max_tokens=max_tokens,
                **kwargs
            )
        else:
            # Generic approach - try to bind parameters
            return base.bind(temperature=temperature, max_tokens=max_tokens, **kwargs)


# Global LLM manager instance
llm_manager = LLMManager()


def initialize_chat_model(
    model: str = "gemini-2.5-flash-preview-05-20",
    model_provider: Optional[str] = None,
    **kwargs
) -> BaseChatModel:
    """
    Initialize a chat model using LangChain's modern approach.
    
    Args:
        model: Model name
        model_provider: Model provider (auto-detected if None)
        **kwargs: Additional model parameters
        
    Returns:
        BaseChatModel instance
    """
    return init_chat_model(
        model=model,
        model_provider=model_provider,
        **kwargs
    )


def get_default_model() -> BaseChatModel:
    """Get the default LLM model."""
    return llm_manager.get_default_model()


def get_model(model_name: str) -> BaseChatModel:
    """Get a specific model by name."""
    return llm_manager.get_model(model_name) 