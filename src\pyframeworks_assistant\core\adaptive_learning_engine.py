"""
Adaptive Learning Engine - Dynamic learning path generation and adaptation.
This component implements personalized learning paths that adapt based on user performance.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from enum import Enum
from dataclasses import dataclass, field
from pydantic import BaseModel, Field
import json
import numpy as np
from collections import defaultdict

from .constellation import ConstellationState
from .intelligent_context import IntelligentContext
from ..config.user_profiles import UserProfile, SkillLevel
from ..config.framework_configs import SupportedFrameworks

logger = logging.getLogger(__name__)


class DifficultyLevel(str, Enum):
    """Learning difficulty levels."""
    BEGINNER = "beginner"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"
    EXPERT = "expert"
    ADAPTIVE = "adaptive"


class LearningStyle(str, Enum):
    """Learning style preferences."""
    VISUAL = "visual"
    KINESTHETIC = "kinesthetic"
    AUDITORY = "auditory"
    READING_WRITING = "reading_writing"
    MULTIMODAL = "multimodal"


class ModuleType(str, Enum):
    """Types of learning modules."""
    CONCEPT = "concept"
    PRACTICE = "practice"
    PROJECT = "project"
    ASSESSMENT = "assessment"
    REVIEW = "review"
    EXPLORATION = "exploration"


@dataclass
class LearningObjective:
    """Represents a specific learning objective."""
    objective_id: str
    description: str
    framework: SupportedFrameworks
    difficulty_level: str
    estimated_duration: int  # in minutes
    prerequisites: List[str] = field(default_factory=list)
    skills_developed: List[str] = field(default_factory=list)
    assessment_criteria: List[str] = field(default_factory=list)
    completion_status: bool = False
    mastery_level: float = 0.0  # 0-1 score


@dataclass
class LearningModule:
    """Represents a learning module within a path."""
    module_id: str
    title: str
    description: str
    module_type: ModuleType
    objectives: List[LearningObjective]
    estimated_duration: int
    difficulty_level: DifficultyLevel
    prerequisites: List[str] = field(default_factory=list)
    resources: List[Dict[str, Any]] = field(default_factory=list)
    activities: List[Dict[str, Any]] = field(default_factory=list)
    completion_percentage: float = 0.0
    effectiveness_score: float = 0.0


@dataclass
class LearningPath:
    """Represents a complete adaptive learning path."""
    path_id: str
    user_id: str
    framework: SupportedFrameworks
    title: str
    description: str
    modules: List[LearningModule]
    current_module_id: str
    total_estimated_duration: int
    completion_percentage: float = 0.0
    effectiveness_score: float = 0.0
    adaptation_count: int = 0
    created_at: datetime = field(default_factory=datetime.now)
    last_adapted: Optional[datetime] = None
    
    def get_current_module(self) -> Optional[LearningModule]:
        """Get the current active module."""
        return next((m for m in self.modules if m.module_id == self.current_module_id), None)
    
    def get_next_objectives(self) -> List[LearningObjective]:
        """Get the next objectives to work on."""
        current_module = self.get_current_module()
        if not current_module:
            return []
        
        # Return incomplete objectives from current module
        incomplete = [obj for obj in current_module.objectives if not obj.completion_status]
        if incomplete:
            return incomplete
        
        # If current module is complete, get objectives from next module
        current_index = next((i for i, m in enumerate(self.modules) if m.module_id == self.current_module_id), -1)
        if current_index < len(self.modules) - 1:
            next_module = self.modules[current_index + 1]
            return next_module.objectives[:3]  # Return first 3 objectives
        
        return []


class AdaptationTrigger(str, Enum):
    """Triggers for learning path adaptation."""
    LOW_ENGAGEMENT = "low_engagement"
    POOR_PERFORMANCE = "poor_performance"
    RAPID_PROGRESS = "rapid_progress"
    STYLE_MISMATCH = "style_mismatch"
    OBJECTIVE_CHANGE = "objective_change"
    TIME_CONSTRAINT = "time_constraint"
    USER_REQUEST = "user_request"


@dataclass
class AdaptationRule:
    """Rule for adapting learning paths."""
    trigger: AdaptationTrigger
    condition: str  # Description of when to trigger
    action: str  # What adaptation to perform
    priority: float  # 0-1, higher means more important
    parameters: Dict[str, Any] = field(default_factory=dict)


@dataclass
class LearningAnalytics:
    """Real-time learning analytics for ELT system."""
    user_id: str
    session_id: str
    framework: SupportedFrameworks
    
    # Engagement Metrics
    engagement_score: float = 0.0
    attention_span: float = 0.0  # minutes
    interaction_frequency: float = 0.0  # interactions per minute
    session_duration: float = 0.0  # minutes
    
    # Learning Velocity Metrics
    concepts_learned_per_hour: float = 0.0
    exercises_completed_per_hour: float = 0.0
    knowledge_retention_rate: float = 0.0
    
    # Comprehension Metrics
    comprehension_confidence: float = 0.0
    error_rate: float = 0.0
    help_request_frequency: float = 0.0
    
    # ELT-Specific Metrics
    agent_handoff_efficiency: float = 0.0
    constellation_adaptation_rate: float = 0.0
    learning_path_optimization_score: float = 0.0
    
    # Real-time Indicators
    current_difficulty_comfort: float = 0.5  # 0-1 scale
    motivation_level: float = 0.5  # 0-1 scale
    cognitive_load: float = 0.5  # 0-1 scale
    
    # Trend Data
    engagement_trend: List[float] = field(default_factory=list)
    comprehension_trend: List[float] = field(default_factory=list)
    velocity_trend: List[float] = field(default_factory=list)
    
    # Timestamps
    last_updated: datetime = field(default_factory=datetime.now)
    analytics_history: List[Dict[str, Any]] = field(default_factory=list)


@dataclass
class PerformanceInsight:
    """Performance insight generated from analytics."""
    insight_type: str
    priority: float  # 0-1, higher = more important
    title: str
    description: str
    recommendation: str
    data_points: List[float] = field(default_factory=list)
    confidence_score: float = 0.0
    actionable: bool = True


class AdaptiveLearningEngine:
    """
    Engine for generating and adapting personalized learning paths.
    """
    
    def __init__(self):
        """Initialize the adaptive learning engine."""
        self.active_paths: Dict[str, LearningPath] = {}
        self.adaptation_rules: List[AdaptationRule] = []
        self.framework_curricula: Dict[SupportedFrameworks, Dict[str, Any]] = {}
        self.learning_analytics: Dict[str, Any] = {}
        
        # NEW: Real-time Analytics System
        self.real_time_analytics: Dict[str, LearningAnalytics] = {}
        self.performance_insights: Dict[str, List[PerformanceInsight]] = {}
        self.analytics_thresholds = {
            "low_engagement": 0.3,
            "high_cognitive_load": 0.8,
            "low_comprehension": 0.4,
            "rapid_progress": 0.9
        }
        
        # Initialize adaptation rules
        self._initialize_adaptation_rules()
        
        # Initialize framework curricula
        self._initialize_framework_curricula()
        
        logger.info("Adaptive Learning Engine with Real-time Analytics initialized")
    
    async def initialize(self) -> None:
        """Initialize the learning engine."""
        try:
            # Load any persisted learning paths
            await self._load_persisted_paths()
            
            # Initialize learning analytics
            self.learning_analytics = {
                "total_paths_generated": 0,
                "total_adaptations": 0,
                "average_completion_rate": 0.0,
                "average_effectiveness": 0.0
            }
            
            logger.info("Adaptive Learning Engine fully initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize Adaptive Learning Engine: {e}")
            raise
    
    async def generate_learning_path(
        self,
        user_profile: UserProfile,
        objectives: List[LearningObjective],
        framework: SupportedFrameworks,
        time_constraint: Optional[int] = None
    ) -> LearningPath:
        """
        Generate a personalized learning path for a user.
        
        Args:
            user_profile: User's learning profile
            objectives: Learning objectives to achieve
            framework: Target framework
            time_constraint: Optional time constraint in minutes
            
        Returns:
            Generated learning path
        """
        try:
            path_id = f"path_{user_profile.user_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # Analyze user profile for path customization
            learning_style = self._determine_learning_style(user_profile)
            difficulty_progression = self._determine_difficulty_progression(user_profile)
            
            # Generate modules based on objectives
            modules = await self._generate_modules(
                objectives,
                framework,
                learning_style,
                difficulty_progression,
                time_constraint
            )
            
            # Calculate total duration
            total_duration = sum(module.estimated_duration for module in modules)
            
            # Create learning path
            learning_path = LearningPath(
                path_id=path_id,
                user_id=user_profile.user_id,
                framework=framework,
                title=f"Personalized {framework.value} Learning Path",
                description=f"Adaptive learning path for {framework.value} tailored to your learning style and goals",
                modules=modules,
                current_module_id=modules[0].module_id if modules else "intro",
                total_estimated_duration=total_duration
            )
            
            # Store the path
            self.active_paths[path_id] = learning_path
            self.learning_analytics["total_paths_generated"] += 1
            
            logger.info(f"Generated learning path {path_id} for user {user_profile.user_id}")
            return learning_path
            
        except Exception as e:
            logger.error(f"Failed to generate learning path: {e}")
            raise
    
    async def should_adapt_path(
        self,
        learning_path: LearningPath,
        constellation_state: ConstellationState,
        context: Any  # TODO: Define IntelligentContext
    ) -> bool:
        """
        Determine if a learning path should be adapted.
        
        Args:
            learning_path: Current learning path
            constellation_state: Current constellation state
            context: Intelligent context
            
        Returns:
            True if adaptation is needed
        """
        try:
            # Check each adaptation rule
            for rule in self.adaptation_rules:
                if await self._evaluate_adaptation_rule(rule, learning_path, constellation_state, context):
                    logger.info(f"Adaptation triggered by rule: {rule.trigger.value}")
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking adaptation need: {e}")
            return False
    
    async def adapt_learning_path(
        self,
        learning_path: LearningPath,
        constellation_state: ConstellationState,
        user_profile: UserProfile
    ) -> LearningPath:
        """
        Adapt an existing learning path based on current performance.
        
        Args:
            learning_path: Current learning path
            constellation_state: Current constellation state
            user_profile: User profile
            
        Returns:
            Adapted learning path
        """
        try:
            # Determine what adaptations are needed
            adaptations = await self._determine_adaptations(
                learning_path,
                constellation_state,
                user_profile
            )
            
            # Apply adaptations
            adapted_path = await self._apply_adaptations(learning_path, adaptations)
            
            # Update adaptation tracking
            adapted_path.adaptation_count += 1
            adapted_path.last_adapted = datetime.now()
            
            # Update analytics
            self.learning_analytics["total_adaptations"] += 1
            
            # Store updated path
            self.active_paths[adapted_path.path_id] = adapted_path
            
            logger.info(f"Adapted learning path {adapted_path.path_id}")
            return adapted_path
            
        except Exception as e:
            logger.error(f"Failed to adapt learning path: {e}")
            return learning_path  # Return original path if adaptation fails
    
    async def get_optimization_recommendations(
        self,
        learning_path: LearningPath,
        constellation_state: ConstellationState,
        performance_analysis: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Get optimization recommendations for a learning path."""
        recommendations = []
        
        # Analyze performance metrics
        effectiveness = performance_analysis.get("effectiveness_score", 0.0)
        engagement = performance_analysis.get("engagement_level", 0.0)
        progress_rate = performance_analysis.get("progress_rate", 0.0)
        
        # Recommend modality changes
        if effectiveness < 0.6:
            if constellation_state.engagement_level < 0.5:
                recommendations.append({
                    "type": "modality_switch",
                    "target_modality": "kinesthetic",
                    "reason": "Low engagement suggests need for more hands-on learning",
                    "expected_improvement": 0.3
                })
        
        # Recommend difficulty adjustments
        if progress_rate > 0.8:  # Too fast
            recommendations.append({
                "type": "difficulty_increase",
                "adjustment": "increase_complexity",
                "reason": "Rapid progress suggests content may be too easy",
                "expected_improvement": 0.2
            })
        elif progress_rate < 0.3:  # Too slow
            recommendations.append({
                "type": "difficulty_decrease",
                "adjustment": "simplify_content",
                "reason": "Slow progress suggests content may be too difficult",
                "expected_improvement": 0.25
            })
        
        # Recommend constellation changes
        if len(constellation_state.topics_covered) > 5 and effectiveness < 0.7:
            recommendations.append({
                "type": "constellation_adaptation",
                "target_constellation": "hands_on_focused",
                "reason": "Multiple topics covered with low effectiveness suggests need for practice focus",
                "expected_improvement": 0.4
            })
        
        return recommendations
    
    async def adjust_learning_path(
        self,
        learning_path: LearningPath,
        adjustments: Dict[str, Any]
    ) -> None:
        """Apply specific adjustments to a learning path."""
        adjustment_type = adjustments.get("type")
        
        if adjustment_type == "reorder_modules":
            # Reorder modules based on new priorities
            new_order = adjustments.get("new_order", [])
            if new_order:
                reordered_modules = []
                for module_id in new_order:
                    module = next((m for m in learning_path.modules if m.module_id == module_id), None)
                    if module:
                        reordered_modules.append(module)
                learning_path.modules = reordered_modules
        
        elif adjustment_type == "add_practice_modules":
            # Add additional practice modules
            practice_topics = adjustments.get("topics", [])
            for topic in practice_topics:
                practice_module = await self._create_practice_module(topic, learning_path.framework)
                learning_path.modules.append(practice_module)
        
        elif adjustment_type == "adjust_difficulty":
            # Adjust difficulty of remaining modules
            difficulty_change = adjustments.get("change", 0)  # -1, 0, or 1
            current_module = learning_path.get_current_module()
            if current_module:
                current_index = learning_path.modules.index(current_module)
                for module in learning_path.modules[current_index:]:
                    module.difficulty_level = self._adjust_difficulty_level(
                        module.difficulty_level,
                        difficulty_change
                    )
    
    def _initialize_adaptation_rules(self) -> None:
        """Initialize adaptation rules for the engine."""
        self.adaptation_rules = [
            AdaptationRule(
                trigger=AdaptationTrigger.LOW_ENGAGEMENT,
                condition="engagement_level < 0.4 for 3+ interactions",
                action="switch_to_hands_on_modules",
                priority=0.8
            ),
            AdaptationRule(
                trigger=AdaptationTrigger.POOR_PERFORMANCE,
                condition="effectiveness_score < 0.5 for current module",
                action="add_review_modules_and_reduce_difficulty",
                priority=0.9
            ),
            AdaptationRule(
                trigger=AdaptationTrigger.RAPID_PROGRESS,
                condition="progress_rate > 0.8 and high_comprehension",
                action="skip_basic_modules_and_increase_difficulty",
                priority=0.7
            ),
            AdaptationRule(
                trigger=AdaptationTrigger.STYLE_MISMATCH,
                condition="learning_style_effectiveness < 0.6",
                action="switch_learning_modality",
                priority=0.6
            )
        ]
    
    def _initialize_framework_curricula(self) -> None:
        """Initialize curriculum templates for different frameworks."""
        self.framework_curricula = {
            SupportedFrameworks.LANGCHAIN: {
                "core_modules": [
                    "langchain_basics", "prompts_and_chains", "memory_systems",
                    "agents_and_tools", "rag_implementation", "vector_stores",
                    "deployment", "advanced_features"
                ],
                "practice_projects": [
                    "chatbot_application", "rag_system", "research_agent"
                ],
                "assessment_points": [
                    "basic_chain_creation", "memory_integration", "agent_development"
                ]
            },
            SupportedFrameworks.LANGGRAPH: {
                "core_modules": [
                    "langgraph_basics", "state_graphs", "conditional_edges",
                    "human_in_loop", "persistence", "streaming",
                    "testing", "deployment"
                ],
                "practice_projects": [
                    "workflow_automation", "multi_agent_system", "decision_tree_app"
                ],
                "assessment_points": [
                    "graph_construction", "state_management", "workflow_design"
                ]
            },
            SupportedFrameworks.CREWAI: {
                "core_modules": [
                    "crewai_basics", "agent_creation", "task_definition",
                    "crew_orchestration", "tool_integration", "memory_systems",
                    "testing", "deployment"
                ],
                "practice_projects": [
                    "research_crew", "content_creation_team", "analysis_pipeline"
                ],
                "assessment_points": [
                    "agent_design", "task_coordination", "crew_management"
                ]
            },
            SupportedFrameworks.AUTOGEN: {
                "core_modules": [
                    "autogen_basics", "conversable_agents", "group_chat",
                    "code_execution", "human_proxy", "tool_calling",
                    "testing", "deployment"
                ],
                "practice_projects": [
                    "coding_assistant", "multi_agent_chat", "automated_workflow"
                ],
                "assessment_points": [
                    "agent_conversation", "code_generation", "workflow_automation"
                ]
            },
            SupportedFrameworks.LLAMAINDEX: {
                "core_modules": [
                    "llamaindex_basics", "document_loading", "indexing_strategies",
                    "query_engines", "chat_engines", "agents",
                    "evaluation", "deployment"
                ],
                "practice_projects": [
                    "document_qa_system", "knowledge_base", "intelligent_search"
                ],
                "assessment_points": [
                    "document_processing", "query_optimization", "system_integration"
                ]
            }
        }
    
    def _determine_learning_style(self, user_profile: UserProfile) -> LearningStyle:
        """Determine the user's learning style from their profile."""
        style_mapping = {
            "visual": LearningStyle.VISUAL,
            "hands_on": LearningStyle.KINESTHETIC,
            "theoretical": LearningStyle.READING_WRITING,
            "mixed": LearningStyle.MULTIMODAL
        }
        
        return style_mapping.get(
            user_profile.preferred_learning_style.value,
            LearningStyle.MULTIMODAL
        )
    
    def _determine_difficulty_progression(self, user_profile: UserProfile) -> List[DifficultyLevel]:
        """Determine difficulty progression based on user skill level."""
        skill_level = user_profile.python_skill_level
        
        if skill_level == SkillLevel.BEGINNER:
            return [DifficultyLevel.BEGINNER, DifficultyLevel.BEGINNER, DifficultyLevel.INTERMEDIATE]
        elif skill_level == SkillLevel.INTERMEDIATE:
            return [DifficultyLevel.INTERMEDIATE, DifficultyLevel.INTERMEDIATE, DifficultyLevel.ADVANCED]
        else:  # ADVANCED
            return [DifficultyLevel.ADVANCED, DifficultyLevel.ADVANCED, DifficultyLevel.EXPERT]
    
    async def _generate_modules(
        self,
        objectives: List[LearningObjective],
        framework: SupportedFrameworks,
        learning_style: LearningStyle,
        difficulty_progression: List[DifficultyLevel],
        time_constraint: Optional[int]
    ) -> List[LearningModule]:
        """Generate learning modules based on objectives and constraints."""
        modules = []
        curriculum = self.framework_curricula.get(framework, {})
        core_modules = curriculum.get("core_modules", [])
        
        # Create modules based on objectives and curriculum
        for i, objective in enumerate(objectives):
            # Determine module type based on learning style
            module_type = self._determine_module_type(learning_style, i)
            
            # Determine difficulty
            difficulty_index = min(i, len(difficulty_progression) - 1)
            difficulty = difficulty_progression[difficulty_index]
            
            # Create module
            module = LearningModule(
                module_id=f"module_{i}_{objective.objective_id}",
                title=f"Module {i+1}: {objective.description}",
                description=f"Learn {objective.description} in {framework.value}",
                module_type=module_type,
                objectives=[objective],
                estimated_duration=objective.estimated_duration,
                difficulty_level=difficulty
            )
            
            # Add activities based on learning style
            module.activities = self._generate_activities(learning_style, module_type, framework)
            
            modules.append(module)
        
        # Adjust for time constraints if provided
        if time_constraint:
            modules = self._adjust_for_time_constraint(modules, time_constraint)
        
        return modules
    
    def _determine_module_type(self, learning_style: LearningStyle, module_index: int) -> ModuleType:
        """Determine module type based on learning style and position."""
        if learning_style == LearningStyle.KINESTHETIC:
            # More practice and project modules
            if module_index % 2 == 0:
                return ModuleType.PRACTICE
            else:
                return ModuleType.CONCEPT
        elif learning_style == LearningStyle.VISUAL:
            # More concept and exploration modules
            return ModuleType.CONCEPT if module_index % 3 != 2 else ModuleType.EXPLORATION
        else:
            # Balanced approach
            cycle = module_index % 4
            if cycle == 0:
                return ModuleType.CONCEPT
            elif cycle == 1:
                return ModuleType.PRACTICE
            elif cycle == 2:
                return ModuleType.PROJECT
            else:
                return ModuleType.ASSESSMENT
    
    def _generate_activities(
        self,
        learning_style: LearningStyle,
        module_type: ModuleType,
        framework: SupportedFrameworks
    ) -> List[Dict[str, Any]]:
        """Generate activities for a module based on learning style."""
        activities = []
        
        if learning_style == LearningStyle.KINESTHETIC:
            activities.extend([
                {"type": "hands_on_coding", "description": "Interactive coding exercise"},
                {"type": "project_building", "description": "Build a mini-project"},
                {"type": "debugging_challenge", "description": "Fix broken code"}
            ])
        elif learning_style == LearningStyle.VISUAL:
            activities.extend([
                {"type": "diagram_study", "description": "Study architecture diagrams"},
                {"type": "code_visualization", "description": "Visualize code execution"},
                {"type": "concept_mapping", "description": "Create concept maps"}
            ])
        else:  # MULTIMODAL or others
            activities.extend([
                {"type": "reading", "description": "Read documentation and guides"},
                {"type": "video_tutorial", "description": "Watch tutorial videos"},
                {"type": "practice_exercise", "description": "Complete practice exercises"},
                {"type": "discussion", "description": "Discuss concepts with AI tutor"}
            ])
        
        return activities
    
    def _adjust_for_time_constraint(
        self,
        modules: List[LearningModule],
        time_constraint: int
    ) -> List[LearningModule]:
        """Adjust modules to fit within time constraint."""
        total_duration = sum(module.estimated_duration for module in modules)
        
        if total_duration <= time_constraint:
            return modules
        
        # Need to reduce content
        reduction_factor = time_constraint / total_duration
        
        adjusted_modules = []
        for module in modules:
            # Reduce module duration
            module.estimated_duration = int(module.estimated_duration * reduction_factor)
            
            # Remove some activities if needed
            if reduction_factor < 0.7:
                module.activities = module.activities[:max(1, len(module.activities) // 2)]
            
            adjusted_modules.append(module)
        
        return adjusted_modules
    
    async def _evaluate_adaptation_rule(
        self,
        rule: AdaptationRule,
        learning_path: LearningPath,
        constellation_state: ConstellationState,
        context: Any  # TODO: Define IntelligentContext
    ) -> bool:
        """Evaluate if an adaptation rule should trigger."""
        if rule.trigger == AdaptationTrigger.LOW_ENGAGEMENT:
            return context.engagement_level < 0.4
        
        elif rule.trigger == AdaptationTrigger.POOR_PERFORMANCE:
            current_module = learning_path.get_current_module()
            return current_module and current_module.effectiveness_score < 0.5
        
        elif rule.trigger == AdaptationTrigger.RAPID_PROGRESS:
            return (
                constellation_state.progress_percentage > 70 and
                context.learning_velocity > 0.8
            )
        
        elif rule.trigger == AdaptationTrigger.STYLE_MISMATCH:
            return context.topic_familiarity < 0.6 and context.engagement_level < 0.6
        
        return False
    
    async def _determine_adaptations(
        self,
        learning_path: LearningPath,
        constellation_state: ConstellationState,
        user_profile: UserProfile
    ) -> List[Dict[str, Any]]:
        """Determine what adaptations should be made."""
        adaptations = []
        
        # Check engagement level
        if constellation_state.engagement_level < 0.4:
            adaptations.append({
                "type": "increase_interactivity",
                "reason": "Low engagement detected",
                "changes": ["add_practice_modules", "reduce_theory_content"]
            })
        
        # Check progress rate
        current_module = learning_path.get_current_module()
        if current_module and current_module.effectiveness_score < 0.5:
            adaptations.append({
                "type": "add_support_content",
                "reason": "Poor performance in current module",
                "changes": ["add_review_module", "reduce_difficulty"]
            })
        
        # Check if user is progressing too quickly
        if constellation_state.progress_percentage > 80:
            adaptations.append({
                "type": "add_advanced_content",
                "reason": "Rapid progress detected",
                "changes": ["add_advanced_modules", "increase_difficulty"]
            })
        
        return adaptations
    
    async def _apply_adaptations(
        self,
        learning_path: LearningPath,
        adaptations: List[Dict[str, Any]]
    ) -> LearningPath:
        """Apply adaptations to a learning path."""
        adapted_path = learning_path  # In a real implementation, you'd create a copy
        
        for adaptation in adaptations:
            adaptation_type = adaptation["type"]
            changes = adaptation.get("changes", [])
            
            if adaptation_type == "increase_interactivity":
                # Add more practice modules
                for change in changes:
                    if change == "add_practice_modules":
                        await self._add_practice_modules(adapted_path)
            
            elif adaptation_type == "add_support_content":
                # Add review and support content
                for change in changes:
                    if change == "add_review_module":
                        await self._add_review_module(adapted_path)
                    elif change == "reduce_difficulty":
                        await self._reduce_difficulty(adapted_path)
            
            elif adaptation_type == "add_advanced_content":
                # Add more challenging content
                for change in changes:
                    if change == "add_advanced_modules":
                        await self._add_advanced_modules(adapted_path)
        
        return adapted_path
    
    async def _add_practice_modules(self, learning_path: LearningPath) -> None:
        """Add practice modules to the learning path."""
        current_module = learning_path.get_current_module()
        if current_module:
            # Create a practice module based on current module
            practice_module = LearningModule(
                module_id=f"practice_{current_module.module_id}",
                title=f"Practice: {current_module.title}",
                description=f"Hands-on practice for {current_module.title}",
                module_type=ModuleType.PRACTICE,
                objectives=current_module.objectives,
                estimated_duration=30,
                difficulty_level=current_module.difficulty_level
            )
            
            # Insert after current module
            current_index = learning_path.modules.index(current_module)
            learning_path.modules.insert(current_index + 1, practice_module)
    
    async def _add_review_module(self, learning_path: LearningPath) -> None:
        """Add a review module to reinforce learning."""
        current_module = learning_path.get_current_module()
        if current_module:
            review_module = LearningModule(
                module_id=f"review_{current_module.module_id}",
                title=f"Review: {current_module.title}",
                description=f"Review and reinforce {current_module.title}",
                module_type=ModuleType.REVIEW,
                objectives=current_module.objectives,
                estimated_duration=20,
                difficulty_level=DifficultyLevel.BEGINNER  # Reviews are easier
            )
            
            # Insert before current module
            current_index = learning_path.modules.index(current_module)
            learning_path.modules.insert(current_index, review_module)
    
    async def _reduce_difficulty(self, learning_path: LearningPath) -> None:
        """Reduce difficulty of remaining modules."""
        current_module = learning_path.get_current_module()
        if current_module:
            current_index = learning_path.modules.index(current_module)
            for module in learning_path.modules[current_index:]:
                module.difficulty_level = self._adjust_difficulty_level(module.difficulty_level, -1)
    
    async def _add_advanced_modules(self, learning_path: LearningPath) -> None:
        """Add advanced modules for fast learners."""
        # Add an advanced exploration module
        advanced_module = LearningModule(
            module_id=f"advanced_{len(learning_path.modules)}",
            title="Advanced Topics and Best Practices",
            description="Explore advanced concepts and industry best practices",
            module_type=ModuleType.EXPLORATION,
            objectives=[],
            estimated_duration=45,
            difficulty_level=DifficultyLevel.ADVANCED
        )
        
        learning_path.modules.append(advanced_module)
    
    def _adjust_difficulty_level(self, current_level: DifficultyLevel, adjustment: int) -> DifficultyLevel:
        """Adjust difficulty level by a given amount."""
        levels = [DifficultyLevel.BEGINNER, DifficultyLevel.INTERMEDIATE, DifficultyLevel.ADVANCED, DifficultyLevel.EXPERT]
        
        try:
            current_index = levels.index(current_level)
            new_index = max(0, min(len(levels) - 1, current_index + adjustment))
            return levels[new_index]
        except ValueError:
            return current_level
    
    async def _create_practice_module(self, topic: str, framework: SupportedFrameworks) -> LearningModule:
        """Create a practice module for a specific topic."""
        return LearningModule(
            module_id=f"practice_{topic.lower().replace(' ', '_')}",
            title=f"Practice: {topic}",
            description=f"Hands-on practice with {topic} in {framework.value}",
            module_type=ModuleType.PRACTICE,
            objectives=[],
            estimated_duration=30,
            difficulty_level=DifficultyLevel.INTERMEDIATE
        )
    
    async def _load_persisted_paths(self) -> None:
        """Load any persisted learning paths."""
        # In a real implementation, this would load from a database
        # For now, we'll just initialize an empty dictionary
        self.active_paths = {}

    # =====================================
    # REAL-TIME LEARNING ANALYTICS METHODS  
    # =====================================

    async def initialize_analytics_session(
        self,
        user_id: str,
        session_id: str,
        framework: SupportedFrameworks
    ) -> LearningAnalytics:
        """Initialize real-time analytics for a learning session."""
        analytics = LearningAnalytics(
            user_id=user_id,
            session_id=session_id,
            framework=framework
        )
        
        self.real_time_analytics[session_id] = analytics
        self.performance_insights[session_id] = []
        
        logger.info(f"Initialized real-time analytics for session {session_id}")
        return analytics

    async def update_engagement_metrics(
        self,
        session_id: str,
        constellation_state: ConstellationState,
        interaction_data: Dict[str, Any]
    ) -> None:
        """Update real-time engagement metrics."""
        if session_id not in self.real_time_analytics:
            return

        analytics = self.real_time_analytics[session_id]
        
        # Calculate engagement score from multiple factors
        message_sentiment = self._analyze_message_sentiment(constellation_state.messages[-1] if constellation_state.messages else None)
        response_time = interaction_data.get('response_time', 0)
        interaction_complexity = interaction_data.get('interaction_complexity', 0.5)
        
        # Weighted engagement calculation
        engagement_score = (
            message_sentiment * 0.4 +
            (1 - min(response_time / 60, 1)) * 0.3 +  # Faster responses = higher engagement
            interaction_complexity * 0.3
        )
        
        analytics.engagement_score = self._smooth_metric(analytics.engagement_score, engagement_score, 0.3)
        analytics.engagement_trend.append(analytics.engagement_score)
        
        # Update interaction frequency
        analytics.interaction_frequency = len(constellation_state.messages) / max(analytics.session_duration, 1)
        
        # Update cognitive load estimation
        help_requests = sum(1 for msg in constellation_state.messages[-10:] 
                          if any(keyword in msg.content.lower() for keyword in ['help', 'confused', 'don\'t understand']))
        analytics.cognitive_load = min(help_requests / 10, 1.0)
        
        analytics.last_updated = datetime.now()

    async def update_comprehension_metrics(
        self,
        session_id: str,
        constellation_state: ConstellationState,
        assessment_data: Dict[str, Any]
    ) -> None:
        """Update real-time comprehension metrics."""
        if session_id not in self.real_time_analytics:
            return

        analytics = self.real_time_analytics[session_id]
        
        # Calculate comprehension confidence from assessment responses
        correct_answers = assessment_data.get('correct_answers', 0)
        total_questions = assessment_data.get('total_questions', 1)
        confidence_indicators = assessment_data.get('confidence_indicators', [])
        
        # Calculate comprehension confidence
        accuracy_score = correct_answers / total_questions
        confidence_score = np.mean(confidence_indicators) if confidence_indicators else 0.5
        
        comprehension_confidence = (accuracy_score * 0.7 + confidence_score * 0.3)
        analytics.comprehension_confidence = self._smooth_metric(
            analytics.comprehension_confidence, comprehension_confidence, 0.4
        )
        
        analytics.comprehension_trend.append(analytics.comprehension_confidence)
        
        # Update error rate
        recent_errors = assessment_data.get('recent_errors', 0)
        recent_attempts = assessment_data.get('recent_attempts', 1)
        analytics.error_rate = self._smooth_metric(
            analytics.error_rate, recent_errors / recent_attempts, 0.5
        )
        
        analytics.last_updated = datetime.now()

    async def update_learning_velocity_metrics(
        self,
        session_id: str,
        constellation_state: ConstellationState,
        progress_data: Dict[str, Any]
    ) -> None:
        """Update learning velocity metrics."""
        if session_id not in self.real_time_analytics:
            return

        analytics = self.real_time_analytics[session_id]
        
        # Update session duration
        session_start = progress_data.get('session_start', datetime.now())
        analytics.session_duration = (datetime.now() - session_start).total_seconds() / 60
        
        # Calculate concepts learned per hour
        concepts_learned = len(constellation_state.topics_covered)
        if analytics.session_duration > 0:
            analytics.concepts_learned_per_hour = (concepts_learned / analytics.session_duration) * 60
        
        # Calculate exercises completed per hour
        exercises_completed = len(constellation_state.exercises_completed)
        if analytics.session_duration > 0:
            analytics.exercises_completed_per_hour = (exercises_completed / analytics.session_duration) * 60
        
        # Update velocity trend
        velocity_score = (analytics.concepts_learned_per_hour + analytics.exercises_completed_per_hour) / 2
        analytics.velocity_trend.append(velocity_score)
        
        # Knowledge retention estimation (based on consistency of performance)
        if len(analytics.comprehension_trend) >= 3:
            recent_comprehension = analytics.comprehension_trend[-3:]
            retention_stability = 1 - np.std(recent_comprehension)
            analytics.knowledge_retention_rate = max(0, min(1, retention_stability))
        
        analytics.last_updated = datetime.now()

    async def update_elt_specific_metrics(
        self,
        session_id: str,
        constellation_state: ConstellationState,
        agent_data: Dict[str, Any]
    ) -> None:
        """Update ELT-specific analytics metrics."""
        if session_id not in self.real_time_analytics:
            return

        analytics = self.real_time_analytics[session_id]
        
        # Agent handoff efficiency
        total_handoffs = len(constellation_state.agent_handoffs)
        successful_handoffs = agent_data.get('successful_handoffs', total_handoffs)
        if total_handoffs > 0:
            analytics.agent_handoff_efficiency = successful_handoffs / total_handoffs
        
        # Constellation adaptation rate
        adaptations = agent_data.get('adaptations', 0)
        session_interactions = len(constellation_state.messages)
        if session_interactions > 0:
            analytics.constellation_adaptation_rate = adaptations / session_interactions
        
        # Learning path optimization score
        path_effectiveness = agent_data.get('path_effectiveness', 0.5)
        user_satisfaction = agent_data.get('user_satisfaction', 0.5)
        analytics.learning_path_optimization_score = (path_effectiveness + user_satisfaction) / 2
        
        analytics.last_updated = datetime.now()

    async def generate_performance_insights(self, session_id: str) -> List[PerformanceInsight]:
        """Generate actionable performance insights from analytics."""
        if session_id not in self.real_time_analytics:
            return []

        analytics = self.real_time_analytics[session_id]
        insights = []
        
        # Low engagement insight
        if analytics.engagement_score < self.analytics_thresholds["low_engagement"]:
            insights.append(PerformanceInsight(
                insight_type="engagement",
                priority=0.9,
                title="Low Engagement Detected",
                description=f"Current engagement score is {analytics.engagement_score:.2f}, below optimal threshold",
                recommendation="Consider switching to more interactive content or hands-on activities",
                data_points=analytics.engagement_trend[-5:],
                confidence_score=0.8,
                actionable=True
            ))
        
        # High cognitive load insight
        if analytics.cognitive_load > self.analytics_thresholds["high_cognitive_load"]:
            insights.append(PerformanceInsight(
                insight_type="cognitive_load",
                priority=0.8,
                title="High Cognitive Load",
                description=f"Cognitive load is {analytics.cognitive_load:.2f}, indicating potential overwhelm",
                recommendation="Break down content into smaller chunks or add review materials",
                data_points=[analytics.cognitive_load],
                confidence_score=0.7,
                actionable=True
            ))
        
        # Low comprehension insight
        if analytics.comprehension_confidence < self.analytics_thresholds["low_comprehension"]:
            insights.append(PerformanceInsight(
                insight_type="comprehension",
                priority=0.85,
                title="Comprehension Challenges",
                description=f"Comprehension confidence is {analytics.comprehension_confidence:.2f}",
                recommendation="Provide additional explanations, examples, or prerequisite review",
                data_points=analytics.comprehension_trend[-5:],
                confidence_score=0.9,
                actionable=True
            ))
        
        # Rapid progress insight
        velocity_avg = np.mean(analytics.velocity_trend[-3:]) if len(analytics.velocity_trend) >= 3 else 0
        if velocity_avg > self.analytics_thresholds["rapid_progress"]:
            insights.append(PerformanceInsight(
                insight_type="progress",
                priority=0.7,
                title="Rapid Progress Detected",
                description=f"Learning velocity is {velocity_avg:.2f}, above average",
                recommendation="Consider introducing advanced topics or challenging projects",
                data_points=analytics.velocity_trend[-5:],
                confidence_score=0.8,
                actionable=True
            ))
        
        self.performance_insights[session_id] = insights
        return insights

    async def get_real_time_dashboard_data(self, session_id: str) -> Dict[str, Any]:
        """Get comprehensive dashboard data for real-time monitoring."""
        if session_id not in self.real_time_analytics:
            return {}

        analytics = self.real_time_analytics[session_id]
        insights = await self.generate_performance_insights(session_id)
        
        return {
            "session_id": session_id,
            "analytics": {
                "engagement": {
                    "current_score": analytics.engagement_score,
                    "trend": analytics.engagement_trend[-10:],
                    "status": "good" if analytics.engagement_score > 0.6 else "needs_attention"
                },
                "comprehension": {
                    "confidence": analytics.comprehension_confidence,
                    "trend": analytics.comprehension_trend[-10:],
                    "error_rate": analytics.error_rate,
                    "status": "good" if analytics.comprehension_confidence > 0.7 else "needs_support"
                },
                "velocity": {
                    "concepts_per_hour": analytics.concepts_learned_per_hour,
                    "exercises_per_hour": analytics.exercises_completed_per_hour,
                    "retention_rate": analytics.knowledge_retention_rate,
                    "trend": analytics.velocity_trend[-10:]
                },
                "elt_metrics": {
                    "handoff_efficiency": analytics.agent_handoff_efficiency,
                    "adaptation_rate": analytics.constellation_adaptation_rate,
                    "optimization_score": analytics.learning_path_optimization_score
                },
                "real_time_indicators": {
                    "difficulty_comfort": analytics.current_difficulty_comfort,
                    "motivation_level": analytics.motivation_level,
                    "cognitive_load": analytics.cognitive_load
                }
            },
            "insights": [
                {
                    "type": insight.insight_type,
                    "priority": insight.priority,
                    "title": insight.title,
                    "description": insight.description,
                    "recommendation": insight.recommendation,
                    "actionable": insight.actionable
                } for insight in insights
            ],
            "recommendations": self._generate_immediate_recommendations(analytics, insights),
            "last_updated": analytics.last_updated.isoformat()
        }

    def _analyze_message_sentiment(self, message) -> float:
        """Analyze sentiment of user message for engagement calculation."""
        if not message or not hasattr(message, 'content'):
            return 0.5
        
        content = message.content.lower()
        
        # Simple sentiment analysis based on keywords
        positive_words = ['great', 'good', 'excellent', 'amazing', 'love', 'perfect', 'understand', 'clear']
        negative_words = ['bad', 'difficult', 'confused', 'hard', 'frustrated', 'unclear', 'stuck']
        
        positive_count = sum(1 for word in positive_words if word in content)
        negative_count = sum(1 for word in negative_words if word in content)
        
        if positive_count + negative_count == 0:
            return 0.5
        
        return positive_count / (positive_count + negative_count)

    def _smooth_metric(self, current_value: float, new_value: float, alpha: float) -> float:
        """Apply exponential smoothing to metrics for stability."""
        return alpha * new_value + (1 - alpha) * current_value

    def _generate_immediate_recommendations(
        self, 
        analytics: LearningAnalytics, 
        insights: List[PerformanceInsight]
    ) -> List[str]:
        """Generate immediate actionable recommendations."""
        recommendations = []
        
        high_priority_insights = [i for i in insights if i.priority > 0.8]
        
        if high_priority_insights:
            for insight in high_priority_insights:
                recommendations.append(insight.recommendation)
        
        # General recommendations based on analytics
        if analytics.engagement_score < 0.5 and analytics.cognitive_load > 0.7:
            recommendations.append("Take a short break and return with simpler content")
        
        if analytics.comprehension_confidence > 0.8 and analytics.motivation_level > 0.7:
            recommendations.append("Ready for more challenging content")
        
        return recommendations[:3]  # Return top 3 recommendations

    async def get_learning_path(self, path_id: str) -> Optional[LearningPath]:
        """
        Get a learning path by ID.
        
        Args:
            path_id: The ID of the learning path to retrieve
            
        Returns:
            The learning path if found, None otherwise
        """
        return self.active_paths.get(path_id)
    
    async def process_quiz_results(self, state: ConstellationState, quiz_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process quiz results and update the learning path accordingly.
        
        Args:
            state: Current constellation state
            quiz_results: Quiz results data
            
        Returns:
            Dict with adaptation information
        """
        if not state.curriculum_path_id:
            return {"error": "No curriculum path associated with this session"}
            
        # Get the learning path
        learning_path = self.active_paths.get(state.curriculum_path_id)
        if not learning_path:
            return {"error": "Learning path not found"}
            
        # Extract quiz information
        topic = quiz_results.get("topic", "")
        score = quiz_results.get("score", 0.0)
        questions = quiz_results.get("total_questions", 0)
        correct = quiz_results.get("correct_count", 0)
        
        # Update analytics
        await self.update_comprehension_metrics(
            session_id=state.session_id,
            constellation_state=state,
            assessment_data={
                "topic": topic,
                "score": score,
                "questions": questions,
                "correct": correct
            }
        )
        
        # Determine if we need to adapt the curriculum
        adaptation_needed = False
        adaptation_type = None
        
        # Check if score is too low (below 50%)
        if score < 0.5:
            adaptation_needed = True
            adaptation_type = "remediation"
        
        # Check if score is very high (above 90%)
        elif score > 0.9:
            adaptation_needed = True
            adaptation_type = "acceleration"
        
        # Apply adaptation if needed
        adaptation_result = {}
        if adaptation_needed:
            if adaptation_type == "remediation":
                # Add remediation content
                adaptation_result = await self._add_remediation_content(
                    learning_path=learning_path,
                    topic=topic,
                    score=score
                )
            elif adaptation_type == "acceleration":
                # Accelerate the learning path
                adaptation_result = await self._accelerate_learning_path(
                    learning_path=learning_path,
                    topic=topic
                )
        
        # Update the learning path
        learning_path.last_adapted = datetime.now()
        learning_path.adaptation_count += 1
        
        # Return the results
        return {
            "topic": topic,
            "score": score,
            "adaptation_needed": adaptation_needed,
            "adaptation_type": adaptation_type,
            "adaptation_result": adaptation_result
        }
    
    async def _add_remediation_content(
        self,
        learning_path: LearningPath,
        topic: str,
        score: float
    ) -> Dict[str, Any]:
        """
        Add remediation content to the learning path.
        
        Args:
            learning_path: The learning path to adapt
            topic: The topic that needs remediation
            score: The quiz score
            
        Returns:
            Dict with remediation information
        """
        # Get the current module
        current_module = learning_path.get_current_module()
        if not current_module:
            return {"error": "Current module not found"}
            
        # Find the objective related to the topic
        related_objectives = [
            obj for obj in current_module.objectives 
            if topic.lower() in obj.description.lower()
        ]
        
        if not related_objectives:
            return {"error": "No related objectives found"}
            
        # Get the related objective
        related_objective = related_objectives[0]
        
        # Create a remediation objective
        remediation_objective = LearningObjective(
            objective_id=f"{related_objective.objective_id}_remediation",
            description=f"Review and reinforce: {related_objective.description}",
            framework=related_objective.framework,
            difficulty_level="beginner",  # Lower difficulty for remediation
            estimated_duration=30,  # 30 minutes for review
            prerequisites=[],
            skills_developed=related_objective.skills_developed,
            assessment_criteria=[
                "Can explain the concept clearly",
                "Can apply the concept in simple examples"
            ],
            completion_status=False,
            mastery_level=0.0
        )
        
        # Insert the remediation objective after the related objective
        objective_index = current_module.objectives.index(related_objective)
        current_module.objectives.insert(objective_index + 1, remediation_objective)
        
        # Update the module's estimated duration
        current_module.estimated_duration += remediation_objective.estimated_duration
        
        # Update the learning path's total estimated duration
        learning_path.total_estimated_duration += remediation_objective.estimated_duration
        
        return {
            "remediation_objective": remediation_objective.description,
            "inserted_after": related_objective.description,
            "module": current_module.title
        }
    
    async def _accelerate_learning_path(
        self,
        learning_path: LearningPath,
        topic: str
    ) -> Dict[str, Any]:
        """
        Accelerate the learning path by skipping or combining objectives.
        
        Args:
            learning_path: The learning path to adapt
            topic: The topic that was mastered
            
        Returns:
            Dict with acceleration information
        """
        # Get the current module
        current_module = learning_path.get_current_module()
        if not current_module:
            return {"error": "Current module not found"}
            
        # Find the objective related to the topic
        related_objectives = [
            obj for obj in current_module.objectives 
            if topic.lower() in obj.description.lower()
        ]
        
        if not related_objectives:
            return {"error": "No related objectives found"}
            
        # Get the related objective
        related_objective = related_objectives[0]
        
        # Mark the objective as completed with high mastery
        related_objective.completion_status = True
        related_objective.mastery_level = 1.0
        
        # Check if there are similar objectives that can be skipped
        similar_objectives = [
            obj for obj in current_module.objectives 
            if obj != related_objective and 
            any(skill in obj.skills_developed for skill in related_objective.skills_developed) and
            not obj.completion_status
        ]
        
        skipped_objectives = []
        for obj in similar_objectives[:1]:  # Skip at most 1 similar objective
            obj.completion_status = True
            obj.mastery_level = 0.8  # High mastery but not perfect
            skipped_objectives.append(obj.description)
        
        # Update module completion percentage
        completed_objectives = sum(1 for obj in current_module.objectives if obj.completion_status)
        current_module.completion_percentage = (completed_objectives / len(current_module.objectives)) * 100
        
        # Update learning path completion percentage
        total_objectives = sum(len(module.objectives) for module in learning_path.modules)
        completed_total = sum(sum(1 for obj in module.objectives if obj.completion_status) 
                             for module in learning_path.modules)
        learning_path.completion_percentage = (completed_total / total_objectives) * 100 if total_objectives > 0 else 0
        
        return {
            "mastered_objective": related_objective.description,
            "skipped_objectives": skipped_objectives,
            "module_completion": f"{current_module.completion_percentage:.1f}%",
            "path_completion": f"{learning_path.completion_percentage:.1f}%"
        }
    
    async def get_next_learning_objectives(self, state: ConstellationState) -> List[Dict[str, Any]]:
        """
        Get the next learning objectives for the current curriculum position.
        
        Args:
            state: Current constellation state
            
        Returns:
            List of next learning objectives
        """
        if not state.curriculum_path_id:
            return []
            
        # Get the learning path
        learning_path = self.active_paths.get(state.curriculum_path_id)
        if not learning_path:
            return []
            
        # Get the current module
        current_module = learning_path.get_current_module()
        if not current_module:
            return []
            
        # Get incomplete objectives from the current module
        incomplete_objectives = [
            obj for obj in current_module.objectives 
            if not obj.completion_status
        ]
        
        # Format the objectives
        formatted_objectives = []
        for obj in incomplete_objectives[:3]:  # Return at most 3 objectives
            formatted_objectives.append({
                "id": obj.objective_id,
                "description": obj.description,
                "difficulty": obj.difficulty_level,
                "estimated_duration": obj.estimated_duration,
                "skills": obj.skills_developed,
                "mastery": obj.mastery_level
            })
            
        return formatted_objectives 