"""
Vector Storage Module for Framework Knowledge
Provides specialized vector storage capabilities for framework knowledge persistence.
"""

import logging
import json
import os
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from pathlib import Path
from src.pyframeworks_assistant.config.settings import settings
try:
    import chromadb
    from chromadb.config import Settings as ChromaSettings
    CHROMADB_AVAILABLE = True
except ImportError:
    CHROMADB_AVAILABLE = False
    logging.warning("ChromaDB not available - vector storage will be disabled")

try:
    import numpy as np
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False
    logging.warning("SentenceTransformers not available - using basic embeddings")

from ..config.framework_configs import SupportedFrameworks
from ..core.framework_initializer import FrameworkContext

logger = logging.getLogger(__name__)


class FrameworkVectorStorage:
    """Vector storage for framework knowledge."""
    
    def __init__(self):
        """Initialize the vector storage."""
        self.available = CHROMADB_AVAILABLE
        self.embeddings_available = SENTENCE_TRANSFORMERS_AVAILABLE
        self.data_dir = Path(settings.chroma_persist_directory)
        self.data_dir.mkdir(parents=True, exist_ok=True)
        self.fallback_json_storage = Path("./data/fallback_storage")
        self.fallback_json_storage.mkdir(parents=True, exist_ok=True)
        
        if self.available:
            try:
                self.client = chromadb.PersistentClient(
                    path=str(self.data_dir),
                    settings=ChromaSettings(
                        anonymized_telemetry=False
                    )
                )
                logger.info("ChromaDB initialized successfully")
                
                # Initialize embedding model if available
                if self.embeddings_available:
                    try:
                        self.embedding_model = SentenceTransformer("all-MiniLM-L6-v2")
                        logger.info("Embedding model initialized successfully")
                    except Exception as e:
                        logger.error(f"Failed to initialize embedding model: {e}")
                        logger.warning("Using fallback embeddings instead")
                        self.embeddings_available = False
            except Exception as e:
                logger.error(f"Failed to initialize ChromaDB: {e}")
                self.available = False
                logger.warning("Using fallback JSON storage instead")
        
    def _get_or_create_collection(self, framework: SupportedFrameworks):
        """Get or create a collection for a framework."""
        if not self.available:
            return None
            
        collection_name = f"framework_{framework.value}"
        try:
            return self.client.get_or_create_collection(
                name=collection_name,
                metadata={"framework": framework.value}
            )
        except Exception as e:
            logger.error(f"Failed to get or create collection: {e}")
            self.available = False  # Mark as unavailable for future calls
            return None
    
    def _generate_embeddings(self, text: str) -> List[float]:
        """Generate embeddings for text."""
        if not self.embeddings_available:
            # Fallback to simple hash-based embedding if model not available
            import hashlib
            hash_val = int(hashlib.md5(text.encode()).hexdigest(), 16)
            return [hash_val % 100 / 100.0] * 10  # Simple 10-dim embedding
            
        # Use sentence transformer model
        try:
            return self.embedding_model.encode(text).tolist()
        except Exception as e:
            logger.error(f"Error generating embeddings: {e}")
            # Fall back to simple hash-based embedding
            import hashlib
            hash_val = int(hashlib.md5(text.encode()).hexdigest(), 16)
            return [hash_val % 100 / 100.0] * 10  # Simple 10-dim embedding
    
    def _save_to_fallback_storage(self, framework: SupportedFrameworks, data: Dict[str, Any]) -> bool:
        """Save data to fallback JSON storage when vector DB is unavailable."""
        try:
            file_path = self.fallback_json_storage / f"{framework.value}_context.json"
            with open(file_path, 'w') as f:
                json.dump(data, f, indent=2)
            logger.info(f"Saved framework context to fallback storage: {file_path}")
            return True
        except Exception as e:
            logger.error(f"Failed to save to fallback storage: {e}")
            return False
    
    def _load_from_fallback_storage(self, framework: SupportedFrameworks) -> Dict[str, Any]:
        """Load data from fallback JSON storage when vector DB is unavailable."""
        try:
            file_path = self.fallback_json_storage / f"{framework.value}_context.json"
            if not file_path.exists():
                return {}
            with open(file_path, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Failed to load from fallback storage: {e}")
            return {}
    
    async def store_framework_context(self, context: FrameworkContext) -> bool:
        """Store framework context in vector storage."""
        if not self.available:
            logger.warning("Vector storage not available - using fallback JSON storage")
            # Create a simplified representation for fallback storage
            fallback_data = {
                "framework": context.framework.value,
                "version": context.version,
                "description": context.description,
                "key_concepts": context.key_concepts,
                "syntax": {
                    "basic_patterns": context.syntax.basic_patterns,
                    "advanced_patterns": context.syntax.advanced_patterns,
                    "common_imports": context.syntax.common_imports,
                    "boilerplate_code": context.syntax.boilerplate_code
                },
                "modules": [
                    {
                        "name": module.name,
                        "description": module.description,
                        "primary_functions": module.primary_functions
                    } for module in context.core_modules
                ],
                "examples": [
                    {
                        "title": example.title,
                        "description": example.description,
                        "code_example": example.code_example,
                        "explanation": example.explanation
                    } for example in context.practice_examples
                ],
                "timestamp": datetime.now().isoformat()
            }
            return self._save_to_fallback_storage(context.framework, fallback_data)
    
    async def retrieve_framework_context(self, framework: SupportedFrameworks, query: str = None) -> List[Dict[str, Any]]:
        """Retrieve framework context from vector storage."""
        if not self.available:
            logger.warning("Vector storage not available - using fallback JSON storage")
            fallback_data = self._load_from_fallback_storage(framework)
            if not fallback_data:
                return []
                
            # Convert fallback data to the expected format
            results = []
            
            # Basic info
            results.append({
                "id": f"{framework.value}_basic_info",
                "content": f"Framework: {framework.value}\nVersion: {fallback_data.get('version', 'latest')}\nDescription: {fallback_data.get('description', '')}",
                "metadata": {"type": "basic_info", "framework": framework.value}
            })
            
            # Key concepts
            if "key_concepts" in fallback_data:
                results.append({
                    "id": f"{framework.value}_key_concepts",
                    "content": "Key concepts: " + ", ".join(fallback_data["key_concepts"]),
                    "metadata": {"type": "key_concepts", "framework": framework.value}
                })
                
            # Syntax
            if "syntax" in fallback_data:
                syntax = fallback_data["syntax"]
                syntax_info = f"Basic patterns: {', '.join(syntax.get('basic_patterns', []))}\n"
                syntax_info += f"Advanced patterns: {', '.join(syntax.get('advanced_patterns', []))}\n"
                syntax_info += f"Common imports: {', '.join(syntax.get('common_imports', []))}\n"
                syntax_info += f"Boilerplate code: {syntax.get('boilerplate_code', '')}"
                results.append({
                    "id": f"{framework.value}_syntax",
                    "content": syntax_info,
                    "metadata": {"type": "syntax", "framework": framework.value}
                })
                
            # Modules
            if "modules" in fallback_data:
                modules_info = []
                for module in fallback_data["modules"]:
                    module_text = f"Module: {module.get('name', '')}\nDescription: {module.get('description', '')}\n"
                    module_text += f"Primary functions: {', '.join(module.get('primary_functions', []))}"
                    modules_info.append(module_text)
                    
                modules_text = "\n\n".join(modules_info)
                results.append({
                    "id": f"{framework.value}_modules",
                    "content": modules_text,
                    "metadata": {"type": "modules", "framework": framework.value}
                })
                
            # Examples
            if "examples" in fallback_data:
                examples_info = []
                for example in fallback_data["examples"]:
                    example_text = f"Example: {example.get('title', '')}\nDescription: {example.get('description', '')}\n"
                    example_text += f"Code:\n{example.get('code_example', '')}\n"
                    example_text += f"Explanation: {example.get('explanation', '')}"
                    examples_info.append(example_text)
                    
                examples_text = "\n\n".join(examples_info)
                results.append({
                    "id": f"{framework.value}_examples",
                    "content": examples_text,
                    "metadata": {"type": "examples", "framework": framework.value}
                })
                
            return results
            
        try:
            collection = self._get_or_create_collection(framework)
            if not collection:
                logger.warning("Collection not available - falling back to JSON storage")
                return await self.retrieve_framework_context(framework, query)
                
            # If query is provided, search for relevant content
            if query:
                query_embedding = self._generate_embeddings(query)
                results = collection.query(
                    query_embeddings=[query_embedding],
                    n_results=5,
                    include=["metadatas", "documents", "distances"]
                )
            else:
                # Otherwise retrieve all framework content
                results = collection.get(
                    where={"framework": framework.value},
                    include=["metadatas", "documents"]
                )
            
            # Format results
            formatted_results = []
            if "ids" in results and results["ids"]:
                for i in range(len(results["ids"])):
                    formatted_results.append({
                        "id": results["ids"][i],
                        "content": results["documents"][i],
                        "metadata": results["metadatas"][i] if "metadatas" in results else {}
                    })
                    
            return formatted_results
            
        except Exception as e:
            logger.error(f"Failed to retrieve framework context: {e}")
            logger.warning("Falling back to JSON storage")
            # If vector DB fails, try fallback storage
            return await self.retrieve_framework_context(framework, query)
    
    async def search_framework_knowledge(self, query: str, framework: Optional[SupportedFrameworks] = None) -> List[Dict[str, Any]]:
        """Search for framework knowledge using a query."""
        if not self.available:
            logger.warning("Vector storage not available - skipping framework knowledge search")
            return []
            
        try:
            if framework:
                # Search in specific framework collection
                return await self.retrieve_framework_context(framework, query)
            else:
                # Search across all framework collections
                all_results = []
                for fw in SupportedFrameworks:
                    results = await self.retrieve_framework_context(fw, query)
                    all_results.extend(results)
                
                # Sort by relevance (distance)
                all_results.sort(key=lambda x: x.get("distance", 1.0))
                return all_results[:5]  # Return top 5 results
                
        except Exception as e:
            logger.error(f"Failed to search framework knowledge: {e}")
            return []


# Global instance
vector_storage = FrameworkVectorStorage()


async def get_vector_storage() -> FrameworkVectorStorage:
    """Get the vector storage instance."""
    return vector_storage 