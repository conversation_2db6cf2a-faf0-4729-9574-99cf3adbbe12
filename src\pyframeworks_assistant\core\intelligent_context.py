"""
Intelligent Context - Provides context-aware analysis of user interactions and intents.
"""

import logging
from typing import Dict, List, Any, Optional, Set
from enum import Enum

from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class UserIntent(str, Enum):
    """Enumeration of user intents for intelligent context analysis."""
    LEARNING = "learning"
    CODING = "coding"
    TROUBLESHOOTING = "troubleshooting"
    DOCUMENTATION = "documentation"
    PROJECT_PLANNING = "project_planning"
    ASSESSMENT = "assessment"
    EXPLORATION = "exploration"
    CLARIFICATION = "clarification"
    PRACTICE = "practice"
    GENERAL_QUESTION = "general_question"


class ProactiveAction(BaseModel):
    """Model for proactive actions that can be taken by the system."""
    action_type: str = Field(description="Type of proactive action")
    description: str = Field(description="Description of the action")
    priority: float = Field(description="Priority score (0-1)")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")


class IntelligentContext(BaseModel):
    """
    Intelligent context for user interactions, providing insights about user state,
    intent, and learning patterns to guide agent selection and responses.
    """
    user_intent: UserIntent = Field(description="Detected user intent")
    topic_familiarity: float = Field(default=0.5, description="Estimated user familiarity with current topic (0-1)")
    engagement_level: float = Field(default=0.5, description="User engagement level (0-1)")
    learning_velocity: float = Field(default=0.5, description="User's learning speed (0-1)")
    recent_struggles: List[str] = Field(default_factory=list, description="Topics user has recently struggled with")
    knowledge_gaps: List[str] = Field(default_factory=list, description="Detected knowledge gaps")
    preferred_learning_style: Optional[str] = Field(default=None, description="User's preferred learning style")
    session_context: Dict[str, Any] = Field(default_factory=dict, description="Additional session context") 