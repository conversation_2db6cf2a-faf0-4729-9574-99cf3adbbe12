"""
Assessment Agent - Handles assessment, testing, and progress evaluation.
"""

from typing import Optional, Dict, List, Any
import json
import asyncio
from datetime import datetime
from langchain_core.messages import HumanMessage
from langchain_core.language_models.chat_models import BaseChatModel

from .base_agent import BaseAgent
from ..core.constellation import ConstellationState, AgentRole


class QuizGenerator:
    """Generates quizzes and evaluates responses for learning assessment."""
    
    def __init__(self, model: BaseChatModel):
        """Initialize the quiz generator with a language model."""
        self.model = model
        
    async def generate_quiz(self, topic: str, difficulty: str, num_questions: int = 3, framework: str = None, context: str = None) -> Dict[str, Any]:
        """
        Generate a quiz on a specific topic.
        
        Args:
            topic: The topic to generate questions about
            difficulty: The difficulty level (beginner, intermediate, advanced)
            num_questions: Number of questions to generate
            framework: Optional framework context
            context: Optional additional context to improve question quality
            
        Returns:
            Dictionary containing quiz questions and answers
        """
        framework_context = f"within the {framework} framework" if framework else ""
        additional_context = f"\n\nAdditional context to consider when generating questions:\n{context}" if context else ""
        
        prompt = f"""Generate a {difficulty} level quiz on {topic} {framework_context} with {num_questions} questions.
For each question, provide:
1. The question text
2. Multiple choice options (A, B, C, D)
3. The correct answer
4. A brief explanation of why it's correct

Make sure the questions test understanding of concepts, not just memorization.
Include questions that test application of knowledge and problem-solving.{additional_context}

Format your response as a JSON object with this structure:
{{
  "topic": "{topic}",
  "difficulty": "{difficulty}",
  "questions": [
    {{
      "question": "Question text here",
      "options": {{
        "A": "First option",
        "B": "Second option",
        "C": "Third option",
        "D": "Fourth option"
      }},
      "correct_answer": "The correct letter (A, B, C, or D)",
      "explanation": "Explanation of the correct answer"
    }}
  ]
}}
"""
        
        response = await self.model.ainvoke(prompt)
        
        # Extract JSON from the response
        try:
            # Find JSON content between triple backticks if present
            content = response.content if hasattr(response, "content") else response
            json_str = content
            
            if "```json" in content:
                json_str = content.split("```json")[1].split("```")[0].strip()
            elif "```" in content:
                json_str = content.split("```")[1].split("```")[0].strip()
                
            quiz_data = json.loads(json_str)
            return quiz_data
        except Exception as e:
            # Fallback if JSON parsing fails
            return {
                "topic": topic,
                "difficulty": difficulty,
                "questions": [
                    {
                        "question": f"Failed to generate quiz: {str(e)}",
                        "options": {"A": "Error", "B": "Error", "C": "Error", "D": "Error"},
                        "correct_answer": "A",
                        "explanation": "Error in quiz generation"
                    }
                ]
            }
    
    async def evaluate_response(self, quiz_data: Dict[str, Any], user_answers: Dict[int, str]) -> Dict[str, Any]:
        """
        Evaluate user responses to a quiz.
        
        Args:
            quiz_data: The quiz data from generate_quiz
            user_answers: Dictionary mapping question index to answer letter
            
        Returns:
            Evaluation results with score and feedback
        """
        correct_count = 0
        total_questions = len(quiz_data["questions"])
        feedback = []
        
        for i, question in enumerate(quiz_data["questions"]):
            if i in user_answers:
                user_answer = user_answers[i]
                is_correct = user_answer == question["correct_answer"]
                
                if is_correct:
                    correct_count += 1
                    feedback.append({
                        "question_index": i,
                        "correct": True,
                        "feedback": "Correct! " + question["explanation"]
                    })
                else:
                    feedback.append({
                        "question_index": i,
                        "correct": False,
                        "feedback": f"Incorrect. The correct answer is {question['correct_answer']}: {question['explanation']}"
                    })
        
        score = correct_count / total_questions if total_questions > 0 else 0
        
        return {
            "score": score,
            "correct_count": correct_count,
            "total_questions": total_questions,
            "feedback": feedback
        }


class AssessmentAgent(BaseAgent):
    """Handles assessment, testing, and progress evaluation."""
    
    def __init__(self, model: Optional[BaseChatModel] = None):
        super().__init__(AgentRole.ASSESSMENT_AGENT, model, "assessment")
        self.quiz_generator = QuizGenerator(self.model)
    
    def get_system_prompt(self, state: ConstellationState) -> str:
        # Get curriculum context if available
        curriculum_context = ""
        if "current_module_name" in state.session_context:
            curriculum_context = f"""
Current Curriculum Context:
- Module: {state.session_context.get('current_module_name', 'Unknown')}
- Description: {state.session_context.get('current_module_description', '')}
- Progress: Module {state.current_module_index + 1} of {state.session_context.get('total_modules', '?')}
- Current Objective: {state.current_objective_index + 1} of {state.session_context.get('objectives_in_current_module', '?')}
- Current Topic: {state.session_context.get('current_objective_description', 'Unknown')}
"""

        # Get mastery levels if available
        mastery_info = ""
        if state.mastery_levels:
            mastery_items = []
            for topic, level in state.mastery_levels.items():
                mastery_items.append(f"- {topic}: {int(level * 100)}%")
            mastery_info = "Current Mastery Levels:\n" + "\n".join(mastery_items)

        return f"""You are an assessment agent for {state.framework.value} learning evaluation.

Current Context:
- Framework: {state.framework.value}
- Module: {state.module_id}
- Progress: {state.progress_percentage:.1f}%
- Topics Covered: {len(state.topics_covered)}
{curriculum_context}
{mastery_info}

Your role is to:
1. Create fair and comprehensive assessments
2. Design questions that test understanding, not memorization
3. Provide detailed feedback on responses
4. Identify knowledge gaps and areas for improvement
5. Suggest remediation strategies
6. Track and report progress accurately

Create assessments that are challenging but achievable, and always provide constructive feedback that helps learning.

When a user asks for a quiz or assessment:
1. Generate 3-5 questions on their current topic
2. Provide multiple choice options
3. Wait for their answers
4. Evaluate their responses and provide feedback
5. Recommend next steps based on performance

Respond as the assessment agent with fair, educational evaluations."""
    
    def should_activate(self, state: ConstellationState) -> bool:
        if not state.messages:
            return False
        
        latest_message = state.messages[-1]
        if isinstance(latest_message, HumanMessage):
            content_lower = latest_message.content.lower()
            assessment_keywords = ["test", "quiz", "assess", "evaluate", "check", "grade", "score", "knowledge", "progress"]
            return any(keyword in content_lower for keyword in assessment_keywords)
        
        return False
        
    async def generate_quiz_for_topic(self, state: ConstellationState, topic: str = None, difficulty: str = "adaptive") -> Dict[str, Any]:
        """
        Generate a quiz for the current topic or a specified topic.
        
        Args:
            state: Current constellation state
            topic: Optional topic override (uses current_topic if None)
            difficulty: Difficulty level
            
        Returns:
            Quiz data
        """
        # Determine topic based on curriculum if available
        if not topic:
            if "current_objective_description" in state.session_context:
                topic = state.session_context.get("current_objective_description")
            else:
                topic = state.current_topic or state.module_id
            
        # Determine appropriate difficulty based on user progress
        if difficulty == "adaptive":
            # Check if we have mastery data for this topic
            if topic in state.mastery_levels:
                mastery = state.mastery_levels[topic]
                if mastery < 0.3:
                    difficulty = "beginner"
                elif mastery < 0.7:
                    difficulty = "intermediate"
                else:
                    difficulty = "advanced"
            else:
                # Default to intermediate if no mastery data
                difficulty = "intermediate"
        
        # Get framework context for better questions
        framework_context = None
        try:
            # Import here to avoid circular imports
            from ..core.vector_storage import vector_storage
            
            # Get relevant framework knowledge
            results = await vector_storage.search_framework_knowledge(
                query=topic, 
                framework=state.framework
            )
            
            if results:
                framework_context = "\n".join([r.get("content", "") for r in results[:2]])
        except Exception as e:
            framework_context = None
                
        # Generate the quiz
        quiz_data = await self.quiz_generator.generate_quiz(
            topic=topic,
            difficulty=difficulty,
            framework=state.framework.value,
            context=framework_context
        )
        
        # Store in state context for later evaluation
        state.session_context["current_quiz"] = quiz_data
        
        return quiz_data
        
    async def evaluate_quiz_response(self, state: ConstellationState, user_answers: Dict[int, str]) -> Dict[str, Any]:
        """
        Evaluate user responses to the current quiz.
        
        Args:
            state: Current constellation state
            user_answers: Dictionary mapping question index to answer letter
            
        Returns:
            Evaluation results
        """
        # Get the current quiz from state
        quiz_data = state.session_context.get("current_quiz")
        if not quiz_data:
            return {
                "error": "No active quiz found. Please generate a quiz first."
            }
            
        # Evaluate the responses
        results = await self.quiz_generator.evaluate_response(quiz_data, user_answers)
        
        # Record the results in state
        topic = quiz_data.get("topic", state.current_topic or state.module_id)
        state.add_quiz_result(
            topic=topic,
            score=results["score"],
            questions=results["total_questions"],
            correct=results["correct_count"]
        )
        
        # Check if we should advance in the curriculum based on score
        if results["score"] >= 0.7:  # 70% or higher is passing
            # Add signal to advance curriculum
            state.adaptation_signals.append({
                "type": "quiz_passed",
                "topic": topic,
                "score": results["score"],
                "timestamp": datetime.now().isoformat()
            })
        else:
            # Add signal for remediation
            state.adaptation_signals.append({
                "type": "quiz_failed",
                "topic": topic,
                "score": results["score"],
                "timestamp": datetime.now().isoformat()
            })
            
        return results
        
    async def generate_curriculum_assessment(self, state: ConstellationState) -> Dict[str, Any]:
        """
        Generate a comprehensive assessment based on the current curriculum position.
        
        Args:
            state: Current constellation state
            
        Returns:
            Assessment data with questions covering current module topics
        """
        # Get current module topics from curriculum
        module_topics = state.session_context.get("current_module_topics", [])
        if not module_topics:
            # Fallback to current topic
            return await self.generate_quiz_for_topic(state)
            
        # Generate a comprehensive assessment covering multiple topics
        all_questions = []
        topic_count = min(len(module_topics), 3)  # Limit to 3 topics
        
        # Get framework context
        try:
            from ..core.vector_storage import vector_storage
            framework_results = await vector_storage.retrieve_framework_context(state.framework)
        except:
            framework_results = []
            
        # Extract relevant context
        framework_context = ""
        if framework_results:
            for result in framework_results[:3]:
                framework_context += result.get("content", "") + "\n\n"
        
        # Generate questions for each topic
        for i in range(topic_count):
            topic = module_topics[i]
            # Generate a mini quiz for this topic
            mini_quiz = await self.quiz_generator.generate_quiz(
                topic=topic,
                difficulty="adaptive",
                num_questions=2,  # 2 questions per topic
                framework=state.framework.value,
                context=framework_context
            )
            
            # Add topic information to each question
            for question in mini_quiz.get("questions", []):
                question["topic"] = topic
                all_questions.append(question)
                
        # Create the comprehensive assessment
        assessment = {
            "topic": f"{state.framework.value} - Module {state.current_module_index + 1} Assessment",
            "difficulty": "comprehensive",
            "questions": all_questions
        }
        
        # Store in state context
        state.session_context["current_quiz"] = assessment
        
        return assessment 