# GAAPF - Guidance AI Agent for Python Framework - CLI Guide

## Overview

The CLI (Command Line Interface) provides a real-time conversational experience with AI constellation agents using actual LLM API calls. No more mock responses - you get genuine AI assistance for learning Python frameworks!

## Features

- 🤖 **Real LLM Integration**: Actual API calls to Google Gemini, OpenAI GPT, or Anthropic Claude
- 🌟 **Intelligent Agent Selection**: Automatically chooses the best specialized agent for your question
- 🎯 **Personalized Learning**: Adapts to your skill level, pace, and learning style
- 💬 **Natural Conversation**: Chat naturally with AI agents about frameworks
- 📊 **Progress Tracking**: Monitors your learning progress and agent interactions
- 🔄 **Temporal Optimization**: Learns from interactions to improve future responses
- 🔍 **Internet Search**: Agents can search Google for the latest information and documentation
- 📁 **Auto File Creation**: Code examples are automatically saved as runnable files

## Quick Start

### 1. Setup API Keys

First, you need at least one LLM API key. Create a `.env` file in the project root:

```bash
# Copy the example file
cp env.example .env

# Edit .env and add your API key(s)
```

**Required: At least ONE of these API keys:**
- `GOOGLE_API_KEY` - For Google Gemini (Recommended - free tier available)
- `OPENAI_API_KEY` - For OpenAI GPT models
- `ANTHROPIC_API_KEY` - For Anthropic Claude models

**Optional: For Internet Search (Recommended):**
- `GOOGLE_CSE_ID` - For Google Custom Search Engine (enables internet search)

**Get your API keys:**
- **Google Gemini**: [Google AI Studio](https://aistudio.google.com/app/apikey) - Free tier available
- **Google Search**: [Google Cloud Console](https://console.cloud.google.com/apis/credentials) + [Programmable Search Engine](https://programmablesearchengine.google.com/controlpanel/create)
- **OpenAI**: [OpenAI Platform](https://platform.openai.com/api-keys) - Pay per use
- **Anthropic**: [Anthropic Console](https://console.anthropic.com/) - Pay per use

#### Setting up Google Search API (Detailed Steps)

To enable internet search functionality, you need both a Google API Key and a Custom Search Engine ID:

**Step 1: Get Google API Key**
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Navigate to "APIs & Services" > "Credentials"
4. Click "Create Credentials" > "API Key"
5. Copy the API key and add it to your `.env` file as `GOOGLE_API_KEY`

**Step 2: Enable Custom Search API**
1. In Google Cloud Console, go to "APIs & Services" > "Library"
2. Search for "Custom Search API"
3. Click on it and press "Enable"

**Step 3: Create Custom Search Engine**
1. Go to [Programmable Search Engine](https://programmablesearchengine.google.com/controlpanel/create)
2. Add `*.com` as the site to search (or specific sites you want to search)
3. Name your search engine (e.g., "PyFramework Assistant Search")
4. Click "Create"
5. In the control panel, click on your search engine
6. Go to "Setup" > "Basic" and copy the "Search engine ID"
7. Add this ID to your `.env` file as `GOOGLE_CSE_ID`

**Step 4: Configure Search Settings (Optional)**
1. In the search engine control panel, go to "Setup" > "Advanced"
2. Enable "Search the entire web" for broader search results
3. You can also add specific sites to prioritize (like python.langchain.com, docs.python.org, etc.)

### 2. Install Dependencies

Make sure you have all required packages:

```bash
pip install -r requirements.txt
```

### 3. Run the CLI

#### Option A: Direct Python execution
```bash
python src/pyframeworks_assistant/interfaces/cli/main.py
```

#### Option B: Using the convenience script
```bash
python run_cli.py
```

## Usage Guide

### Initial Setup

When you first run the CLI, you'll go through these steps:

1. **API Key Check**: The system verifies you have at least one LLM configured
2. **User Profile Setup**: Create your learning profile with:
   - Programming experience level
   - Python skill level
   - Learning pace preference
   - Learning style preference
   - Learning goals

3. **Framework Selection**: Choose which framework you want to learn:
   - LangChain
   - LangGraph
   - CrewAI
   - AutoGen
   - (More frameworks available)

4. **Constellation Recommendation**: The system recommends an optimal learning constellation based on your profile

### Conversation

Once setup is complete, you can:

- **Ask any question** about your selected framework
- **Request code examples** and implementations
- **Ask for explanations** at different complexity levels
- **Request practice exercises** and hands-on activities
- **Get troubleshooting help** when stuck
- **Ask for documentation references**

### Available Commands

| Command | Description |
|---------|-------------|
| `/help` | Show help and available commands |
| `/status` | Show current session status and progress |
| `/agents` | List all available AI agents and their usage |
| `/clear` | Clear conversation history |
| `/quit` or `/exit` | Exit the application |
| `Ctrl+C` | Quick exit |

### Specialized AI Agents

The system automatically selects the best agent for your question:

- **Instructor Agent**: For conceptual explanations and teaching
- **Code Assistant Agent**: For code examples and implementation help
- **Documentation Expert**: For API references and documentation
- **Practice Facilitator**: For exercises and hands-on learning
- **Assessment Agent**: For quizzes and knowledge evaluation
- **Mentor Agent**: For learning strategy and motivation
- **Research Assistant**: For advanced topics and latest information
- **Project Guide**: For building real applications
- **Troubleshooter**: For debugging and problem-solving
- **Motivational Coach**: For encouragement and learning tips
- **Knowledge Synthesizer**: For connecting concepts across topics
- **Progress Tracker**: For monitoring and reporting learning progress

## Example Session

```
🤖 GAAPF - Guidance AI Agent for Python Framework
   CLI Interface v1.0

✅ LLM Available: google_default, gemini-2.0-flash

👤 User Profile Setup
Programming experience in years (0-20): 3
Select your Python skill level (1-4): 2
Select your learning pace (1-3): 2
Select your learning style (1-4): 1

🔧 Framework Selection
Available frameworks:
  1. LangChain (v0.1.0) - Intermediate
  2. LangGraph (v0.0.45) - Advanced

Select a framework (1-2): 1

🌟 Recommended Learning Constellation:
   Knowledge Intensive
   
🚀 Learning session initialized!

💬 Learning Session Started

You: What is LangChain and why should I use it?

🤖 Instructor: LangChain is a powerful framework for building applications with Large Language Models (LLMs). Here's why it's valuable:

1. **Simplified LLM Integration**: LangChain provides easy-to-use abstractions for working with different LLM providers like OpenAI, Anthropic, and Google...

[Full AI response with real explanations]

Confidence: 0.92
💡 Suggested follow-up: Code Assistant

You: Can you show me a simple example?

🤖 Code Assistant: Absolutely! Here's a basic LangChain example to get you started:

```python
from langchain.llms import OpenAI
from langchain.prompts import PromptTemplate
from langchain.chains import LLMChain

# Initialize the LLM
llm = OpenAI(temperature=0.7)
...
```

You: /status

📊 Session Status:
• Framework: langchain
• Messages: 4
• Progress: 15.5%
• Active Agents: 2
```

## Troubleshooting

### No LLM Models Available
**Error**: "❌ No LLM models available. Please check your API keys in .env file."

**Solution**: 
1. Make sure you have a `.env` file in the project root
2. Add at least one valid API key
3. Test your API key is working

### Import Errors
**Error**: Import errors when running the CLI

**Solution**:
1. Make sure you're in the correct conda environment: `conda activate Ver5`
2. Install all dependencies: `pip install -r requirements.txt`
3. Check Python path and working directory

### Agent Errors
**Error**: "Error processing message"

**Solution**:
1. Check your internet connection
2. Verify your API key is valid and has credits/quota
3. Try rephrasing your question
4. Check the console for detailed error messages

## Performance Tips

1. **Use Specific Questions**: More specific questions get better agent selection and responses
2. **Context Matters**: The AI remembers conversation context, so build on previous questions
3. **Try Different Agents**: Use `/agents` to see which agents are available and their specializations
4. **Clear When Needed**: Use `/clear` to start fresh if the conversation gets off-track

## Advanced Features

### Temporal Optimization
The system learns from your interactions to:
- Recommend better learning constellations over time
- Optimize agent selection for your learning style
- Track effectiveness of different approaches

### Memory Management
- Conversation history is maintained throughout the session
- Context is preserved across agent handoffs
- Learning progress is tracked and can be resumed

### Multi-Agent Collaboration
- Agents can suggest handoffs to more specialized agents
- Smooth transitions between different types of assistance
- Confidence scores help you understand response quality

## Next Steps

1. **Try Different Frameworks**: Explore various Python frameworks
2. **Experiment with Questions**: Ask about concepts, code, debugging, best practices
3. **Use Commands**: Explore `/status`, `/agents`, and other commands
4. **Provide Feedback**: The system learns from your interactions

## Support

If you encounter issues:
1. Check this guide for troubleshooting steps
2. Review error messages in the console
3. Verify your API keys and environment setup
4. Try restarting with a fresh session

---

**Happy Learning! 🚀** 