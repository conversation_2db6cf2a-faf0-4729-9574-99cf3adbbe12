"""
Constellation Types and Enums.
Separated to avoid circular import issues between constellation and memory modules.
"""

from enum import Enum


class ConstellationType(str, Enum):
    """Types of agent constellations for different learning contexts."""
    KNOWLEDGE_INTENSIVE = "knowledge_intensive"
    HANDS_ON_FOCUSED = "hands_on_focused"
    THEORY_PRACTICE_BALANCED = "theory_practice_balanced"
    RESEARCH_INTENSIVE = "research_intensive"
    QUICK_LEARNING = "quick_learning"
    DEEP_EXPLORATION = "deep_exploration"
    PROJECT_ORIENTED = "project_oriented"
    ASSESSMENT_FOCUSED = "assessment_focused"
    # Additional constellation types
    BASIC_LEARNING = "basic_learning"
    GUIDED_LEARNING = "guided_learning"
    INTERACTIVE_LEARNING = "interactive_learning"
    PROJECT_BASED = "project_based"
    CHALLENGE_BASED = "challenge_based"


class AgentRole(str, Enum):
    """Specialized agent roles within a constellation."""
    INSTRUCTOR = "instructor"
    CODE_ASSISTANT = "code_assistant"
    DOCUMENTATION_EXPERT = "documentation_expert"
    PRACTICE_FACILITATOR = "practice_facilitator"
    ASSESSMENT_AGENT = "assessment_agent"
    MENTOR = "mentor"
    RESEARCH_ASSISTANT = "research_assistant"
    PROJECT_GUIDE = "project_guide"
    TROUBLESHOOTER = "troubleshooter"
    MOTIVATIONAL_COACH = "motivational_coach"
    KNOWLEDGE_SYNTHESIZER = "knowledge_synthesizer"
    PROGRESS_TRACKER = "progress_tracker"


class ConstellationPhase(str, Enum):
    """Learning phases in constellation lifecycle."""
    FORMATION = "formation"
    ACTIVE_LEARNING = "active_learning"
    HANDOFF = "handoff"
    ADAPTATION = "adaptation"
    COMPLETION = "completion" 