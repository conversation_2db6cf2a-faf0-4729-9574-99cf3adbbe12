"""
Constellation Memory Management for ELT Architecture.
Tracks constellation formation patterns, effectiveness metrics, and learning adaptations.
"""

from typing import Dict, List, Optional, Any, Set, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass
import json
import logging
from enum import Enum

from pydantic import BaseModel, Field

from ..core.constellation_types import ConstellationType, AgentRole, ConstellationPhase

logger = logging.getLogger(__name__)


# ConstellationPhase now imported from constellation_types


class ConstellationEffectivenessMetric(BaseModel):
    """Tracks effectiveness of constellation configurations."""
    constellation_type: ConstellationType = Field(description="Type of constellation")
    user_profile_hash: str = Field(description="Hash of user profile characteristics")
    framework: str = Field(description="Target learning framework")
    
    # Performance metrics
    learning_velocity: float = Field(description="Rate of concept mastery")
    engagement_score: float = Field(description="User engagement level")
    retention_rate: float = Field(description="Knowledge retention percentage")
    satisfaction_score: float = Field(description="User satisfaction rating")
    
    # Operational metrics
    formation_time: float = Field(description="Time to form constellation (seconds)")
    handoff_efficiency: float = Field(description="Agent handoff success rate")
    adaptation_frequency: int = Field(description="Number of adaptations needed")
    
    # Context
    session_duration: float = Field(description="Session length in minutes")
    topics_covered: int = Field(description="Number of topics covered")
    exercises_completed: int = Field(description="Number of exercises completed")
    
    timestamp: datetime = Field(default_factory=datetime.now)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class ConstellationFormationPattern(BaseModel):
    """Tracks successful constellation formation patterns."""
    pattern_id: str = Field(description="Unique pattern identifier")
    trigger_conditions: Dict[str, Any] = Field(description="Conditions that trigger this pattern")
    constellation_sequence: List[ConstellationType] = Field(description="Sequence of constellations")
    agent_configurations: Dict[str, List[AgentRole]] = Field(description="Agent configs per constellation")
    success_rate: float = Field(description="Success rate of this pattern")
    average_effectiveness: float = Field(description="Average effectiveness score")
    usage_count: int = Field(default=0, description="Number of times pattern was used")
    last_used: Optional[datetime] = Field(default=None, description="Last usage timestamp")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class ConstellationSession(BaseModel):
    """Individual constellation session record."""
    session_id: str = Field(description="Session identifier")
    user_id: str = Field(description="User identifier")
    start_time: datetime = Field(description="Session start time")
    end_time: Optional[datetime] = Field(default=None, description="Session end time")
    
    # Constellation evolution
    constellation_timeline: List[Dict[str, Any]] = Field(default_factory=list, description="Constellation changes over time")
    phase_transitions: List[Dict[str, Any]] = Field(default_factory=list, description="Phase transition records")
    agent_handoffs: List[Dict[str, Any]] = Field(default_factory=list, description="Agent handoff records")
    
    # Learning tracking
    initial_skill_level: Dict[str, float] = Field(default_factory=dict, description="Initial skill assessment")
    final_skill_level: Dict[str, float] = Field(default_factory=dict, description="Final skill assessment")
    learning_objectives: List[str] = Field(default_factory=list, description="Session objectives")
    achievements: List[str] = Field(default_factory=list, description="Achievements unlocked")
    
    # Memory integration
    short_term_interactions: List[str] = Field(default_factory=list, description="Key short-term interactions")
    long_term_insights: List[str] = Field(default_factory=list, description="Long-term learning insights")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class ConstellationMemoryManager:
    """Manages constellation-specific memory and learning patterns."""
    
    def __init__(self):
        """Initialize constellation memory manager."""
        self.effectiveness_history: Dict[str, List[ConstellationEffectivenessMetric]] = {}
        self.formation_patterns: Dict[str, ConstellationFormationPattern] = {}
        self.active_sessions: Dict[str, ConstellationSession] = {}
        self.user_constellation_preferences: Dict[str, Dict[str, Any]] = {}
        
        # Learning analytics
        self.optimal_configurations: Dict[str, ConstellationType] = {}
        self.adaptation_triggers: Dict[str, List[Callable]] = {}
        
    def start_constellation_session(
        self,
        session_id: str,
        user_id: str,
        initial_constellation: ConstellationType,
        learning_objectives: List[str]
    ) -> ConstellationSession:
        """Start tracking a new constellation session."""
        session = ConstellationSession(
            session_id=session_id,
            user_id=user_id,
            start_time=datetime.now(),
            learning_objectives=learning_objectives
        )
        
        # Record initial constellation
        session.constellation_timeline.append({
            "timestamp": datetime.now().isoformat(),
            "constellation_type": initial_constellation.value,
            "phase": ConstellationPhase.FORMATION.value,
            "trigger": "session_start"
        })
        
        self.active_sessions[session_id] = session
        return session
    
    def record_constellation_change(
        self,
        session_id: str,
        new_constellation: ConstellationType,
        phase: ConstellationPhase,
        trigger_reason: str,
        effectiveness_score: float
    ) -> None:
        """Record a constellation change event."""
        if session_id not in self.active_sessions:
            logger.warning(f"Session {session_id} not found")
            return
        
        session = self.active_sessions[session_id]
        
        # Record the change
        change_record = {
            "timestamp": datetime.now().isoformat(),
            "constellation_type": new_constellation.value,
            "phase": phase.value,
            "trigger": trigger_reason,
            "effectiveness_score": effectiveness_score
        }
        
        session.constellation_timeline.append(change_record)
        
        # Update phase transitions
        session.phase_transitions.append({
            "timestamp": datetime.now().isoformat(),
            "from_phase": session.constellation_timeline[-2]["phase"] if len(session.constellation_timeline) > 1 else "none",
            "to_phase": phase.value,
            "effectiveness_improvement": self._calculate_effectiveness_improvement(session)
        })
    
    def record_agent_handoff(
        self,
        session_id: str,
        from_agent: AgentRole,
        to_agent: AgentRole,
        handoff_reason: str,
        handoff_success: bool
    ) -> None:
        """Record an agent handoff event."""
        if session_id not in self.active_sessions:
            return
        
        session = self.active_sessions[session_id]
        handoff_record = {
            "timestamp": datetime.now().isoformat(),
            "from_agent": from_agent.value,
            "to_agent": to_agent.value,
            "reason": handoff_reason,
            "success": handoff_success
        }
        
        session.agent_handoffs.append(handoff_record)
    
    def record_effectiveness_metric(
        self,
        session_id: str,
        metric: ConstellationEffectivenessMetric
    ) -> None:
        """Record constellation effectiveness metrics."""
        user_id = self.active_sessions.get(session_id, {}).user_id if session_id in self.active_sessions else "unknown"
        
        if user_id not in self.effectiveness_history:
            self.effectiveness_history[user_id] = []
        
        self.effectiveness_history[user_id].append(metric)
        
        # Update optimal configurations
        self._update_optimal_configurations(metric)
    
    def get_optimal_constellation(
        self,
        user_profile_hash: str,
        framework: str,
        current_context: Dict[str, Any]
    ) -> ConstellationType:
        """Get optimal constellation type based on historical effectiveness."""
        
        # Check user-specific patterns first
        user_metrics = []
        for user_id, metrics in self.effectiveness_history.items():
            user_hash = self._generate_profile_hash(user_id)  # Simplified
            if user_hash == user_profile_hash:
                user_metrics.extend([m for m in metrics if m.framework == framework])
        
        if user_metrics:
            # Find highest performing constellation for this user and framework
            best_metric = max(user_metrics, key=lambda m: m.learning_velocity * m.engagement_score)
            return best_metric.constellation_type
        
        # Fall back to general patterns
        framework_metrics = []
        for metrics in self.effectiveness_history.values():
            framework_metrics.extend([m for m in metrics if m.framework == framework])
        
        if framework_metrics:
            # Group by constellation type and calculate average effectiveness
            constellation_scores = {}
            for metric in framework_metrics:
                if metric.constellation_type not in constellation_scores:
                    constellation_scores[metric.constellation_type] = []
                
                effectiveness = (
                    metric.learning_velocity * 0.3 +
                    metric.engagement_score * 0.25 +
                    metric.retention_rate * 0.25 +
                    metric.satisfaction_score * 0.2
                )
                constellation_scores[metric.constellation_type].append(effectiveness)
            
            # Calculate average scores
            avg_scores = {
                const_type: sum(scores) / len(scores)
                for const_type, scores in constellation_scores.items()
            }
            
            best_constellation = max(avg_scores.keys(), key=lambda k: avg_scores[k])
            return best_constellation
        
        # Default fallback
        return ConstellationType.THEORY_PRACTICE_BALANCED
    
    def get_formation_pattern(
        self,
        trigger_conditions: Dict[str, Any]
    ) -> Optional[ConstellationFormationPattern]:
        """Get the most suitable formation pattern for given conditions."""
        
        best_pattern = None
        best_score = 0.0
        
        for pattern in self.formation_patterns.values():
            score = self._calculate_pattern_match_score(pattern.trigger_conditions, trigger_conditions)
            if score > best_score and score > 0.7:  # Minimum match threshold
                best_score = score
                best_pattern = pattern
        
        return best_pattern
    
    def end_constellation_session(
        self,
        session_id: str,
        final_skill_assessment: Dict[str, float],
        achievements: List[str]
    ) -> Optional[ConstellationSession]:
        """End constellation session and compute final metrics."""
        if session_id not in self.active_sessions:
            return None
        
        session = self.active_sessions[session_id]
        session.end_time = datetime.now()
        session.final_skill_level = final_skill_assessment
        session.achievements = achievements
        
        # Compute session-level insights
        self._generate_session_insights(session)
        
        # Update formation patterns
        self._update_formation_patterns(session)
        
        completed_session = session
        del self.active_sessions[session_id]
        
        return completed_session
    
    def get_user_constellation_analytics(self, user_id: str) -> Dict[str, Any]:
        """Get comprehensive constellation analytics for a user."""
        user_metrics = self.effectiveness_history.get(user_id, [])
        
        if not user_metrics:
            return {"total_sessions": 0, "message": "No constellation data available"}
        
        # Calculate analytics
        constellation_usage = {}
        total_effectiveness = []
        framework_performance = {}
        
        for metric in user_metrics:
            # Constellation usage
            if metric.constellation_type not in constellation_usage:
                constellation_usage[metric.constellation_type] = 0
            constellation_usage[metric.constellation_type] += 1
            
            # Overall effectiveness
            effectiveness = (
                metric.learning_velocity * 0.3 +
                metric.engagement_score * 0.25 +
                metric.retention_rate * 0.25 +
                metric.satisfaction_score * 0.2
            )
            total_effectiveness.append(effectiveness)
            
            # Framework performance
            if metric.framework not in framework_performance:
                framework_performance[metric.framework] = []
            framework_performance[metric.framework].append(effectiveness)
        
        # Most effective constellation
        constellation_effectiveness = {}
        for metric in user_metrics:
            if metric.constellation_type not in constellation_effectiveness:
                constellation_effectiveness[metric.constellation_type] = []
            
            effectiveness = (
                metric.learning_velocity * 0.3 +
                metric.engagement_score * 0.25 +
                metric.retention_rate * 0.25 +
                metric.satisfaction_score * 0.2
            )
            constellation_effectiveness[metric.constellation_type].append(effectiveness)
        
        avg_constellation_effectiveness = {
            const_type: sum(scores) / len(scores)
            for const_type, scores in constellation_effectiveness.items()
        }
        
        most_effective = max(avg_constellation_effectiveness.keys(), 
                           key=lambda k: avg_constellation_effectiveness[k])
        
        return {
            "total_sessions": len(user_metrics),
            "average_effectiveness": sum(total_effectiveness) / len(total_effectiveness),
            "constellation_usage": dict(constellation_usage),
            "most_effective_constellation": most_effective.value,
            "constellation_effectiveness": {k.value: v for k, v in avg_constellation_effectiveness.items()},
            "framework_performance": {
                framework: sum(scores) / len(scores)
                for framework, scores in framework_performance.items()
            },
            "learning_velocity_trend": [m.learning_velocity for m in user_metrics[-10:]],  # Last 10 sessions
            "engagement_trend": [m.engagement_score for m in user_metrics[-10:]]
        }
    
    def _calculate_effectiveness_improvement(self, session: ConstellationSession) -> float:
        """Calculate effectiveness improvement over session."""
        if len(session.constellation_timeline) < 2:
            return 0.0
        
        # Simple improvement calculation based on phase transitions
        recent_scores = [change.get("effectiveness_score", 0.5) 
                        for change in session.constellation_timeline[-3:]]
        
        if len(recent_scores) >= 2:
            return recent_scores[-1] - recent_scores[0]
        
        return 0.0
    
    def _update_optimal_configurations(self, metric: ConstellationEffectivenessMetric) -> None:
        """Update optimal configuration recommendations."""
        key = f"{metric.user_profile_hash}:{metric.framework}"
        
        effectiveness = (
            metric.learning_velocity * 0.3 +
            metric.engagement_score * 0.25 +
            metric.retention_rate * 0.25 +
            metric.satisfaction_score * 0.2
        )
        
        current_best = self.optimal_configurations.get(key)
        if not current_best or effectiveness > 0.8:  # High effectiveness threshold
            self.optimal_configurations[key] = metric.constellation_type
    
    def _calculate_pattern_match_score(
        self, 
        pattern_conditions: Dict[str, Any], 
        current_conditions: Dict[str, Any]
    ) -> float:
        """Calculate how well a pattern matches current conditions."""
        total_weight = 0.0
        matched_weight = 0.0
        
        for key, pattern_value in pattern_conditions.items():
            weight = 1.0  # Could be made configurable
            total_weight += weight
            
            if key in current_conditions:
                current_value = current_conditions[key]
                
                if isinstance(pattern_value, str) and isinstance(current_value, str):
                    if pattern_value.lower() == current_value.lower():
                        matched_weight += weight
                elif isinstance(pattern_value, (int, float)) and isinstance(current_value, (int, float)):
                    # Numeric similarity
                    similarity = 1.0 - abs(pattern_value - current_value) / max(abs(pattern_value), abs(current_value), 1)
                    matched_weight += weight * max(0, similarity)
                elif pattern_value == current_value:
                    matched_weight += weight
        
        return matched_weight / total_weight if total_weight > 0 else 0.0
    
    def _generate_profile_hash(self, user_id: str) -> str:
        """Generate a simplified profile hash."""
        # In a real implementation, this would hash user profile characteristics
        return f"hash_{user_id}"
    
    def _generate_session_insights(self, session: ConstellationSession) -> None:
        """Generate insights from completed session."""
        # Short-term insights
        if len(session.constellation_timeline) > 1:
            session.short_term_interactions.append(
                f"Used {len(set(change['constellation_type'] for change in session.constellation_timeline))} different constellations"
            )
        
        if session.agent_handoffs:
            successful_handoffs = sum(1 for handoff in session.agent_handoffs if handoff.get("success", False))
            session.short_term_interactions.append(
                f"Achieved {successful_handoffs}/{len(session.agent_handoffs)} successful agent handoffs"
            )
        
        # Long-term insights
        if session.final_skill_level and session.initial_skill_level:
            skill_improvements = []
            for skill, final_level in session.final_skill_level.items():
                initial_level = session.initial_skill_level.get(skill, 0.0)
                if final_level > initial_level:
                    improvement = final_level - initial_level
                    skill_improvements.append(f"{skill}: +{improvement:.2f}")
            
            if skill_improvements:
                session.long_term_insights.append(f"Skill improvements: {', '.join(skill_improvements)}")
    
    def _update_formation_patterns(self, session: ConstellationSession) -> None:
        """Update formation patterns based on session success."""
        if len(session.constellation_timeline) < 2:
            return
        
        # Extract pattern from session
        constellation_sequence = [
            ConstellationType(change["constellation_type"]) 
            for change in session.constellation_timeline
        ]
        
        # Create pattern key
        pattern_key = "_".join([const.value for const in constellation_sequence])
        
        if pattern_key not in self.formation_patterns:
            # Create new pattern
            self.formation_patterns[pattern_key] = ConstellationFormationPattern(
                pattern_id=pattern_key,
                trigger_conditions={},  # Would be populated from session context
                constellation_sequence=constellation_sequence,
                agent_configurations={},  # Would be populated from agent usage
                success_rate=1.0,
                average_effectiveness=0.8,  # Placeholder
                usage_count=1,
                last_used=datetime.now()
            )
        else:
            # Update existing pattern
            pattern = self.formation_patterns[pattern_key]
            pattern.usage_count += 1
            pattern.last_used = datetime.now()
            # Update success rate and effectiveness based on session outcomes


# Global constellation memory manager instance
constellation_memory_manager = ConstellationMemoryManager()


def get_constellation_memory() -> ConstellationMemoryManager:
    """Get the global constellation memory manager."""
    return constellation_memory_manager 