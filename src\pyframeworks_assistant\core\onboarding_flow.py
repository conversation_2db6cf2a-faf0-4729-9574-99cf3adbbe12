"""
Onboarding Graph - Systematic user onboarding flow for PyFrameworks Assistant.
Handles framework selection, framework discovery, curriculum generation, and user profile setup.
"""

import asyncio
import logging
import re
import json
from typing import Dict, List, Any, Optional, Union, Callable
from datetime import datetime
from enum import Enum
from pydantic import BaseModel, Field
from langgraph.graph import StateGraph, END

from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, SystemMessage
from langchain_core.language_models.chat_models import BaseChatModel

from ..config.user_profiles import UserProfile, SkillLevel, LearningSession
from ..config.framework_configs import SupportedFrameworks
from ..core.framework_initializer import <PERSON><PERSON>FrameworkInitializer, FrameworkContext, PracticeExample
from ..core.adaptive_learning_engine import AdaptiveLearningEngine, LearningObjective
from ..core.models import llm_manager
from ..tools.search_tools import TavilySearchTool, TavilyExtractTool, TavilyCrawlTool
from ..memory.persistent_storage import get_storage_manager

logger = logging.getLogger(__name__)


class OnboardingStage(str, Enum):
    """Stages of the onboarding process."""
    WELCOME = "welcome"
    USER_PROFILE = "user_profile"
    FRAMEWORK_SELECTION = "framework_selection"
    FRAMEWORK_DISCOVERY = "framework_discovery"
    LEARNING_GOALS = "learning_goals"
    CURRICULUM_GENERATION = "curriculum_generation"
    COMPLETE = "complete"


class OnboardingState(BaseModel):
    """State maintained during the onboarding process."""
    user_id: str = Field(description="User identifier")
    session_id: str = Field(description="Session identifier")
    current_stage: OnboardingStage = Field(default=OnboardingStage.WELCOME, description="Current stage of onboarding")
    completed_stages: List[OnboardingStage] = Field(default_factory=list, description="Completed stages")
    
    # Messages
    messages: List[BaseMessage] = Field(default_factory=list, description="Conversation history")
    
    # User profile information
    name: Optional[str] = Field(default=None, description="User's name")
    skill_level: Optional[SkillLevel] = Field(default=None, description="User's skill level")
    learning_style_preferences: List[str] = Field(default_factory=list, description="User's learning style preferences")
    time_availability: Optional[int] = Field(default=None, description="User's weekly time availability in hours")
    
    # Framework information
    selected_framework: Optional[SupportedFrameworks] = Field(default=None, description="Selected framework")
    framework_interests: List[str] = Field(default_factory=list, description="Frameworks of interest")
    framework_context: Optional[Dict[str, Any]] = Field(default=None, description="Framework context information")
    
    # Learning goals
    learning_goals: List[str] = Field(default_factory=list, description="User's learning goals")
    learning_objectives: List[LearningObjective] = Field(default_factory=list, description="Generated learning objectives")
    
    # Metadata
    tool_results: Dict[str, Any] = Field(default_factory=dict, description="Tool execution results")
    
    def add_message(self, message: BaseMessage) -> None:
        """Add a message to the conversation history."""
        self.messages.append(message)
    
    def complete_stage(self, stage: OnboardingStage) -> None:
        """Mark a stage as completed."""
        if stage not in self.completed_stages:
            self.completed_stages.append(stage)


class OnboardingGraph:
    """LangGraph-based onboarding flow for user configuration and framework selection."""
    
    def __init__(self):
        """Initialize the onboarding graph."""
        self.framework_initializer = DynamicFrameworkInitializer()
        self.learning_engine = AdaptiveLearningEngine()
        self.model = llm_manager.get_default_model()
        self.search_tool = TavilySearchTool()
        self.extract_tool = TavilyExtractTool()
        self.crawl_tool = TavilyCrawlTool()
        self.graph = self._create_onboarding_graph()
        
    def _create_onboarding_graph(self) -> StateGraph:
        """Create the onboarding graph for user configuration."""
        # Define the onboarding workflow as a state graph
        workflow = StateGraph(OnboardingState)
        
        # Define the nodes in the graph using string values that don't conflict with state fields
        workflow.add_node("welcome_node", self.welcome_node)
        workflow.add_node("profile_node", self.user_profile_node)
        workflow.add_node("framework_node", self.framework_selection_node)
        workflow.add_node("discovery_node", self.framework_discovery_node)
        workflow.add_node("goals_node", self.learning_goals_node)
        workflow.add_node("curriculum_node", self.curriculum_generation_node)
        workflow.add_node("complete_node", self.completion_node)
        
        # Define the edges in the graph
        workflow.add_edge("welcome_node", "profile_node")
        workflow.add_edge("profile_node", "framework_node")
        workflow.add_edge("framework_node", "discovery_node")
        workflow.add_edge("discovery_node", "goals_node")
        workflow.add_edge("goals_node", "curriculum_node")
        workflow.add_edge("curriculum_node", "complete_node")
        workflow.add_edge("complete_node", END)
        
        # Set the entry point
        workflow.set_entry_point("welcome_node")
        
        return workflow
        
    async def start_onboarding(self, user_id: str, session_id: str, message: str) -> Dict[str, Any]:
        """Start the onboarding process for a user."""
        # Check if we have an existing state for this session
        storage = get_storage_manager()
        existing_state = None
        
        try:
            # Try to load existing onboarding state from storage
            if hasattr(storage, 'cache') and storage.cache.available:
                state_data = storage.cache.get(f"onboarding_state_{session_id}")
                if state_data:
                    # Deserialize the state
                    state_dict = json.loads(state_data)
                    existing_state = OnboardingState(**state_dict)
                    logger.info(f"Loaded existing onboarding state for session {session_id}")
        except Exception as e:
            logger.error(f"Error loading onboarding state: {e}")
        
        # Initialize the state or use existing
        if existing_state:
            # Add the new message to existing state
            existing_state.add_message(HumanMessage(content=message))
            initial_state = existing_state
        else:
            # Create new state
            initial_state = OnboardingState(
                user_id=user_id,
                session_id=session_id
            )
            initial_state.add_message(HumanMessage(content=message))
        
        # Invoke the workflow
        try:
            # Map enum values to node names
            stage_to_node = {
                OnboardingStage.WELCOME: "welcome_node",
                OnboardingStage.USER_PROFILE: "profile_node",
                OnboardingStage.FRAMEWORK_SELECTION: "framework_node",
                OnboardingStage.FRAMEWORK_DISCOVERY: "discovery_node",
                OnboardingStage.LEARNING_GOALS: "goals_node",
                OnboardingStage.CURRICULUM_GENERATION: "curriculum_node",
                OnboardingStage.COMPLETE: "complete_node"
            }
            
            # Run the onboarding flow with continuation
            # If we have an existing state, we'll continue from there
            entry_node = stage_to_node.get(initial_state.current_stage, "welcome_node")
            # Compile the graph first
            compiled_graph = self.graph.compile()
            
            # Use the correct async invoke method
            result = await compiled_graph.ainvoke(
                initial_state,
                config={"recursion_limit": 20}
            )
            
            final_state = result
            
            # Save the state for future continuation
            try:
                if hasattr(storage, 'cache') and storage.cache.available:
                    # Convert state to dict and serialize
                    state_dict = final_state.dict()
                    # Convert any non-serializable objects
                    state_dict["messages"] = [
                        {"type": type(msg).__name__, "content": msg.content}
                        for msg in final_state.messages
                    ]
                    state_json = json.dumps(state_dict)
                    storage.cache.set(f"onboarding_state_{session_id}", state_json, expire=3600)  # 1 hour expiry
            except Exception as e:
                logger.error(f"Error saving onboarding state: {e}")
            
            # Create a user profile from the onboarding state if complete
            if OnboardingStage.COMPLETE in final_state.completed_stages:
                user_profile = self._create_user_profile(final_state)
                
                # Clear the state from cache when complete
                if hasattr(storage, 'cache') and storage.cache.available:
                    storage.cache.delete(f"onboarding_state_{session_id}")
                
                # Return the completed onboarding results
                return {
                    "user_profile": user_profile,
                    "framework": final_state.selected_framework,
                    "learning_objectives": final_state.learning_objectives,
                    "messages": final_state.messages,
                    "tool_results": final_state.tool_results,
                    "success": True,
                    "onboarding_complete": True
                }
            else:
                # Return the in-progress state
                return {
                    "framework": final_state.selected_framework,
                    "current_stage": final_state.current_stage,
                    "completed_stages": final_state.completed_stages,
                    "messages": final_state.messages,
                    "success": True,
                    "onboarding_complete": False
                }
            
        except Exception as e:
            logger.error(f"Error in onboarding flow: {e}")
            return {
                "error": str(e),
                "success": False,
                "onboarding_complete": False
            }
    
    async def welcome_node(self, state: OnboardingState) -> OnboardingState:
        """Welcome the user and explain the onboarding process."""
        # Generate welcome message
        welcome_prompt = """You are the PyFrameworks Onboarding Assistant. Your task is to welcome the user to the 
        PyFrameworks Assistant and guide them through the onboarding process.

        Introduce yourself briefly and explain that you'll help them set up their learning profile.
        Keep it friendly, concise, and focused on getting started.
        
        Your response should be structured as:
        1. A brief welcome
        2. An explanation that you'll help them set up their profile
        3. A question about their name and experience with Python frameworks
        """
        
        # Generate response
        conversation = [
            SystemMessage(content=welcome_prompt),
            *state.messages[-1:]  # Only include the latest message
        ]
        
        response = await self.model.ainvoke(conversation)
        state.add_message(AIMessage(content=response.content))
        
        # Update state
        state.current_stage = OnboardingStage.USER_PROFILE
        state.complete_stage(OnboardingStage.WELCOME)
        
        return state
    
    async def user_profile_node(self, state: OnboardingState) -> OnboardingState:
        """Gather information about the user's profile."""
        # Get the latest message
        user_messages = [msg for msg in state.messages if isinstance(msg, HumanMessage)]
        if not user_messages:
            return state
        
        latest_message = user_messages[-1]
        
        # Generate prompt for user profile creation
        profile_prompt = """You are the PyFrameworks Onboarding Assistant. Your task is to gather information about 
        the user to create their learning profile.

        Based on their response, extract the following information:
        1. Their name (if provided)
        2. Their experience level with Python frameworks (beginner, intermediate, advanced, expert)
        3. Their learning style preferences (visual, hands-on, reading, etc.)
        4. Their time availability for learning (hours per week)
        
        If any information is missing, ask follow-up questions to gather it. Keep your response conversational and friendly.
        Once you have all the information, summarize what you've learned about them and ask them about which framework they are interested in learning.
        """
        
        # Generate response
        conversation = [
            SystemMessage(content=profile_prompt),
            *state.messages[-4:]  # Include a few recent messages for context
        ]
        
        response = await self.model.ainvoke(conversation)
        state.add_message(AIMessage(content=response.content))
        
        # Extract profile information from the conversation
        message_text = latest_message.content.lower()
        
        # Extract name
        if not state.name and "name" in message_text and "my name is" in message_text:
            name_parts = message_text.split("my name is")[1].strip().split()
            if name_parts:
                state.name = name_parts[0].title()
        
        # Extract skill level
        if not state.skill_level:
            if "beginner" in message_text:
                state.skill_level = SkillLevel.BEGINNER
            elif "intermediate" in message_text:
                state.skill_level = SkillLevel.INTERMEDIATE
            elif "advanced" in message_text:
                state.skill_level = SkillLevel.ADVANCED
            elif "expert" in message_text:
                state.skill_level = SkillLevel.EXPERT
        
        # Extract learning style preferences
        learning_styles = ["visual", "hands-on", "reading", "watching", "coding", "project-based", "interactive"]
        for style in learning_styles:
            if style in message_text and style not in state.learning_style_preferences:
                state.learning_style_preferences.append(style)
        
        # Extract time availability
        time_indicators = ["hours per week", "hours a week", "hr/week", "hours/week"]
        for indicator in time_indicators:
            if indicator in message_text:
                parts = message_text.split(indicator)[0].strip().split()
                for part in reversed(parts):
                    if part.isdigit():
                        state.time_availability = int(part)
                        break
        
        # Check if we have enough information to move on
        if state.skill_level and (state.name or "name" not in message_text):
            state.current_stage = OnboardingStage.FRAMEWORK_SELECTION
            state.complete_stage(OnboardingStage.USER_PROFILE)
        
        return state
    
    async def framework_selection_node(self, state: OnboardingState) -> OnboardingState:
        """Guide the user through framework selection."""
        # Get the latest message
        user_messages = [msg for msg in state.messages if isinstance(msg, HumanMessage)]
        if not user_messages:
            return state
        
        latest_message = user_messages[-1].content.lower()
        
        # Generate prompt for framework selection
        framework_prompt = """You are the PyFrameworks Onboarding Assistant. Your task is to help the user select 
        a Python AI framework to learn.

        The available frameworks are:
        1. LangChain - A framework for building applications with Large Language Models (LLMs)
        2. LangGraph - A library for building stateful, multi-actor applications with LLMs
        3. CrewAI - A framework for orchestrating role-based AI agents
        4. AutoGen - A framework for building multi-agent systems with LLMs
        
        Based on their response and needs, suggest an appropriate framework. If they've already mentioned a specific framework,
        confirm their choice and briefly explain why it's a good fit. If they haven't specified one, ask them about their goals
        and suggest a suitable framework.
        
        Once you have their framework selection, confirm it and prepare them for the next step: exploring the framework.
        """
        
        # Generate response
        conversation = [
            SystemMessage(content=framework_prompt),
            *state.messages[-4:]  # Include a few recent messages for context
        ]
        
        response = await self.model.ainvoke(conversation)
        state.add_message(AIMessage(content=response.content))
        
        # Extract framework selection from the latest message
        frameworks = {
            "langchain": SupportedFrameworks.LANGCHAIN,
            "langgraph": SupportedFrameworks.LANGGRAPH,
            "crewai": SupportedFrameworks.CREWAI,
            "autogen": SupportedFrameworks.AUTOGEN,
        }
        
        # Check for framework mentions in the user's message
        for framework_name, framework_enum in frameworks.items():
            if framework_name in latest_message:
                state.selected_framework = framework_enum
                if framework_name not in state.framework_interests:
                    state.framework_interests.append(framework_name)
                break
        
        # If no specific framework was selected, check if they mentioned any frameworks
        if not state.selected_framework:
            for framework_name in frameworks.keys():
                if framework_name in latest_message and framework_name not in state.framework_interests:
                    state.framework_interests.append(framework_name)
        
        # Move to next stage if a framework was selected
        if state.selected_framework:
            state.current_stage = OnboardingStage.FRAMEWORK_DISCOVERY
            state.complete_stage(OnboardingStage.FRAMEWORK_SELECTION)
        
        return state
    
    async def framework_discovery_node(self, state: OnboardingState) -> OnboardingState:
        """Discover and explore the selected framework using Tavily tools."""
        # Only proceed if a framework is selected
        if not state.selected_framework:
            return state
        
        try:
            # Use Tavily Search to find information about the framework
            framework_name = state.selected_framework.value
            search_query = f"{framework_name} Python framework overview latest documentation features 2024"
            
            # Execute the search tool directly
            search_results = await asyncio.to_thread(self.search_tool._run, search_query)
            state.tool_results["framework_search"] = search_results
            
            # Extract documentation URLs
            url_pattern = r'https?://[^\s]+'
            urls = re.findall(url_pattern, search_results)
            
            docs_urls = [url for url in urls if "docs" in url or "documentation" in url or "github" in url]
            extract_results = ""
            
            if docs_urls:
                # Execute the extract tool directly
                extract_results = await asyncio.to_thread(self.extract_tool._run, docs_urls[0])
                state.tool_results["framework_extract"] = extract_results
                
                # Execute the crawl tool for more comprehensive information
                crawl_results = await asyncio.to_thread(self.crawl_tool._run, docs_urls[0])
                state.tool_results["framework_crawl"] = crawl_results
            
            # Initialize the framework context with the gathered information
            framework_context = await self.framework_initializer.initialize_framework_context(state.selected_framework)
            
            # Enrich the framework context with the newly discovered information
            if extract_results:
                # Update the framework context with the extracted information
                # This will be used to create a more personalized curriculum
                enhanced_context = await self._enhance_framework_context(
                    framework_context, 
                    search_results, 
                    extract_results, 
                    state.tool_results.get("framework_crawl", "")
                )
                
                # Persist the enhanced context to vector database
                await self.framework_initializer.persist_framework_context(
                    state.selected_framework, 
                    enhanced_context
                )
                
                # Update the state's framework context
                framework_context = enhanced_context
            
            # Store the framework context in the state
            state.framework_context = {
                "description": framework_context.description,
                "key_concepts": framework_context.key_concepts,
                "version": framework_context.version,
                "learning_path": framework_context.learning_path,
                "common_use_cases": framework_context.common_use_cases,
                "resources": framework_context.resources
            }
            
            # Generate a response about the framework discovery
            discovery_prompt = f"""You are the PyFrameworks Onboarding Assistant. You've just gathered information about the {framework_name} framework for the user.

            Based on the information gathered, provide the user with:
            1. A brief overview of {framework_name} (2-3 sentences)
            2. The key concepts they'll learn (list 3-5 bullet points)
            3. Common use cases for this framework (list 2-3 examples)
            
            Then, ask them about their specific learning goals with this framework. What do they want to be able to build or accomplish?
            Keep your response conversational, engaging, and focused on the framework's relevance to them.
            """
            
            conversation = [
                SystemMessage(content=discovery_prompt),
                *state.messages[-2:]  # Include a couple of recent messages for context
            ]
            
            response = await self.model.ainvoke(conversation)
            state.add_message(AIMessage(content=response.content))
            
            # Move to next stage
            state.current_stage = OnboardingStage.LEARNING_GOALS
            state.complete_stage(OnboardingStage.FRAMEWORK_DISCOVERY)
            
        except Exception as e:
            logger.error(f"Error in framework discovery: {e}")
            state.add_message(AIMessage(content=f"I encountered an issue while gathering information about {state.selected_framework.value}. Let's continue with what we know so far. What specific learning goals do you have for this framework?"))
            state.current_stage = OnboardingStage.LEARNING_GOALS
            state.complete_stage(OnboardingStage.FRAMEWORK_DISCOVERY)
        
        return state
    
    async def _enhance_framework_context(
        self, 
        base_context: FrameworkContext, 
        search_results: str, 
        extract_results: str,
        crawl_results: str
    ) -> FrameworkContext:
        """
        Enhance the framework context with additional information from search results.
        
        Args:
            base_context: The base framework context to enhance
            search_results: Results from the search tool
            extract_results: Results from the extract tool
            crawl_results: Results from the crawl tool
            
        Returns:
            Enhanced framework context
        """
        # Extract key concepts from search results
        key_concepts = base_context.key_concepts.copy()
        for line in search_results.split('\n'):
            if any(keyword in line.lower() for keyword in ['feature', 'capability', 'support', 'key']):
                if len(line) > 15 and len(line) < 200 and not any(concept in line for concept in key_concepts):
                    key_concepts.append(line.strip())
        
        # Extract code examples from extract and crawl results
        code_examples = []
        in_code_block = False
        code_block = []
        
        for line in extract_results.split('\n') + crawl_results.split('\n'):
            if '```python' in line:
                in_code_block = True
                code_block = []
            elif '```' in line and in_code_block:
                in_code_block = False
                if code_block:
                    code_examples.append('\n'.join(code_block))
            elif in_code_block:
                code_block.append(line)
        
        # Create enhanced practice examples
        practice_examples = base_context.practice_examples.copy()
        for i, code in enumerate(code_examples[:3]):  # Limit to 3 examples
            if len(code) > 30:  # Only include substantial examples
                practice_examples.append(PracticeExample(
                    title=f"Example from documentation {i+1}",
                    description="Example extracted from framework documentation",
                    difficulty_level="intermediate",
                    code_example=code,
                    explanation="This example demonstrates typical usage of the framework",
                    concepts_demonstrated=key_concepts[:3],
                    next_steps=["Modify the example", "Integrate with your project"],
                    source_urls=[]
                ))
        
        # Create enhanced context
        from copy import deepcopy
        enhanced_context = deepcopy(base_context)
        enhanced_context.key_concepts = key_concepts[:10]  # Limit to 10 key concepts
        enhanced_context.practice_examples = practice_examples
        enhanced_context.source_urls = base_context.source_urls + re.findall(r'https?://[^\s]+', search_results)
        
        return enhanced_context
    
    async def learning_goals_node(self, state: OnboardingState) -> OnboardingState:
        """Identify the user's learning goals for the selected framework."""
        # Get the latest message
        user_messages = [msg for msg in state.messages if isinstance(msg, HumanMessage)]
        if not user_messages:
            return state
        
        latest_message = user_messages[-1].content
        
        # Generate prompt for learning goals
        goals_prompt = f"""You are the PyFrameworks Onboarding Assistant. Your task is to help the user articulate their learning goals for {state.selected_framework.value}.

        Based on their response, extract specific learning goals they've mentioned. If they haven't provided clear goals,
        guide them with some suggestions based on their skill level and the framework's capabilities.
        
        For a {state.skill_level.value if state.skill_level else 'beginner'} user learning {state.selected_framework.value}, appropriate goals might include:
        - Building a simple application that uses {state.selected_framework.value}
        - Understanding the core concepts and architecture of the framework
        - Learning how to integrate {state.selected_framework.value} with other technologies
        - Creating a specific type of AI application with the framework
        
        Confirm their learning goals and explain that you'll use this information to create a customized learning curriculum for them.
        Then ask if they're ready to generate their personalized learning path.
        """
        
        # Generate response
        conversation = [
            SystemMessage(content=goals_prompt),
            *state.messages[-4:]  # Include a few recent messages for context
        ]
        
        response = await self.model.ainvoke(conversation)
        state.add_message(AIMessage(content=response.content))
        
        # Extract learning goals from the user's message
        # Simple extraction based on keywords
        goal_indicators = ["want to", "learn", "build", "create", "understand", "goal", "make", "develop"]
        for indicator in goal_indicators:
            if indicator in latest_message.lower():
                parts = latest_message.split(indicator, 1)
                if len(parts) > 1:
                    goal = parts[1].strip().split(".")[0].strip()
                    if goal and len(goal) > 10 and goal not in state.learning_goals:
                        state.learning_goals.append(goal)
        
        # If we couldn't extract specific goals, add generic ones based on framework
        if not state.learning_goals:
            state.learning_goals = [
                f"Learn the basics of {state.selected_framework.value}",
                f"Build a simple application using {state.selected_framework.value}",
                f"Understand how to integrate {state.selected_framework.value} with other technologies"
            ]
        
        # Check if the user is ready to move on (based on their latest response)
        ready_indicators = ["yes", "ready", "generate", "curriculum", "continue", "next", "proceed"]
        if any(indicator in latest_message.lower() for indicator in ready_indicators):
            state.current_stage = OnboardingStage.CURRICULUM_GENERATION
            state.complete_stage(OnboardingStage.LEARNING_GOALS)
        
        return state
    
    async def curriculum_generation_node(self, state: OnboardingState) -> OnboardingState:
        """Generate a personalized learning curriculum based on the user's profile and goals."""
        # Create learning objectives from goals
        skill_level = state.skill_level or SkillLevel.BEGINNER
        framework = state.selected_framework
        
        # Create placeholder learning objectives based on goals
        for i, goal in enumerate(state.learning_goals):
            objective_id = f"obj_{i+1}"
            
            # Determine difficulty level based on user's skill level
            if skill_level == SkillLevel.BEGINNER:
                difficulty = "beginner"
            elif skill_level == SkillLevel.INTERMEDIATE:
                difficulty = "intermediate"
            else:
                difficulty = "advanced"
            
            # Create learning objective
            objective = LearningObjective(
                objective_id=objective_id,
                description=goal,
                framework=framework,
                difficulty_level=difficulty,
                estimated_duration=60,  # Default to 1 hour
                prerequisites=[],
                skills_developed=[f"{framework.value} fundamentals", "Python programming"],
                assessment_criteria=[f"Can demonstrate {goal.lower()}", "Can explain concepts clearly"],
                completion_status=False,
                mastery_level=0.0
            )
            
            state.learning_objectives.append(objective)
        
        # Format the curriculum for display
        curriculum_display = self._format_curriculum_display(state, skill_level, framework)
        
        # Generate a response about the curriculum
        curriculum_prompt = f"""You are the PyFrameworks Onboarding Assistant. You've successfully created a personalized learning curriculum for {state.name if state.name else 'the user'} based on their goals and profile.

        Framework: {state.selected_framework.value}
        Skill Level: {skill_level.value}
        Learning Goals:
        {chr(10).join(['- ' + goal for goal in state.learning_goals])}
        
        Provide a brief intro (2-3 sentences) saying that their personalized curriculum has been created and that they can see the detailed learning path below.
        
        Then include this formatted curriculum display:
        
        {curriculum_display}
        
        After showing the curriculum, explain that this curriculum will adapt based on their progress and ask if they're ready to start learning {state.selected_framework.value} with this personalized plan.
        """
        
        # Generate response
        conversation = [
            SystemMessage(content=curriculum_prompt),
            *state.messages[-2:]  # Include a couple of recent messages for context
        ]
        
        response = await self.model.ainvoke(conversation)
        
        # Store the formatted curriculum for later use
        state.tool_results["formatted_curriculum"] = curriculum_display
        
        state.add_message(AIMessage(content=response.content))
        
        # Move to completion stage
        state.current_stage = OnboardingStage.COMPLETE
        state.complete_stage(OnboardingStage.CURRICULUM_GENERATION)
        
        return state
    
    async def completion_node(self, state: OnboardingState) -> OnboardingState:
        """Handle the completion stage of onboarding."""
        # Mark this stage as completed
        state.complete_stage(OnboardingStage.COMPLETE)
        
        # Create a user profile from the onboarding data
        user_profile = self._create_user_profile(state)
        
        # Connect with the learning hub to create a learning session
        try:
            from .learning_hub import LearningHubCore
            learning_hub = LearningHubCore()
            await learning_hub.initialize_hub()
            
            # Create a learning session with the onboarding data
            session_id = await learning_hub.create_learning_session(
                user_id=state.user_id,
                user_profile=user_profile,
                framework=state.selected_framework,
                learning_objectives=[obj.description for obj in state.learning_objectives],
                onboarding_data={
                    "learning_objectives": state.learning_objectives,
                    "framework_context": state.framework_context
                }
            )
            
            # Store the session ID in the state
            state.session_context = {
                "learning_session_id": session_id,
                "onboarding_complete": True
            }
            
            # Add completion message
            completion_message = (
                f"Great! I've created a personalized learning path for {state.selected_framework.value} "
                f"based on your profile and learning goals. Your learning session is ready to begin!\n\n"
                f"We'll start with the fundamentals and progress through your curriculum at a pace "
                f"that works for you. You can ask questions, request exercises, or take quizzes "
                f"to test your knowledge at any time.\n\n"
                f"Let's start your learning journey!"
            )
            
            state.add_message(AIMessage(content=completion_message))
            
        except Exception as e:
            # Log the error but continue
            logger.error(f"Failed to connect with learning hub: {e}")
            
            # Add fallback completion message
            completion_message = (
                f"Thanks for completing the onboarding process! I've created a profile "
                f"for learning {state.selected_framework.value} based on your preferences.\n\n"
                f"You're all set to begin your learning journey."
            )
            
            state.add_message(AIMessage(content=completion_message))
        
        return state
    
    def _create_user_profile(self, state: OnboardingState) -> UserProfile:
        """Create a UserProfile from the onboarding state."""
        # Create a learning session from the onboarding
        learning_session = LearningSession(
            session_id=state.session_id,
            framework=state.selected_framework,
            start_time=datetime.now(),
            topics_covered=[],
            engagement_metrics={},
            learning_objectives=[obj.objective_id for obj in state.learning_objectives],
            completion_percentage=0.0,
            notes=f"Created during onboarding on {datetime.now().strftime('%Y-%m-%d')}"
        )
        
        # Create the user profile
        profile = UserProfile(
            user_id=state.user_id,
            name=state.name or f"User_{state.user_id[:6]}",
            skill_level=state.skill_level or SkillLevel.BEGINNER,
            learning_style_preferences=state.learning_style_preferences,
            time_availability=state.time_availability or 5,  # Default to 5 hours/week
            framework_experience={
                str(state.selected_framework): 1.0  # Initial experience level
            },
            learning_goals=state.learning_goals,
            learning_sessions=[learning_session],
            achievements=[],
            progress_metrics={},
            preferences={
                "learning_style": state.learning_style_preferences,
                "communication_style": "conversational"
            },
            created_at=datetime.now(),
            last_active=datetime.now()
        )
        
        return profile

    def _format_curriculum_display(self, state: OnboardingState, skill_level: SkillLevel, framework: SupportedFrameworks) -> str:
        """Format the curriculum for CLI display."""
        
        # Header
        display = f"""
╔══════════════════════════════════════════════════════════════════╗
║                     📚 YOUR LEARNING CURRICULUM                 ║
║                        {framework.value.upper()} Framework                         ║
╚══════════════════════════════════════════════════════════════════╝

👤 Student Profile:
   • Name: {state.name or "Learner"}
   • Skill Level: {skill_level.value.title()}
   • Time Availability: {state.time_availability or 5} hours/week
   • Learning Styles: {', '.join(state.learning_style_preferences) if state.learning_style_preferences else 'Mixed'}

🎯 Learning Goals:"""
        
        # Add learning goals
        for i, goal in enumerate(state.learning_goals, 1):
            display += f"\n   {i}. {goal}"
        
        # Add learning objectives
        display += f"\n\n📖 Learning Objectives:"
        for i, obj in enumerate(state.learning_objectives, 1):
            estimated_hours = obj.estimated_duration // 60 if obj.estimated_duration >= 60 else 1
            display += f"\n   Module {i}: {obj.description}"
            display += f"\n            • Difficulty: {obj.difficulty_level.title()}"
            display += f"\n            • Duration: ~{estimated_hours} hour{'s' if estimated_hours > 1 else ''}"
            display += f"\n            • Skills: {', '.join(obj.skills_developed[:2])}"
            if i < len(state.learning_objectives):
                display += "\n"
        
        # Add framework context if available
        if state.framework_context:
            display += f"\n\n🔧 Framework Focus Areas:"
            key_concepts = state.framework_context.get("key_concepts", [])
            for i, concept in enumerate(key_concepts[:5], 1):
                display += f"\n   • {concept}"
            if len(key_concepts) > 5:
                display += f"\n   • ... and {len(key_concepts) - 5} more concepts"
        
        # Add learning path
        display += f"\n\n🛤️  Learning Path Overview:"
        if state.framework_context and "learning_path" in state.framework_context:
            learning_path = state.framework_context["learning_path"]
            for i, step in enumerate(learning_path[:4], 1):
                display += f"\n   Step {i}: {step}"
            if len(learning_path) > 4:
                display += f"\n   ... {len(learning_path) - 4} more steps in your journey"
        else:
            # Default learning path
            display += f"\n   Step 1: Introduction to {framework.value} fundamentals"
            display += f"\n   Step 2: Core concepts and basic implementation"
            display += f"\n   Step 3: Hands-on projects and practice"
            display += f"\n   Step 4: Advanced features and best practices"
        
        # Add estimated timeline
        total_hours = sum(obj.estimated_duration for obj in state.learning_objectives) // 60
        weeks_needed = max(1, total_hours // (state.time_availability or 5))
        
        display += f"\n\n⏱️  Estimated Timeline:"
        display += f"\n   • Total Learning Time: ~{total_hours} hours"
        display += f"\n   • Completion Time: ~{weeks_needed} week{'s' if weeks_needed > 1 else ''}"
        display += f"\n   • Sessions: 2-3 sessions per week recommended"
        
        # Footer
        display += f"\n\n💡 This curriculum will adapt based on your progress and interests!"
        display += f"\n════════════════════════════════════════════════════════════════════"
        
        return display


# Factory function to get a singleton instance
_onboarding_graph_instance = None

def get_onboarding_graph() -> OnboardingGraph:
    """Get a singleton instance of the OnboardingGraph."""
    global _onboarding_graph_instance
    
    if _onboarding_graph_instance is None:
        _onboarding_graph_instance = OnboardingGraph()
    
    return _onboarding_graph_instance


async def start_onboarding(user_id: str, session_id: str, message: str) -> Dict[str, Any]:
    """Start the onboarding process for a user."""
    graph = get_onboarding_graph()
    return await graph.start_onboarding(user_id, session_id, message) 