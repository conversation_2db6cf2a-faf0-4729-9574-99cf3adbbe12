"""
Framework-specific configurations for all supported AI frameworks.
Dynamic system that fetches current information from official sources using web search.
"""

from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class SupportedFrameworks(str, Enum):
    """Supported AI framework enumeration."""
    LANGCHAIN = "langchain"
    LANGGRAPH = "langgraph"
    CREWAI = "crewai"
    AUTOGEN = "autogen"
    LLAMAINDEX = "llamaindex"


class ModuleDifficulty(str, Enum):
    """Module difficulty levels."""
    BEGINNER = "beginner"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"
    EXPERT = "expert"


class LearningModule(BaseModel):
    """Individual learning module definition."""
    module_id: str = Field(description="Unique module identifier")
    title: str = Field(description="Module title")
    description: str = Field(description="Module description")
    difficulty: ModuleDifficulty = Field(description="Module difficulty level")
    estimated_duration: int = Field(description="Estimated duration in minutes")
    prerequisites: List[str] = Field(default_factory=list, description="Required prerequisite modules")
    learning_objectives: List[str] = Field(description="Learning objectives")
    topics: List[str] = Field(description="Topics covered")
    hands_on_exercises: int = Field(default=0, description="Number of hands-on exercises")
    theoretical_content: int = Field(default=0, description="Theoretical content weight (1-10)")
    practical_content: int = Field(default=0, description="Practical content weight (1-10)")
    tools_introduced: List[str] = Field(default_factory=list, description="New tools/concepts introduced")


class LearningPath(BaseModel):
    """Complete learning path for a framework."""
    path_id: str = Field(description="Unique path identifier")
    framework: SupportedFrameworks = Field(description="Target framework")
    title: str = Field(description="Learning path title")
    description: str = Field(description="Path description")
    total_estimated_hours: int = Field(description="Total estimated learning hours")
    modules: List[LearningModule] = Field(description="Ordered list of modules")
    certification_available: bool = Field(default=False, description="Certification available")
    project_templates: List[str] = Field(default_factory=list, description="Available project templates")


class FrameworkConfig(BaseModel):
    """Complete configuration for an AI framework."""
    framework: SupportedFrameworks = Field(description="Framework identifier")
    display_name: str = Field(description="Human-readable framework name")
    official_website: str = Field(description="Official framework website")
    documentation_url: str = Field(description="Official documentation URL")
    github_repository: str = Field(description="GitHub repository URL")
    description: str = Field(default="", description="Framework description")
    
    # Dynamic Information (fetched from web search)
    latest_version: str = Field(default="latest", description="Latest stable version (dynamic)")
    min_python_version: str = Field(default="3.8", description="Minimum Python version required")
    
    # Framework Characteristics (can be dynamically updated)
    primary_use_cases: List[str] = Field(default_factory=list, description="Primary use cases")
    key_features: List[str] = Field(default_factory=list, description="Key framework features")
    learning_complexity: ModuleDifficulty = Field(default=ModuleDifficulty.INTERMEDIATE, description="Overall learning complexity")
    
    # Learning Resources (dynamic)
    learning_paths: List[LearningPath] = Field(default_factory=list, description="Available learning paths")
    quick_start_modules: List[str] = Field(default_factory=list, description="Quick start module IDs")
    advanced_modules: List[str] = Field(default_factory=list, description="Advanced module IDs")
    
    # Integration Information (dynamic)
    compatible_frameworks: List[str] = Field(default_factory=list, description="Compatible frameworks")
    dependencies: List[str] = Field(default_factory=list, description="Key dependencies")
    
    # Constellation Preferences
    recommended_constellations: List[str] = Field(
        default_factory=list,
        description="Recommended constellation types for this framework"
    )
    optimal_agent_roles: List[str] = Field(
        default_factory=list,
        description="Optimal agent roles for learning this framework"
    )
    
    # Source tracking
    last_updated: Optional[str] = Field(default=None, description="Last update timestamp")
    source_urls: List[str] = Field(default_factory=list, description="Source URLs for dynamic data")


# Static Framework Configurations (Only Official URLs and Basic Info)
FRAMEWORK_STATIC_CONFIGS: Dict[SupportedFrameworks, Dict[str, str]] = {
    SupportedFrameworks.LANGCHAIN: {
        "display_name": "LangChain",
        "official_website": "https://www.langchain.com/",
        "documentation_url": "https://python.langchain.com/docs/",
        "github_repository": "https://github.com/langchain-ai/langchain",
        "description": "Framework for developing applications powered by language models"
    },
    SupportedFrameworks.LANGGRAPH: {
        "display_name": "LangGraph",
        "official_website": "https://langchain-ai.github.io/langgraph/",
        "documentation_url": "https://langchain-ai.github.io/langgraph/",
        "github_repository": "https://github.com/langchain-ai/langgraph",
        "description": "Library for building stateful, multi-actor applications with LLMs"
    },
    SupportedFrameworks.CREWAI: {
        "display_name": "CrewAI",
        "official_website": "https://crewai.com/",
        "documentation_url": "https://docs.crewai.com/",
        "github_repository": "https://github.com/joaomdmoura/crewAI",
        "description": "Framework for orchestrating role-playing, autonomous AI agents"
    },
    SupportedFrameworks.AUTOGEN: {
        "display_name": "AutoGen",
        "official_website": "https://microsoft.github.io/autogen/",
        "documentation_url": "https://microsoft.github.io/autogen/docs/",
        "github_repository": "https://github.com/microsoft/autogen",
        "description": "Framework for multi-agent conversation systems"
    },
    SupportedFrameworks.LLAMAINDEX: {
        "display_name": "LlamaIndex",
        "official_website": "https://www.llamaindex.ai/",
        "documentation_url": "https://docs.llamaindex.ai/",
        "github_repository": "https://github.com/run-llama/llama_index",
        "description": "Data framework for LLM applications to ingest, structure, and access data"
    }
}


class DynamicFrameworkConfigLoader:
    """Dynamic loader for framework configurations using web search."""
    
    def __init__(self):
        self._cache: Dict[SupportedFrameworks, FrameworkConfig] = {}
        
    def get_framework_config(self, framework: SupportedFrameworks, use_cache: bool = True) -> FrameworkConfig:
        """Get configuration for a specific framework, using cache or dynamic loading."""
        if use_cache and framework in self._cache:
            return self._cache[framework]
            
        # Load dynamic configuration
        config = self._load_dynamic_config(framework)
        self._cache[framework] = config
        return config
    
    def _load_dynamic_config(self, framework: SupportedFrameworks) -> FrameworkConfig:
        """Load framework configuration dynamically using web search."""
        try:
            # Get static info (official URLs)
            static_info = FRAMEWORK_STATIC_CONFIGS.get(framework, {})
            
            # Create base config with static information
            config = FrameworkConfig(
                framework=framework,
                display_name=static_info.get("display_name", framework.value.title()),
                official_website=static_info.get("official_website", f"https://{framework.value}.com/"),
                documentation_url=static_info.get("documentation_url", f"https://docs.{framework.value}.com/"),
                github_repository=static_info.get("github_repository", f"https://github.com/{framework.value}/{framework.value}"),
                latest_version="latest",
                min_python_version="3.8",
                primary_use_cases=[static_info.get("description", f"{framework.value} framework")],
                key_features=[],
                learning_complexity=ModuleDifficulty.INTERMEDIATE,
                learning_paths=[],
                quick_start_modules=[],
                advanced_modules=[],
                compatible_frameworks=[],
                dependencies=[],
                recommended_constellations=["knowledge_intensive", "hands_on_focused"],
                optimal_agent_roles=["instructor", "code_assistant", "documentation_expert"],
                source_urls=[]
            )
            
            # Try to load dynamic information using search tools
            try:
                self._enrich_with_dynamic_data(config)
            except Exception as e:
                logger.warning(f"Could not load dynamic data for {framework.value}: {e}")
            
            return config
            
        except Exception as e:
            logger.error(f"Error loading config for {framework.value}: {e}")
            return self._get_fallback_config(framework)
    
    def _enrich_with_dynamic_data(self, config: FrameworkConfig):
        """Enrich configuration with dynamic data from web search."""
        try:
            # Import here to avoid circular imports
            from ..tools.search_tools import TavilySearchTool
            
            search_tool = TavilySearchTool()
            framework_name = config.framework.value
            
            # Search for current framework information
            info_query = f"{framework_name} framework features capabilities latest version 2024"
            search_result = search_tool._run(info_query)
            
            # Extract dynamic information
            config.key_features = self._extract_key_features(search_result, framework_name)
            config.primary_use_cases = self._extract_use_cases(search_result, framework_name)
            config.dependencies = self._extract_dependencies(search_result, framework_name)
            config.source_urls = self._extract_source_urls(search_result)
            
            # Update timestamp
            from datetime import datetime
            config.last_updated = datetime.now().isoformat()
            
        except ImportError:
            logger.warning("Search tools not available for dynamic config loading")
        except Exception as e:
            logger.warning(f"Error enriching dynamic data: {e}")
    
    def _extract_key_features(self, search_result: str, framework: str) -> List[str]:
        """Extract key features from search results."""
        features = []
        lines = search_result.split('\n')
        
        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['feature', 'capability', 'support', 'provides']):
                if len(line) > 10 and len(line) < 200 and framework.lower() in line.lower():
                    features.append(line)
        
        return features[:10]  # Limit to top 10
    
    def _extract_use_cases(self, search_result: str, framework: str) -> List[str]:
        """Extract use cases from search results."""
        use_cases = []
        lines = search_result.split('\n')
        
        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['use case', 'used for', 'application', 'suitable for']):
                if len(line) > 10 and len(line) < 200 and framework.lower() in line.lower():
                    use_cases.append(line)
        
        return use_cases[:8]  # Limit to top 8
    
    def _extract_dependencies(self, search_result: str, framework: str) -> List[str]:
        """Extract dependencies from search results."""
        dependencies = []
        lines = search_result.split('\n')
        
        for line in lines:
            line = line.strip()
            if any(keyword in line.lower() for keyword in ['install', 'pip install', 'dependency', 'require']):
                # Look for package names
                import re
                packages = re.findall(r'pip install ([a-zA-Z0-9_-]+)', line)
                dependencies.extend(packages)
        
        return list(set(dependencies))[:10]  # Remove duplicates and limit
    
    def _extract_source_urls(self, search_result: str) -> List[str]:
        """Extract source URLs from search results."""
        import re
        url_pattern = r'https?://[^\s<>"{}|\\^`\[\]]+[^\s.,<>"{}|\\^`\[\]]'
        urls = re.findall(url_pattern, search_result)
        
        # Filter for relevant URLs
        relevant_urls = []
        for url in urls:
            if any(domain in url.lower() for domain in ['github.com', 'docs.', 'python.langchain.com', 'microsoft.github.io']):
                relevant_urls.append(url)
        
        return list(set(relevant_urls))[:5]  # Remove duplicates and limit
    
    def _get_fallback_config(self, framework: SupportedFrameworks) -> FrameworkConfig:
        """Get fallback configuration when dynamic loading fails."""
        static_info = FRAMEWORK_STATIC_CONFIGS.get(framework, {})
        
        return FrameworkConfig(
            framework=framework,
            display_name=static_info.get("display_name", framework.value.title()),
            official_website=static_info.get("official_website", f"https://{framework.value}.com/"),
            documentation_url=static_info.get("documentation_url", f"https://docs.{framework.value}.com/"),
            github_repository=static_info.get("github_repository", f"https://github.com/{framework.value}/{framework.value}"),
            latest_version="latest",
            min_python_version="3.8",
            primary_use_cases=[static_info.get("description", f"{framework.value} framework")],
            key_features=[f"Core {framework.value} functionality"],
            learning_complexity=ModuleDifficulty.INTERMEDIATE,
            learning_paths=[],
            quick_start_modules=[],
            advanced_modules=[],
            compatible_frameworks=[],
            dependencies=[framework.value],
            recommended_constellations=["knowledge_intensive"],
            optimal_agent_roles=["instructor", "documentation_expert"],
            source_urls=[]
        )


# Global loader instance
_dynamic_loader = DynamicFrameworkConfigLoader()


def get_framework_config(framework: SupportedFrameworks, use_cache: bool = True) -> FrameworkConfig:
    """Get configuration for a specific framework."""
    return _dynamic_loader.get_framework_config(framework, use_cache)


def get_all_frameworks() -> List[SupportedFrameworks]:
    """Get list of all supported frameworks."""
    return list(SupportedFrameworks)


def get_learning_path(framework: SupportedFrameworks, path_id: str) -> Optional[LearningPath]:
    """Get a specific learning path for a framework."""
    config = get_framework_config(framework)
    for path in config.learning_paths:
        if path.path_id == path_id:
            return path
    return None


def get_module(framework: SupportedFrameworks, module_id: str) -> Optional[LearningModule]:
    """Get a specific learning module."""
    config = get_framework_config(framework)
    for path in config.learning_paths:
        for module in path.modules:
            if module.module_id == module_id:
                return module
    return None


def get_prerequisites_chain(framework: SupportedFrameworks, module_id: str) -> List[str]:
    """Get the full chain of prerequisites for a module."""
    prerequisites = []
    module = get_module(framework, module_id)
    
    if not module:
        return prerequisites
    
    for prereq in module.prerequisites:
        prerequisites.extend(get_prerequisites_chain(framework, prereq))
        prerequisites.append(prereq)
    
    return prerequisites


def refresh_framework_config(framework: SupportedFrameworks) -> FrameworkConfig:
    """Force refresh of framework configuration from dynamic sources."""
    return _dynamic_loader.get_framework_config(framework, use_cache=False)


def get_framework_static_info(framework: SupportedFrameworks) -> Dict[str, str]:
    """Get static information (official URLs) for a framework."""
    return FRAMEWORK_STATIC_CONFIGS.get(framework, {}) 