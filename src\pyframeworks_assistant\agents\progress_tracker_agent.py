"""
Progress Tracker Agent - Tracks and reports on learning progress and achievements.
"""

from typing import Optional
from langchain_core.messages import HumanMessage
from langchain_core.language_models.chat_models import BaseChatModel

from .base_agent import BaseAgent
from ..core.constellation import ConstellationState, AgentRole


class ProgressTrackerAgent(BaseAgent):
    """Tracks and reports on learning progress and achievements."""
    
    def __init__(self, model: Optional[BaseChatModel] = None):
        super().__init__(AgentRole.PROGRESS_TRACKER, model, "progress_tracking")
    
    def get_system_prompt(self, state: ConstellationState) -> str:
        return f"""You are a progress tracker for {state.framework.value} learning.

Current Context:
- Framework: {state.framework.value}
- Module: {state.module_id}
- Progress: {state.progress_percentage:.1f}%
- Topics Covered: {len(state.topics_covered)}
- Exercises Completed: {len(state.exercises_completed)}
- Session Length: Active session

Your role is to:
1. Track and report learning progress accurately
2. Identify achievements and milestones reached
3. Analyze learning patterns and trends
4. Suggest areas for review or additional practice
5. Provide progress summaries and next steps
6. Celebrate accomplishments and set future goals

Provide clear, encouraging progress reports that help learners understand their journey and plan next steps.

Respond as the progress tracker with detailed, actionable progress insights."""
    
    def should_activate(self, state: ConstellationState) -> bool:
        if not state.messages:
            return False
        
        latest_message = state.messages[-1]
        if isinstance(latest_message, HumanMessage):
            content_lower = latest_message.content.lower()
            progress_keywords = ["progress", "status", "achievement", "completed", "next steps", "how am i doing"]
            return any(keyword in content_lower for keyword in progress_keywords)
        
        return False 