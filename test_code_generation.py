#!/usr/bin/env python3
"""
Test script to verify code generation fixes in the GAAPF system.
This script will test the dynamic code generation functionality.
"""

import os
import sys
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent))

from src.pyframeworks_assistant.agents.code_assistant_agent import CodeAssistantAgent
from src.pyframeworks_assistant.core.constellation_types import Agent<PERSON><PERSON>
from src.pyframeworks_assistant.config.framework_configs import SupportedFrameworks

def test_code_generation():
    """Test the dynamic code generation functionality."""
    print("Testing code generation functionality...")
    
    # Create a code assistant agent
    code_assistant = CodeAssistantAgent()
    
    # Test code generation for different frameworks
    frameworks = ["langchain", "langgraph", "crewai", "autogen"]
    user_messages = [
        "I want to create a simple chain",
        "Show me how to create a graph with nodes",
        "I need a crew with agents",
        "Generate a multi-agent system"
    ]
    
    for framework, message in zip(frameworks, user_messages):
        print(f"\n\nTesting code generation for {framework} with message: '{message}'")
        
        # Generate code
        code = code_assistant.generate_dynamic_code(
            user_message=message,
            framework=framework,
            skill_level="beginner"
        )
        
        # Print the first few lines of the generated code
        print(f"\nGenerated code (first 10 lines):")
        lines = code.split('\n')
        for i, line in enumerate(lines[:10]):
            print(f"{i+1}: {line}")
        
        print(f"... (total {len(lines)} lines)")
        
        # Verify the code is not just the user's message
        assert message not in code, f"Code contains the user's message: {message}"
        assert len(code) > 100, f"Code is too short: {len(code)} characters"
        
        # Check for framework-specific imports
        if framework == "langchain":
            assert "langchain" in code.lower(), "Code doesn't contain langchain imports"
        elif framework == "langgraph":
            assert "langgraph" in code.lower(), "Code doesn't contain langgraph imports"
        elif framework == "crewai":
            assert "crewai" in code.lower(), "Code doesn't contain crewai imports"
        elif framework == "autogen":
            assert "autogen" in code.lower(), "Code doesn't contain autogen imports"
    
    print("\nAll tests passed successfully!")

if __name__ == "__main__":
    test_code_generation() 