"""
Conversation memory management for maintaining context and history.
Handles short-term and long-term conversation memory with summarization.
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Union
from dataclasses import dataclass, asdict
import logging

from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, SystemMessage
from langchain.memory import ConversationSummaryBufferMemory
from langchain.schema import ChatMessage
from pydantic import BaseModel, Field

from ..core.models import llm_manager
from ..config.settings import settings

logger = logging.getLogger(__name__)


@dataclass
class ConversationTurn:
    """Represents a single conversation turn."""
    timestamp: datetime
    user_message: str
    ai_response: str
    context: Dict[str, Any]
    topic: Optional[str] = None
    framework: Optional[str] = None
    session_id: Optional[str] = None


class ConversationSummary(BaseModel):
    """Summary of a conversation session."""
    session_id: str = Field(description="Unique session identifier")
    start_time: datetime = Field(description="Session start time")
    end_time: Optional[datetime] = Field(default=None, description="Session end time")
    framework: str = Field(description="Framework being learned")
    topics_covered: List[str] = Field(default_factory=list, description="Topics covered in session")
    key_concepts: List[str] = Field(default_factory=list, description="Key concepts discussed")
    user_questions: List[str] = Field(default_factory=list, description="User questions asked")
    learning_outcomes: List[str] = Field(default_factory=list, description="Learning outcomes achieved")
    summary_text: str = Field(description="Human-readable summary")
    turn_count: int = Field(default=0, description="Number of conversation turns")
    engagement_score: float = Field(default=0.0, description="Session engagement score")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class ConversationMemory:
    """Manages conversation memory with short-term buffer and long-term summaries."""
    
    def __init__(
        self,
        session_id: str,
        max_buffer_size: int = 20,
        summary_threshold: int = 10
    ):
        """Initialize conversation memory."""
        self.session_id = session_id
        self.max_buffer_size = max_buffer_size
        self.summary_threshold = summary_threshold
        
        # Short-term memory buffer
        self.message_buffer: List[BaseMessage] = []
        self.turn_buffer: List[ConversationTurn] = []
        
        # Long-term memory
        self.session_summary: Optional[ConversationSummary] = None
        self.conversation_summaries: List[str] = []
        
        # Modern LangChain memory integration (avoiding deprecated classes)
        try:
            # Use modern approach - just store messages directly without deprecated memory classes
            # The deprecated ConversationSummaryBufferMemory has been replaced with simpler message handling
            self.langchain_memory = None  # We'll handle memory manually
            logger.info("Using modern message-based memory approach")
        except Exception as e:
            logger.warning(f"Memory initialization warning: {e}")
            self.langchain_memory = None
        
        self.start_time = datetime.now()
        self.last_activity = self.start_time
        
    def add_message(self, message: BaseMessage, context: Optional[Dict[str, Any]] = None) -> None:
        """Add a message to the conversation memory."""
        try:
            # Add to buffer
            self.message_buffer.append(message)
            self.last_activity = datetime.now()
            
            # Modern approach: messages are already stored in message_buffer
            # No need for deprecated LangChain memory classes
            
            # Check if we need to create a conversation turn
            if len(self.message_buffer) >= 2:
                self._try_create_turn(context or {})
            
            # Trigger summarization if buffer is full
            if len(self.message_buffer) > self.max_buffer_size:
                asyncio.create_task(self._summarize_and_compress())
                
        except Exception as e:
            logger.error(f"Error adding message to memory: {e}")
    
    def _try_create_turn(self, context: Dict[str, Any]) -> None:
        """Try to create a conversation turn from recent messages."""
        try:
            # Look for user message followed by AI message
            recent_messages = self.message_buffer[-2:]
            if (len(recent_messages) == 2 and 
                isinstance(recent_messages[0], HumanMessage) and
                isinstance(recent_messages[1], AIMessage)):
                
                turn = ConversationTurn(
                    timestamp=self.last_activity,
                    user_message=recent_messages[0].content,
                    ai_response=recent_messages[1].content,
                    context=context,
                    topic=context.get("topic"),
                    framework=context.get("framework"),
                    session_id=self.session_id
                )
                
                self.turn_buffer.append(turn)
                
        except Exception as e:
            logger.error(f"Error creating conversation turn: {e}")
    
    async def _summarize_and_compress(self) -> None:
        """Summarize old conversations and compress memory."""
        try:
            if len(self.turn_buffer) < self.summary_threshold:
                return
            
            # Create summary of older turns
            turns_to_summarize = self.turn_buffer[:-5]  # Keep last 5 turns
            summary_text = await self._generate_summary(turns_to_summarize)
            
            self.conversation_summaries.append(summary_text)
            
            # Remove summarized turns
            self.turn_buffer = self.turn_buffer[-5:]
            
            # Compress message buffer
            self.message_buffer = self.message_buffer[-self.max_buffer_size//2:]
            
            logger.info(f"Conversation memory compressed for session {self.session_id}")
            
        except Exception as e:
            logger.error(f"Error in memory compression: {e}")
    
    async def _generate_summary(self, turns: List[ConversationTurn]) -> str:
        """Generate a summary of conversation turns."""
        try:
            if not turns:
                return ""
            
            # Prepare content for summarization
            conversation_text = []
            for turn in turns:
                conversation_text.append(f"User: {turn.user_message}")
                conversation_text.append(f"AI: {turn.ai_response}")
            
            content = "\n".join(conversation_text)
            
            # Use LangChain's summarization
            summary_prompt = f"""
            Summarize the following conversation focusing on:
            1. Key topics discussed
            2. Learning progress made
            3. Important concepts covered
            4. Questions asked and answered
            
            Conversation:
            {content}
            
            Summary:
            """
            
            model = llm_manager.get_default_model()
            response = await model.ainvoke([SystemMessage(content=summary_prompt)])
            
            return response.content
            
        except Exception as e:
            logger.error(f"Error generating summary: {e}")
            return f"Summary generation failed: {str(e)}"
    
    def get_recent_context(self, n_messages: int = 10) -> List[BaseMessage]:
        """Get recent conversation context."""
        return self.message_buffer[-n_messages:]
    
    def get_conversation_history(self) -> Dict[str, Any]:
        """Get full conversation history including summaries."""
        return {
            "session_id": self.session_id,
            "start_time": self.start_time.isoformat(),
            "last_activity": self.last_activity.isoformat(),
            "message_count": len(self.message_buffer),
            "turn_count": len(self.turn_buffer),
            "summaries": self.conversation_summaries,
            "recent_messages": [
                {
                    "type": type(msg).__name__,
                    "content": msg.content[:200] + "..." if len(msg.content) > 200 else msg.content,
                    "timestamp": getattr(msg, 'timestamp', self.last_activity).isoformat() if hasattr(msg, 'timestamp') else self.last_activity.isoformat()
                }
                for msg in self.message_buffer[-5:]
            ]
        }
    
    async def create_session_summary(self) -> ConversationSummary:
        """Create a comprehensive session summary."""
        try:
            # Extract topics and concepts from conversation
            topics_covered = []
            key_concepts = []
            user_questions = []
            
            for turn in self.turn_buffer:
                if turn.topic:
                    topics_covered.append(turn.topic)
                
                # Extract questions (simple heuristic)
                if "?" in turn.user_message:
                    user_questions.append(turn.user_message)
                
                # Extract concepts from context
                if turn.context.get("concepts"):
                    key_concepts.extend(turn.context["concepts"])
            
            # Generate summary text
            summary_text = await self._generate_session_summary()
            
            # Calculate engagement score
            engagement_score = self._calculate_engagement_score()
            
            self.session_summary = ConversationSummary(
                session_id=self.session_id,
                start_time=self.start_time,
                end_time=datetime.now(),
                framework=self.turn_buffer[0].framework if self.turn_buffer else "unknown",
                topics_covered=list(set(topics_covered)),
                key_concepts=list(set(key_concepts)),
                user_questions=user_questions,
                learning_outcomes=[],  # Would be populated by assessment agents
                summary_text=summary_text,
                turn_count=len(self.turn_buffer),
                engagement_score=engagement_score
            )
            
            return self.session_summary
            
        except Exception as e:
            logger.error(f"Error creating session summary: {e}")
            return ConversationSummary(
                session_id=self.session_id,
                start_time=self.start_time,
                framework="unknown",
                summary_text=f"Session summary failed: {str(e)}"
            )
    
    async def _generate_session_summary(self) -> str:
        """Generate a comprehensive session summary."""
        try:
            all_turns_text = []
            for turn in self.turn_buffer:
                all_turns_text.append(f"Q: {turn.user_message}")
                all_turns_text.append(f"A: {turn.ai_response[:500]}...")  # Truncate long responses
            
            # Include conversation summaries
            all_text = "\n".join(self.conversation_summaries + all_turns_text)
            
            summary_prompt = f"""
            Create a comprehensive summary of this learning session:
            
            {all_text}
            
            Please provide:
            1. Main topics covered
            2. Key learning achievements
            3. Areas that may need more practice
            4. Overall session assessment
            
            Summary:
            """
            
            model = llm_manager.get_default_model()
            response = await model.ainvoke([SystemMessage(content=summary_prompt)])
            
            return response.content
            
        except Exception as e:
            logger.error(f"Error generating session summary: {e}")
            return "Failed to generate session summary"
    
    def _calculate_engagement_score(self) -> float:
        """Calculate engagement score based on conversation patterns."""
        try:
            if not self.turn_buffer:
                return 0.0
            
            # Factors for engagement calculation
            total_turns = len(self.turn_buffer)
            avg_user_message_length = sum(len(turn.user_message) for turn in self.turn_buffer) / total_turns
            avg_ai_response_length = sum(len(turn.ai_response) for turn in self.turn_buffer) / total_turns
            
            # Question frequency
            questions_asked = sum(1 for turn in self.turn_buffer if "?" in turn.user_message)
            question_rate = questions_asked / total_turns
            
            # Session duration
            session_duration = (self.last_activity - self.start_time).total_seconds() / 60  # minutes
            turn_frequency = total_turns / max(session_duration, 1)
            
            # Calculate engagement score (0-1)
            engagement_score = (
                min(avg_user_message_length / 100, 1.0) * 0.3 +  # User engagement
                min(turn_frequency / 2, 1.0) * 0.3 +  # Conversation pace
                min(question_rate * 2, 1.0) * 0.4  # Question asking
            )
            
            return min(engagement_score, 1.0)
            
        except Exception as e:
            logger.error(f"Error calculating engagement score: {e}")
            return 0.5  # Default moderate engagement


class MemoryManager:
    """Manages multiple conversation memory instances."""
    
    def __init__(self):
        """Initialize the memory manager."""
        self.active_sessions: Dict[str, ConversationMemory] = {}
        self.session_summaries: Dict[str, ConversationSummary] = {}
        self.cleanup_interval = timedelta(hours=settings.memory_cleanup_interval // 3600)
        self.max_sessions = settings.max_memory_sessions
        
    def get_or_create_session(self, session_id: str) -> ConversationMemory:
        """Get existing session or create new one."""
        if session_id not in self.active_sessions:
            self.active_sessions[session_id] = ConversationMemory(session_id)
            
            # Cleanup old sessions if needed
            if len(self.active_sessions) > self.max_sessions:
                asyncio.create_task(self._cleanup_old_sessions())
        
        return self.active_sessions[session_id]
    
    async def end_session(self, session_id: str) -> Optional[ConversationSummary]:
        """End a session and create summary."""
        try:
            if session_id in self.active_sessions:
                memory = self.active_sessions[session_id]
                summary = await memory.create_session_summary()
                
                # Store summary and remove active session
                self.session_summaries[session_id] = summary
                del self.active_sessions[session_id]
                
                return summary
            
            return None
            
        except Exception as e:
            logger.error(f"Error ending session {session_id}: {e}")
            return None
    
    async def _cleanup_old_sessions(self) -> None:
        """Clean up old inactive sessions."""
        try:
            current_time = datetime.now()
            sessions_to_remove = []
            
            for session_id, memory in self.active_sessions.items():
                if current_time - memory.last_activity > self.cleanup_interval:
                    sessions_to_remove.append(session_id)
            
            # End old sessions
            for session_id in sessions_to_remove:
                await self.end_session(session_id)
                
            if sessions_to_remove:
                logger.info(f"Cleaned up {len(sessions_to_remove)} inactive sessions")
                
        except Exception as e:
            logger.error(f"Error in session cleanup: {e}")
    
    def get_session_history(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get session history if available."""
        if session_id in self.active_sessions:
            return self.active_sessions[session_id].get_conversation_history()
        elif session_id in self.session_summaries:
            return asdict(self.session_summaries[session_id])
        return None
    
    def get_all_sessions(self) -> Dict[str, Dict[str, Any]]:
        """Get information about all sessions."""
        all_sessions = {}
        
        # Active sessions
        for session_id, memory in self.active_sessions.items():
            all_sessions[session_id] = {
                "status": "active",
                "start_time": memory.start_time.isoformat(),
                "last_activity": memory.last_activity.isoformat(),
                "message_count": len(memory.message_buffer),
                "turn_count": len(memory.turn_buffer)
            }
        
        # Completed sessions
        for session_id, summary in self.session_summaries.items():
            all_sessions[session_id] = {
                "status": "completed",
                "start_time": summary.start_time.isoformat(),
                "end_time": summary.end_time.isoformat() if summary.end_time else None,
                "framework": summary.framework,
                "turn_count": summary.turn_count,
                "engagement_score": summary.engagement_score
            }
        
        return all_sessions


class ConversationMemoryManager:
    """
    Manager for conversation memory across multiple sessions.
    Provides the interface needed by the Learning Hub.
    """
    
    def __init__(self):
        """Initialize the conversation memory manager."""
        self.memory_manager = MemoryManager()
        logger.info("ConversationMemoryManager initialized")
    
    async def initialize(self) -> None:
        """Initialize the conversation memory manager asynchronously."""
        try:
            # Any async initialization can go here
            logger.info("ConversationMemoryManager async initialization complete")
        except Exception as e:
            logger.error(f"Failed to initialize ConversationMemoryManager: {e}")
            raise
    
    async def create_session(self, session_id: str, user_id: str) -> None:
        """Create a new conversation session."""
        try:
            # Create the session memory
            self.memory_manager.get_or_create_session(session_id)
            logger.info(f"Created conversation session {session_id} for user {user_id}")
        except Exception as e:
            logger.error(f"Failed to create conversation session: {e}")
            raise
    
    async def add_message(self, session_id: str, message: str, message_type: str) -> None:
        """Add a message to a conversation session."""
        try:
            session_memory = self.memory_manager.get_or_create_session(session_id)
            
            # Create appropriate message type
            if message_type == "user":
                msg = HumanMessage(content=message)
            elif message_type == "assistant":
                msg = AIMessage(content=message)
            else:
                msg = SystemMessage(content=message)
            
            session_memory.add_message(msg)
            
        except Exception as e:
            logger.error(f"Failed to add message to session {session_id}: {e}")
    
    async def get_session_insights(self, session_id: str) -> Dict[str, Any]:
        """Get insights from a conversation session."""
        try:
            session_history = self.memory_manager.get_session_history(session_id)
            if not session_history:
                return {"error": "Session not found"}
            
            session_memory = self.memory_manager.sessions.get(session_id)
            if not session_memory:
                return {"error": "Session memory not found"}
            
            # Generate insights
            insights = {
                "session_id": session_id,
                "total_turns": len(session_memory.turn_buffer),
                "total_messages": len(session_memory.message_buffer),
                "session_duration": (datetime.now() - session_memory.start_time).total_seconds() / 60,
                "engagement_score": session_memory._calculate_engagement_score(),
                "topics_discussed": self._extract_topics_from_turns(session_memory.turn_buffer),
                "key_concepts": self._extract_concepts_from_turns(session_memory.turn_buffer),
                "conversation_flow": self._analyze_conversation_flow(session_memory.turn_buffer)
            }
            
            return insights
            
        except Exception as e:
            logger.error(f"Failed to get session insights: {e}")
            return {"error": str(e)}
    
    def _extract_topics_from_turns(self, turns: List[ConversationTurn]) -> List[str]:
        """Extract topics from conversation turns."""
        topics = set()
        for turn in turns:
            if turn.topic:
                topics.add(turn.topic)
            if turn.context and "topic" in turn.context:
                topics.add(turn.context["topic"])
        return list(topics)
    
    def _extract_concepts_from_turns(self, turns: List[ConversationTurn]) -> List[str]:
        """Extract key concepts from conversation turns."""
        concepts = set()
        
        # Simple keyword extraction (in a real implementation, this would be more sophisticated)
        concept_keywords = [
            "model", "view", "template", "form", "database", "query", "api", "endpoint",
            "authentication", "authorization", "middleware", "routing", "blueprint",
            "dependency injection", "async", "await", "class", "function", "method"
        ]
        
        for turn in turns:
            text = (turn.user_message + " " + turn.ai_response).lower()
            for keyword in concept_keywords:
                if keyword in text:
                    concepts.add(keyword)
        
        return list(concepts)
    
    def _analyze_conversation_flow(self, turns: List[ConversationTurn]) -> Dict[str, Any]:
        """Analyze the flow of conversation."""
        if not turns:
            return {"flow_type": "empty"}
        
        # Simple flow analysis
        question_count = 0
        explanation_count = 0
        code_count = 0
        
        for turn in turns:
            user_msg = turn.user_message.lower()
            ai_msg = turn.ai_response.lower()
            
            if "?" in user_msg or any(word in user_msg for word in ["what", "how", "why", "when", "where"]):
                question_count += 1
            
            if any(word in ai_msg for word in ["explain", "understand", "concept", "means"]):
                explanation_count += 1
            
            if "```" in ai_msg or "code" in ai_msg:
                code_count += 1
        
        total_turns = len(turns)
        
        return {
            "flow_type": "mixed",
            "question_ratio": question_count / total_turns if total_turns > 0 else 0,
            "explanation_ratio": explanation_count / total_turns if total_turns > 0 else 0,
            "code_ratio": code_count / total_turns if total_turns > 0 else 0,
            "dominant_pattern": self._determine_dominant_pattern(question_count, explanation_count, code_count)
        }
    
    def _determine_dominant_pattern(self, questions: int, explanations: int, code: int) -> str:
        """Determine the dominant conversation pattern."""
        max_count = max(questions, explanations, code)
        
        if max_count == questions:
            return "inquiry_focused"
        elif max_count == explanations:
            return "explanation_focused"
        elif max_count == code:
            return "code_focused"
        else:
            return "balanced"


# Global memory manager instance
memory_manager = MemoryManager() 