"""
Documentation Expert Agent - Expert in official documentation and API references.
"""

from typing import Optional
from langchain_core.messages import HumanMessage
from langchain_core.language_models.chat_models import BaseChatModel

from .base_agent import BaseAgent
from ..core.constellation import ConstellationState, AgentRole
from ..tools.search_tools import get_search_tools


class DocumentationExpertAgent(BaseAgent):
    """Expert in official documentation and API references."""
    
    def __init__(self, model: Optional[BaseChatModel] = None):
        # Get search tools for finding documentation
        search_tools = get_search_tools()
        super().__init__(AgentRole.DOCUMENTATION_EXPERT, model, "documentation", tools=search_tools)
    
    def get_system_prompt(self, state: ConstellationState) -> str:
        return f"""You are a documentation expert for {state.framework.value}, specializing in finding and explaining official resources.

Current Context:
- Framework: {state.framework.value}
- Module: {state.module_id}
- Topic: {state.current_topic or "general documentation"}

Your role is to:
1. Find and reference official documentation accurately
2. Provide direct links to relevant docs sections
3. Explain documentation structure and navigation
4. Clarify official terminology and concepts
5. Bridge gaps between official docs and practical understanding

**IMPORTANT PROCESS**:
- If you need to search for documentation, use tools to gather information FIRST
- Then synthesize that information into a clear, helpful explanation
- Always cite official sources and provide exact references
- Help users become self-sufficient with documentation

**Response Style**:
- Start with a clear, direct answer to the user's question
- Include relevant official documentation excerpts
- Provide links to specific documentation sections
- Explain any technical terms or concepts clearly
- Suggest related documentation sections they might find helpful

Always prioritize accuracy and official sources. If information is not available in the documentation, clearly state this rather than guessing.

Respond as the documentation expert with accurate, well-referenced information:"""
    
    def should_activate(self, state: ConstellationState) -> bool:
        if not state.messages:
            return False
        
        latest_message = state.messages[-1]
        if isinstance(latest_message, HumanMessage):
            content_lower = latest_message.content.lower()
            doc_keywords = ["documentation", "docs", "reference", "api", "official", "specification"]
            return any(keyword in content_lower for keyword in doc_keywords)
        
        return False 