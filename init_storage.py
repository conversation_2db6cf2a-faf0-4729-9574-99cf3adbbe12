#!/usr/bin/env python3
"""
Storage Initialization Script for PyFrameworks Assistant
Run this script before starting the guidance system to ensure all storage is ready.
"""

import sys
import logging
from pathlib import Path

# Add the src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from pyframeworks_assistant.memory.db_init import initialize_storage_systems, create_storage_summary
from pyframeworks_assistant.config.settings import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('storage_init.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)


def main():
    """Main initialization function."""
    print("=" * 60)
    print("🚀 PyFrameworks Assistant - Storage Initialization")
    print("=" * 60)
    print()
    
    try:
        # Initialize all storage systems
        logger.info("Starting storage initialization process...")
        results = initialize_storage_systems()
        
        # Display results
        print("📊 INITIALIZATION RESULTS:")
        print("-" * 40)
        
        for component, data in results.items():
            if isinstance(data, dict):
                status = data.get("status", "unknown")
                if status == "success":
                    status_icon = "✅"
                elif status == "unavailable":
                    status_icon = "⚠️"
                elif status == "failed":
                    status_icon = "❌"
                else:
                    status_icon = "🔄"
                
                print(f"{status_icon} {component.replace('_', ' ').title()}: {status}")
                
                # Show additional info
                if "type" in data and data["type"]:
                    print(f"   Type: {data['type']}")
                if "path" in data and data["path"]:
                    print(f"   Path: {data['path']}")
                if "error" in data and data["error"]:
                    print(f"   Error: {data['error']}")
                print()
        
        # Show verification results
        verification = results.get("verification", {})
        if verification:
            print("🔍 VERIFICATION RESULTS:")
            print("-" * 40)
            
            for check, data in verification.items():
                if isinstance(data, dict):
                    status = data.get("status", "unknown")
                    status_icon = "✅" if status == "success" else "❌"
                    print(f"{status_icon} {check.replace('_', ' ').title()}: {status}")
                    
                    if "tables" in data:
                        print(f"   Tables: {', '.join(data['tables'])}")
                    if "collections" in data:
                        collections = data['collections']
                        available = [k for k, v in collections.items() if v == "available"]
                        print(f"   Collections: {', '.join(available)}")
                    if "error" in data and data["error"]:
                        print(f"   Error: {data['error']}")
                    print()
        
        # Check if ready for guidance
        summary = create_storage_summary()
        ready = summary.get("ready_for_guidance", False)
        
        print("=" * 60)
        if ready:
            print("🎉 STORAGE INITIALIZATION COMPLETE!")
            print("✅ All systems ready for guidance")
        else:
            print("⚠️  STORAGE INITIALIZATION ISSUES DETECTED")
            print("❌ Some systems may not be fully functional")
        print("=" * 60)
        
        # Create sample data if requested
        if len(sys.argv) > 1 and sys.argv[1] == "--create-sample":
            print("\n🔧 Creating sample data...")
            
            # Create a new initializer and initialize it
            from pyframeworks_assistant.memory.db_init import StorageInitializer
            sample_initializer = StorageInitializer()
            sample_initializer.initialize_all_storage()
            sample_results = sample_initializer.create_sample_data()
            
            if sample_results.get("status") == "success":
                print("✅ Sample data created successfully")
                print(f"   Sample user ID: {sample_results.get('sample_user_id')}")
                print(f"   Records created: {sample_results.get('created_records')}")
            else:
                print(f"❌ Failed to create sample data: {sample_results.get('error')}")
        
        return 0 if ready else 1
        
    except Exception as e:
        logger.error(f"Storage initialization failed: {e}")
        print(f"❌ CRITICAL ERROR: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main()) 