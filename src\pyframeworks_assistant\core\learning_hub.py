"""
Learning Hub Core - Central coordination system for the Learning Constellation Hub architecture.
This implements the core hub that orchestrates all learning components and agents.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Tuple, Union
from datetime import datetime, timed<PERSON>ta
from enum import Enum
from dataclasses import dataclass
from pydantic import BaseModel, Field

from .constellation import ConstellationManager, ConstellationState, AgentRole, ConstellationType
from .intelligent_agent_manager import IntelligentAgentManager
from .intelligent_context import IntelligentContext
from langchain_core.messages import HumanMessage, AIMessage 
from .adaptive_learning_engine import AdaptiveLearningEngine, LearningPath, LearningObjective
from .knowledge_graph import KnowledgeGraphManager, ConceptNode, LearningRelation
from .analytics_engine import RealTimeAnalytics, LearningMetrics, PerformanceIndicator
from ..agents.base_agent import BaseAgent
from ..memory.conversation_memory import ConversationMemoryManager
from ..memory.knowledge_memory import KnowledgeMemoryManager
from ..memory.user_memory import UserMemoryManager
from ..config.user_profiles import UserProfile
from ..config.framework_configs import SupportedFrameworks

logger = logging.getLogger(__name__)


class LearningModality(str, Enum):
    """Different learning modalities supported by the hub."""
    VISUAL = "visual"
    AUDITORY = "auditory"
    KINESTHETIC = "kinesthetic"
    READING_WRITING = "reading_writing"
    MULTIMODAL = "multimodal"


class HubState(str, Enum):
    """States of the Learning Hub."""
    INITIALIZING = "initializing"
    ACTIVE = "active"
    ADAPTING = "adapting"
    EVALUATING = "evaluating"
    OPTIMIZING = "optimizing"
    IDLE = "idle"


@dataclass
class LearningSession:
    """Represents a complete learning session in the hub."""
    session_id: str
    user_id: str
    framework: SupportedFrameworks
    learning_objectives: List[LearningObjective]
    current_path: LearningPath
    constellation_state: ConstellationState
    start_time: datetime
    last_activity: datetime
    modality: LearningModality
    effectiveness_score: float = 0.0
    completion_percentage: float = 0.0


class LearningHubCore:
    """
    Central Learning Hub that coordinates all learning components.
    Implements the Learning Constellation Hub architecture.
    """
    
    def __init__(self):
        """Initialize the Learning Hub Core."""
        self.hub_state = HubState.INITIALIZING
        self.active_sessions: Dict[str, LearningSession] = {}
        
        # Core components
        self.constellation_manager = ConstellationManager()
        self.intelligent_agent_manager = IntelligentAgentManager()
        self.adaptive_learning_engine = AdaptiveLearningEngine()
        self.knowledge_graph_manager = KnowledgeGraphManager()
        self.analytics_engine = RealTimeAnalytics()
        
        # Memory systems
        self.conversation_memory = ConversationMemoryManager()
        self.knowledge_memory = KnowledgeMemoryManager()
        self.user_memory = UserMemoryManager()
        
        # Hub metrics
        self.total_sessions = 0
        self.successful_completions = 0
        self.average_effectiveness = 0.0
        
        logger.info("Learning Hub Core initialized")
    
    async def initialize_hub(self) -> None:
        """Initialize all hub components."""
        try:
            self.hub_state = HubState.INITIALIZING
            
            # Initialize core components
            await self.constellation_manager.initialize()
            await self.adaptive_learning_engine.initialize()
            await self.knowledge_graph_manager.initialize()
            await self.analytics_engine.initialize()
            
            # Initialize memory systems
            await self.conversation_memory.initialize()
            await self.knowledge_memory.initialize()
            await self.user_memory.initialize()
            
            self.hub_state = HubState.IDLE
            logger.info("Learning Hub Core fully initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize Learning Hub: {e}")
            raise
    
    async def create_learning_session(
        self,
        user_id: str,
        user_profile: UserProfile,
        framework: SupportedFrameworks,
        learning_objectives: List[str],
        preferred_modality: Optional[LearningModality] = None,
        onboarding_data: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Create a new learning session with adaptive path generation.
        
        Args:
            user_id: Unique user identifier
            user_profile: User's learning profile
            framework: Target framework to learn
            learning_objectives: List of learning objectives
            preferred_modality: Preferred learning modality
            onboarding_data: Optional data from onboarding flow
            
        Returns:
            Session ID for the created session
        """
        try:
            session_id = f"session_{user_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # Use learning objectives from onboarding if available
            if onboarding_data and "learning_objectives" in onboarding_data:
                objectives = onboarding_data["learning_objectives"]
            else:
                # Convert string objectives to LearningObjective objects
                objectives = [
                    LearningObjective(
                        objective_id=f"obj_{i}",
                        description=obj,
                        framework=framework,
                        difficulty_level="adaptive",
                        estimated_duration=30  # Default 30 minutes
                    )
                    for i, obj in enumerate(learning_objectives)
                ]
            
            # Generate adaptive learning path
            learning_path = await self.adaptive_learning_engine.generate_learning_path(
                user_profile=user_profile,
                objectives=objectives,
                framework=framework
            )
            
            # Determine optimal learning modality
            modality = preferred_modality or self._determine_optimal_modality(user_profile)
            
            # Create constellation state with curriculum integration
            constellation_state = ConstellationState(
                session_id=session_id,
                user_id=user_id,
                framework=framework,
                module_id=learning_path.current_module_id if learning_path.modules else "intro",
                learning_objectives=[obj.description for obj in objectives],
                curriculum_path_id=learning_path.path_id
            )
            
            # Initialize curriculum tracking in constellation state
            if learning_path.modules:
                # Store module IDs for curriculum tracking
                constellation_state.session_context["module_ids"] = [
                    module.module_id for module in learning_path.modules
                ]
                constellation_state.session_context["total_modules"] = len(learning_path.modules)
                
                # Set current module information
                current_module = learning_path.get_current_module()
                if current_module:
                    constellation_state.session_context["current_module_title"] = current_module.title
                    constellation_state.session_context["current_module_description"] = current_module.description
                    constellation_state.session_context["objectives_in_current_module"] = len(current_module.objectives)
            
            # Create learning session
            session = LearningSession(
                session_id=session_id,
                user_id=user_id,
                framework=framework,
                learning_objectives=objectives,
                current_path=learning_path,
                constellation_state=constellation_state,
                start_time=datetime.now(),
                last_activity=datetime.now(),
                modality=modality
            )
            
            # Store session
            self.active_sessions[session_id] = session
            self.total_sessions += 1
            
            # Initialize session in memory systems
            await self.conversation_memory.create_session(session_id, user_id)
            await self.user_memory.update_session_history(user_id, session_id)
            
            # Start analytics tracking
            await self.analytics_engine.start_session_tracking(session_id, user_profile)
            
            # Create the constellation with curriculum integration
            await self.constellation_manager.create_constellation(
                constellation_type=ConstellationType.GUIDED_LEARNING,  # Start with guided learning
                user_profile=user_profile,
                framework=framework,
                module_id=learning_path.current_module_id if learning_path.modules else "intro",
                session_id=session_id,
                learning_path_id=learning_path.path_id
            )
            
            # Add welcome message based on curriculum
            if current_module := learning_path.get_current_module():
                welcome_message = (
                    f"Welcome to your personalized learning session for {framework.value}!\n\n"
                    f"We'll start with: {current_module.title}\n"
                    f"{current_module.description}\n\n"
                    f"Your learning objectives:\n"
                )
                for i, obj in enumerate(current_module.objectives[:3]):
                    welcome_message += f"{i+1}. {obj.description}\n"
                
                welcome_message += "\nWhat would you like to learn first?"
                
                # Add to constellation state
                constellation_state.add_message(AIMessage(content=welcome_message))
            
            logger.info(f"Created curriculum-driven learning session {session_id} for user {user_id}")
            return session_id
            
        except Exception as e:
            logger.error(f"Failed to create learning session: {e}")
            raise
    
    async def process_learning_interaction(
        self,
        session_id: str,
        user_message: str,
        user_profile: UserProfile
    ) -> Dict[str, Any]:
        """
        Process a learning interaction with the user.
        
        Args:
            session_id: Session identifier
            user_message: User's message
            user_profile: User's profile
            
        Returns:
            Response with agent output and learning analytics
        """
        try:
            # Check if session exists
            if session_id not in self.active_sessions:
                return {
                    "error": f"Session {session_id} not found",
                    "success": False
                }
                
            # Get the session
            session = self.active_sessions[session_id]
            
            # Update last activity timestamp
            session.last_activity = datetime.now()
            
            # Process user intent through agent manager
            intent_analysis = await self.intelligent_agent_manager.analyze_user_intent(
                user_message, 
                session.framework,
                user_profile
            )
            
            # Extract learning context from knowledge graph
            context_nodes = await self.knowledge_graph_manager.get_relevant_context(
                user_message,
                session.framework,
                limit=5
            )
            
            # Determine if this is a quiz/assessment request
            is_assessment_request = any(keyword in user_message.lower() 
                                       for keyword in ["quiz", "test", "assess", "evaluate"])
            
            # Handle quiz/assessment request
            if is_assessment_request:
                # Get the current topic from the session
                current_topic = session.constellation_state.current_topic or session.constellation_state.module_id
                
                # Process through assessment agent
                from ..agents.assessment_agent import AssessmentAgent
                assessment_agent = AssessmentAgent()
                
                # Generate a quiz for the current topic
                quiz_data = await assessment_agent.generate_quiz_for_topic(
                    session.constellation_state, 
                    topic=current_topic
                )
                
                # Format quiz for display
                quiz_message = f"Here's a quiz on {quiz_data['topic']} ({quiz_data['difficulty']} level):\n\n"
                
                for i, question in enumerate(quiz_data['questions']):
                    quiz_message += f"{i+1}. {question['question']}\n"
                    for option, text in question['options'].items():
                        quiz_message += f"   {option}. {text}\n"
                    quiz_message += "\n"
                
                # Add instructions
                quiz_message += "Please answer with the letter of your choice for each question."
                
                # Update session state
                session.constellation_state.add_message(AIMessage(content=quiz_message))
                
                return {
                    "response": quiz_message,
                    "session_id": session_id,
                    "success": True,
                    "is_quiz": True,
                    "quiz_data": quiz_data
                }
            
            # Check if this is a quiz answer
            is_quiz_answer = False
            quiz_answers = {}
            
            if "current_quiz" in session.constellation_state.session_context:
                # Simple pattern matching for quiz answers
                answer_pattern = r"(?:^|\n)(\d+)[.:\)]?\s*([A-D])"
                import re
                matches = re.findall(answer_pattern, user_message, re.IGNORECASE)
                
                if matches:
                    is_quiz_answer = True
                    for question_num, answer in matches:
                        quiz_answers[int(question_num) - 1] = answer.upper()
            
            # Handle quiz answer
            if is_quiz_answer and quiz_answers:
                # Process through assessment agent
                from ..agents.assessment_agent import AssessmentAgent
                assessment_agent = AssessmentAgent()
                
                # Evaluate the quiz responses
                evaluation = await assessment_agent.evaluate_quiz_response(
                    session.constellation_state,
                    quiz_answers
                )
                
                # Format evaluation response
                response = f"Quiz Results: {evaluation['correct_count']}/{evaluation['total_questions']} correct ({evaluation['score']:.0%})\n\n"
                
                for feedback in evaluation['feedback']:
                    question_idx = feedback['question_index']
                    question_num = question_idx + 1
                    response += f"Question {question_num}: {'✓' if feedback['correct'] else '✗'} {feedback['feedback']}\n\n"
                
                # Add next steps based on score
                if evaluation['score'] >= 0.7:
                    response += "\nGreat job! You've demonstrated good understanding of this topic."
                    
                    # Check if we should advance in curriculum
                    if session.current_path and session.constellation_state.curriculum_path_id:
                        # Advance in curriculum
                        advanced = session.constellation_state.advance_curriculum()
                        
                        if advanced:
                            # Update current module in learning path if needed
                            if session.constellation_state.current_objective_index == 0:
                                session.current_path.current_module_id = session.constellation_state.module_id
                            
                            # Add advancement message
                            response += "\n\nYou've completed this learning objective. Let's move on to the next topic!"
                else:
                    response += "\nLet's review some of these concepts before moving forward."
                
                # Update analytics
                await self.analytics_engine.update_comprehension_metrics(
                    session_id=session_id,
                    constellation_state=session.constellation_state,
                    assessment_data={
                        'correct_answers': evaluation['correct_count'],
                        'total_questions': evaluation['total_questions'],
                        'confidence_indicators': [1.0 if f['correct'] else 0.0 for f in evaluation['feedback']]
                    }
                )
                
                # Update session state
                session.constellation_state.add_message(AIMessage(content=response))
                
                return {
                    "response": response,
                    "session_id": session_id,
                    "success": True,
                    "is_quiz_result": True,
                    "evaluation": evaluation
                }
            
            # For regular interactions, process through constellation
            # Run the constellation with the user message
            updated_state = await self.constellation_manager.run_session(
                session_id=session_id,
                user_message=user_message,
                user_profile=user_profile,
                framework=session.framework,
                module_id=session.current_path.current_module_id
            )
            
            # Update the session's constellation state
            session.constellation_state = updated_state
            
            # Extract the response from the last AI message
            response = ""
            for msg in reversed(updated_state.messages):
                if isinstance(msg, AIMessage):
                    response = msg.content
                    break
            
            # Update learning analytics
            await self.analytics_engine.update_engagement_metrics(
                session_id=session_id,
                constellation_state=updated_state,
                interaction_data={
                    "message_length": len(user_message),
                    "response_time": 0.0,  # Would be calculated from request timestamp
                    "session_duration": (datetime.now() - session.start_time).total_seconds() / 60.0
                }
            )
            
            # Check if curriculum needs adaptation based on engagement
            if session.current_path:
                should_adapt = await self.adaptive_learning_engine.should_adapt_path(
                    learning_path=session.current_path,
                    constellation_state=updated_state,
                    context=None  # TODO: Define IntelligentContext
                )
                
                if should_adapt:
                    # Adapt the learning path
                    adapted_path = await self.adaptive_learning_engine.adapt_learning_path(
                        learning_path=session.current_path,
                        constellation_state=updated_state,
                        user_profile=user_profile
                    )
                    
                    # Update the session with the adapted path
                    session.current_path = adapted_path
                    
                    # Log adaptation
                    logger.info(f"Adapted learning path for session {session_id}")
            
            # Generate learning insights
            insights = await self.analytics_engine.generate_performance_insights(session_id)
            
            # Generate recommendations based on insights
            recommendations = await self._generate_learning_recommendations(session)
            
            return {
                "response": response,
                "session_id": session_id,
                "success": True,
                "insights": [insight.__dict__ for insight in insights],
                "recommendations": recommendations
            }
            
        except Exception as e:
            logger.error(f"Error processing learning interaction: {e}")
            return {
                "error": f"Failed to process interaction: {str(e)}",
                "success": False
            }
    
    async def get_learning_analytics(self, session_id: str) -> Dict[str, Any]:
        """Get comprehensive learning analytics for a session."""
        if session_id not in self.active_sessions:
            raise ValueError(f"Session {session_id} not found")
        
        session = self.active_sessions[session_id]
        
        # Get analytics from analytics engine
        analytics = await self.analytics_engine.get_session_analytics(session_id)
        
        # Get knowledge graph insights
        knowledge_insights = await self.knowledge_graph_manager.get_learning_insights(
            session.framework,
            session.constellation_state.topics_covered
        )
        
        # Get memory insights
        conversation_insights = await self.conversation_memory.get_session_insights(session_id)
        
        return {
            "session_overview": {
                "session_id": session_id,
                "duration": (datetime.now() - session.start_time).total_seconds() / 60,
                "effectiveness_score": session.effectiveness_score,
                "completion_percentage": session.completion_percentage,
                "modality": session.modality.value
            },
            "learning_progress": analytics,
            "knowledge_insights": knowledge_insights,
            "conversation_insights": conversation_insights,
            "recommendations": await self._generate_learning_recommendations(session)
        }
    
    async def optimize_learning_experience(self, session_id: str) -> Dict[str, Any]:
        """Optimize the learning experience based on current performance."""
        if session_id not in self.active_sessions:
            raise ValueError(f"Session {session_id} not found")
        
        self.hub_state = HubState.OPTIMIZING
        session = self.active_sessions[session_id]
        
        try:
            # Analyze current performance
            performance_analysis = await self.analytics_engine.analyze_learning_performance(session_id)
            
            # Get optimization recommendations
            optimizations = await self.adaptive_learning_engine.get_optimization_recommendations(
                session.current_path,
                session.constellation_state,
                performance_analysis
            )
            
            # Apply optimizations
            optimization_results = []
            for optimization in optimizations:
                result = await self._apply_optimization(session, optimization)
                optimization_results.append(result)
            
            self.hub_state = HubState.IDLE
            
            return {
                "optimizations_applied": optimization_results,
                "expected_improvements": optimizations,
                "new_effectiveness_prediction": performance_analysis.get("predicted_effectiveness", 0.0)
            }
            
        except Exception as e:
            logger.error(f"Error optimizing learning experience: {e}")
            self.hub_state = HubState.IDLE
            raise
    
    def _determine_optimal_modality(self, user_profile: UserProfile) -> LearningModality:
        """Determine optimal learning modality based on user profile."""
        # Simple heuristic based on learning style
        style_mapping = {
            "visual": LearningModality.VISUAL,
            "hands_on": LearningModality.KINESTHETIC,
            "theoretical": LearningModality.READING_WRITING,
            "mixed": LearningModality.MULTIMODAL
        }
        
        return style_mapping.get(
            user_profile.preferred_learning_style.value,
            LearningModality.MULTIMODAL
        )
    
    async def _generate_learning_recommendations(self, session: LearningSession) -> List[str]:
        """Generate personalized learning recommendations."""
        recommendations = []
        
        # Based on effectiveness score
        if session.effectiveness_score < 0.6:
            recommendations.append("Consider switching to a more hands-on learning approach")
            recommendations.append("Take more frequent breaks to improve retention")
        
        # Based on progress
        if session.completion_percentage < 30 and (datetime.now() - session.start_time).total_seconds() > 3600:
            recommendations.append("Focus on smaller, more achievable learning goals")
        
        # Based on engagement
        if session.constellation_state.engagement_level < 0.5:
            recommendations.append("Try interactive coding exercises to boost engagement")
            recommendations.append("Consider exploring real-world applications of concepts")
        
        return recommendations
    
    async def _apply_optimization(self, session: LearningSession, optimization: Dict[str, Any]) -> Dict[str, Any]:
        """Apply a specific optimization to the learning session."""
        optimization_type = optimization.get("type")
        
        if optimization_type == "modality_switch":
            new_modality = LearningModality(optimization["target_modality"])
            session.modality = new_modality
            return {"type": "modality_switch", "new_modality": new_modality.value, "applied": True}
        
        elif optimization_type == "constellation_adaptation":
            new_constellation = ConstellationType(optimization["target_constellation"])
            # This would trigger constellation adaptation in the next interaction
            return {"type": "constellation_adaptation", "target": new_constellation.value, "applied": True}
        
        elif optimization_type == "learning_path_adjustment":
            # Adjust learning path based on optimization parameters
            await self.adaptive_learning_engine.adjust_learning_path(
                session.current_path,
                optimization["adjustments"]
            )
            return {"type": "learning_path_adjustment", "adjustments": optimization["adjustments"], "applied": True}
        
        return {"type": optimization_type, "applied": False, "reason": "Unknown optimization type"}
    
    async def end_session(self, session_id: str) -> Dict[str, Any]:
        """End a learning session and generate summary."""
        if session_id not in self.active_sessions:
            raise ValueError(f"Session {session_id} not found")
        
        session = self.active_sessions[session_id]
        
        # Generate session summary
        summary = await self.analytics_engine.generate_session_summary(session_id)
        
        # Update user memory with session results
        await self.user_memory.update_learning_history(
            session.user_id,
            session.framework,
            session.effectiveness_score,
            session.completion_percentage
        )
        
        # Update global metrics
        if session.completion_percentage >= 80:
            self.successful_completions += 1
        
        # Calculate new average effectiveness
        total_effectiveness = self.average_effectiveness * (self.total_sessions - 1) + session.effectiveness_score
        self.average_effectiveness = total_effectiveness / self.total_sessions
        
        # Clean up session
        del self.active_sessions[session_id]
        
        return summary
    
    def get_hub_status(self) -> Dict[str, Any]:
        """Get current hub status and metrics."""
        return {
            "hub_state": self.hub_state.value,
            "active_sessions": len(self.active_sessions),
            "total_sessions": self.total_sessions,
            "successful_completions": self.successful_completions,
            "success_rate": self.successful_completions / max(1, self.total_sessions),
            "average_effectiveness": self.average_effectiveness,
            "components_status": {
                "constellation_manager": "active",
                "adaptive_learning_engine": "active",
                "knowledge_graph_manager": "active",
                "analytics_engine": "active"
            }
        } 