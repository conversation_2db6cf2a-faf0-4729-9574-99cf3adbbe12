"""
Motivational Coach Agent - Provides motivation, encouragement, and learning support.
"""

from typing import Optional
from langchain_core.messages import HumanMessage
from langchain_core.language_models.chat_models import BaseChatModel

from .base_agent import BaseAgent
from ..core.constellation import ConstellationState, AgentRole


class MotivationalCoachAgent(BaseAgent):
    """Provides motivation, encouragement, and learning support."""
    
    def __init__(self, model: Optional[BaseChatModel] = None):
        super().__init__(AgentRole.MOTIVATIONAL_COACH, model, "motivation")
    
    def get_system_prompt(self, state: ConstellationState) -> str:
        return f"""You are a motivational coach for {state.framework.value} learning.

Current Context:
- Framework: {state.framework.value}
- Progress: {state.progress_percentage:.1f}%
- Engagement: {state.engagement_level:.1f}
- Recent Feedback: {state.user_feedback_scores[-1] if state.user_feedback_scores else 'No recent feedback'}

Your role is to:
1. Provide positive reinforcement and encouragement
2. Help overcome learning obstacles and setbacks
3. Celebrate achievements and progress
4. Maintain motivation during challenging periods
5. Foster confidence and self-efficacy
6. Connect learning to personal and professional goals

Be enthusiastic, supportive, and genuinely encouraging while maintaining authenticity.

Respond as the motivational coach with inspiring, confidence-building support."""
    
    def should_activate(self, state: ConstellationState) -> bool:
        # Activate when engagement is low or user seems discouraged
        if state.engagement_level < 0.3:
            return True
        
        if state.user_feedback_scores and state.user_feedback_scores[-1] < 3.0:
            return True
        
        if not state.messages:
            return False
        
        latest_message = state.messages[-1]
        if isinstance(latest_message, HumanMessage):
            content_lower = latest_message.content.lower()
            motivation_keywords = ["difficult", "hard", "frustrated", "give up", "discouraged", "struggling"]
            return any(keyword in content_lower for keyword in motivation_keywords)
        
        return False 