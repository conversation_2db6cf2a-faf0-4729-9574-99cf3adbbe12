"""
Main FastAPI application for the GAAPF - Guidance AI Agent for Python Framework.
Provides REST API endpoints for all system functionality.
"""

import logging
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn

from ...config.settings import settings
from ...core.models import llm_manager
from ...memory.conversation_memory import memory_manager
from .routes import (
    chat_router,
    constellation_router,
    memory_router,
    framework_router,
    user_router
)

logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan management."""
    # Startup
    logger.info("Starting GAAPF - Guidance AI Agent API")
    
    # Initialize core components
    try:
        # Test LLM connection
        model = llm_manager.get_default_model()
        logger.info(f"Initialized with default model: {type(model).__name__}")
        
        # Initialize memory manager
        logger.info("Memory manager initialized")
        
        yield
        
    except Exception as e:
        logger.error(f"Failed to initialize application: {e}")
        raise
    
    # Shutdown
    logger.info("Shutting down GAAPF - Guidance AI Agent API")


def create_app() -> FastAPI:
    """Create and configure the FastAPI application."""
    
    app = FastAPI(
        title="GAAPF - Guidance AI Agent API",
        description="""
        Adaptive multi-agent learning system for AI framework education.
        
        ## Features
        
        * **Adaptive Learning Constellation**: Dynamic multi-agent system with 12 specialized agents
        * **Temporal Optimization**: Machine learning-powered effectiveness tracking
        * **Multi-Framework Support**: LangChain, LangGraph, FastAPI, Streamlit, and more
        * **Intelligent Memory Management**: Context-aware conversation memory
        * **Real-time Progress Tracking**: Comprehensive learning analytics
        
        ## Constellation Types
        
        * Knowledge Intensive
        * Hands-on Focused  
        * Theory-Practice Balanced
        * Troubleshooting Specialized
        * Research Oriented
        * Project Guided
        * Assessment Focused
        * Mentorship Enhanced
        
        Built with LangChain 0.3+ and LangGraph 0.4+ for production-ready AI education.
        """,
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc",
        lifespan=lifespan
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Configure appropriately for production
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Include routers
    app.include_router(chat_router, prefix="/api/v1/chat", tags=["Chat"])
    app.include_router(constellation_router, prefix="/api/v1/constellation", tags=["Constellation"])
    app.include_router(memory_router, prefix="/api/v1/memory", tags=["Memory"])
    app.include_router(framework_router, prefix="/api/v1/frameworks", tags=["Frameworks"])
    app.include_router(user_router, prefix="/api/v1/users", tags=["Users"])
    
    # Health check endpoint
    @app.get("/health", tags=["Health"])
    async def health_check():
        """Health check endpoint."""
        try:
            # Check LLM availability
            model_available = llm_manager.get_default_model() is not None
            
            # Check memory manager
            memory_available = memory_manager is not None
            
            return {
                "status": "healthy" if model_available and memory_available else "unhealthy",
                "services": {
                    "llm_manager": "up" if model_available else "down",
                    "memory_manager": "up" if memory_available else "down"
                },
                "version": "1.0.0"
            }
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return JSONResponse(
                status_code=503,
                content={
                    "status": "unhealthy",
                    "error": str(e),
                    "version": "1.0.0"
                }
            )
    
    # Root endpoint
    @app.get("/", tags=["Root"])
    async def root():
        """Root endpoint with API information."""
        return {
            "message": "GAAPF - Guidance AI Agent API",
            "version": "1.0.0",
            "docs_url": "/docs",
            "health_url": "/health",
            "description": "Adaptive multi-agent learning system for AI framework education"
        }
    
    # Global exception handler
    @app.exception_handler(Exception)
    async def global_exception_handler(request, exc: Exception):
        """Global exception handler."""
        logger.error(f"Unhandled exception: {exc}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={
                "error": "Internal server error",
                "message": str(exc) if settings.debug else "An unexpected error occurred",
                "type": type(exc).__name__
            }
        )
    
    return app


# Create the app instance
app = create_app()


def get_app() -> FastAPI:
    """Get the FastAPI application instance."""
    return app


def run_server(
    host: str = settings.host,
    port: int = settings.api_port,
    reload: bool = settings.debug,
    log_level: str = settings.log_level.lower()
):
    """Run the FastAPI server."""
    uvicorn.run(
        "src.pyframeworks_assistant.interfaces.api.main:app",
        host=host,
        port=port,
        reload=reload,
        log_level=log_level,
        access_log=True
    )


if __name__ == "__main__":
    run_server() 