#!/usr/bin/env python3
"""
Streamlit Web Interface for GAAPF - Guidance AI Agent for Python Framework
Demo application showcasing the adaptive learning constellation system.
"""

import streamlit as st
import sys
import os
from datetime import datetime
from typing import Optional
import uuid
import asyncio

# Add src to path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, "..", "..", "..")
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

from pyframeworks_assistant.config.settings import settings
from pyframeworks_assistant.config.user_profiles import (
    UserProfile, SkillLevel, LearningPace, LearningStyle
)
from pyframeworks_assistant.config.framework_configs import (
    SupportedFrameworks, get_all_frameworks, get_framework_config
)
from pyframeworks_assistant.core.constellation import (
    ConstellationType, ConstellationManager, ConstellationState
)
from pyframeworks_assistant.core.temporal_state import TemporalStateManager

# Page config
st.set_page_config(
    page_title="GAAPF - Guidance AI Agent",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
.main-header {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1f77b4;
    text-align: center;
    margin-bottom: 2rem;
}

.constellation-card {
    border: 1px solid #e0e0e0;
    border-radius: 10px;
    padding: 1rem;
    margin: 0.5rem 0;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.metric-card {
    background: #f0f2f6;
    padding: 1rem;
    border-radius: 8px;
    text-align: center;
}

.framework-badge {
    background: #1f77b4;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    margin: 0.2rem;
    display: inline-block;
}
</style>
""", unsafe_allow_html=True)

def initialize_session_state():
    """Initialize Streamlit session state."""
    if 'user_profile' not in st.session_state:
        st.session_state.user_profile = None
    if 'constellation_manager' not in st.session_state:
        st.session_state.constellation_manager = ConstellationManager()
    if 'temporal_manager' not in st.session_state:
        st.session_state.temporal_manager = TemporalStateManager()
    if 'current_session_id' not in st.session_state:
        st.session_state.current_session_id = None
    if 'chat_history' not in st.session_state:
        st.session_state.chat_history = []

def create_user_profile_form():
    """Create user profile setup form."""
    st.subheader("👤 User Profile Setup")
    
    with st.form("user_profile_form"):
        col1, col2 = st.columns(2)
        
        with col1:
            user_id = st.text_input("User ID", value=f"user_{uuid.uuid4().hex[:8]}")
            programming_years = st.slider("Programming Experience (years)", 0, 20, 2)
            python_skill = st.selectbox(
                "Python Skill Level",
                options=[skill.value for skill in SkillLevel],
                index=1
            )
        
        with col2:
            learning_pace = st.selectbox(
                "Learning Pace",
                options=[pace.value for pace in LearningPace],
                index=1
            )
            learning_style = st.selectbox(
                "Learning Style",
                options=[style.value for style in LearningStyle],
                index=1
            )
        
        learning_goals = st.text_area(
            "Learning Goals (one per line)",
            value="Learn LangChain fundamentals\nBuild RAG applications\nUnderstand agent systems"
        )
        
        submitted = st.form_submit_button("Create Profile")
        
        if submitted:
            goals_list = [goal.strip() for goal in learning_goals.split('\n') if goal.strip()]
            
            profile = UserProfile(
                user_id=user_id,
                programming_experience_years=programming_years,
                python_skill_level=SkillLevel(python_skill),
                learning_pace=LearningPace(learning_pace),
                preferred_learning_style=LearningStyle(learning_style),
                learning_goals=goals_list
            )
            
            st.session_state.user_profile = profile
            st.success("✅ User profile created successfully!")
            st.rerun()

def display_user_profile():
    """Display current user profile."""
    profile = st.session_state.user_profile
    
    st.subheader("👤 Current User Profile")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("User ID", profile.user_id)
        st.metric("Programming Experience", f"{profile.programming_experience_years} years")
    
    with col2:
        st.metric("Python Skill", profile.python_skill_level.value.title())
        st.metric("Learning Pace", profile.learning_pace.value.title())
    
    with col3:
        st.metric("Learning Style", profile.preferred_learning_style.value.replace('_', ' ').title())
        st.metric("Learning Goals", len(profile.learning_goals))
    
    if st.button("📝 Edit Profile"):
        st.session_state.user_profile = None
        st.rerun()

def display_framework_selection():
    """Display framework selection interface."""
    st.subheader("🔧 Framework Selection")
    
    frameworks = get_all_frameworks()
    framework_options = {fw.value: fw for fw in frameworks}
    
    col1, col2 = st.columns([1, 2])
    
    with col1:
        selected_framework = st.selectbox(
            "Choose a Framework",
            options=list(framework_options.keys()),
            format_func=lambda x: x.replace('_', ' ').title()
        )
    
    framework = framework_options[selected_framework]
    config = get_framework_config(framework)
    
    with col2:
        st.markdown(f"""
        <div class="constellation-card">
        <h4>{config.display_name}</h4>
        <p><strong>Version:</strong> {config.latest_version}</p>
        <p><strong>Complexity:</strong> {config.learning_complexity.value.title()}</p>
        <p><strong>Use Cases:</strong> {', '.join(config.primary_use_cases[:3])}</p>
        </div>
        """, unsafe_allow_html=True)
    
    # Learning paths
    st.markdown("### 📚 Available Learning Paths")
    for path in config.learning_paths:
        with st.expander(f"{path.title} ({path.total_estimated_hours}h)"):
            st.write(path.description)
            st.write(f"**Modules:** {len(path.modules)}")
            st.write(f"**Certification:** {'✅' if path.certification_available else '❌'}")
            
            for i, module in enumerate(path.modules, 1):
                st.write(f"{i}. **{module.title}** ({module.estimated_duration} min) - {module.difficulty.value}")
    
    return framework

def get_optimal_constellation(user_profile: UserProfile, framework: SupportedFrameworks):
    """Get optimal constellation for user and framework."""
    temporal_manager = st.session_state.temporal_manager
    
    # Use temporal optimization if available
    optimal_constellation = temporal_manager.get_optimal_constellation(
        user_profile, framework, {}
    )
    
    return optimal_constellation

def display_constellation_recommendation(user_profile: UserProfile, framework: SupportedFrameworks):
    """Display constellation recommendation."""
    st.subheader("🌟 Recommended Learning Constellation")
    
    optimal_constellation = get_optimal_constellation(user_profile, framework)
    
    col1, col2 = st.columns([1, 1])
    
    with col1:
        st.markdown(f"""
        <div class="constellation-card">
        <h4>🎯 {optimal_constellation.value.replace('_', ' ').title()}</h4>
        <p>This constellation is optimized for your learning style and experience level.</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        # Get constellation config from manager
        manager = st.session_state.constellation_manager
        if optimal_constellation in manager.constellation_configs:
            config = manager.constellation_configs[optimal_constellation]
            
            st.write("**Primary Agents:**")
            for agent in config.primary_agents:
                st.write(f"• {agent.value.replace('_', ' ').title()}")
            
            st.write("**Support Agents:**")
            for agent in config.support_agents:
                st.write(f"• {agent.value.replace('_', ' ').title()}")
    
    return optimal_constellation

def display_learning_interface(user_profile: UserProfile, framework: SupportedFrameworks, constellation_type: ConstellationType):
    """Display the main learning interface."""
    st.subheader("💬 Learning Session")
    
    # Initialize session if needed
    if st.session_state.current_session_id is None:
        st.session_state.current_session_id = f"session_{uuid.uuid4().hex[:8]}"
        st.session_state.chat_history = []
    
    # Chat interface
    chat_container = st.container()
    
    with chat_container:
        # Display chat history
        for message in st.session_state.chat_history:
            if message["role"] == "user":
                st.markdown(f"**You:** {message['content']}")
            else:
                agent_role = message.get("agent", "Assistant")
                st.markdown(f"**{agent_role.title()}:** {message['content']}")
        
        # User input
        user_input = st.text_input("Ask a question or request help:", key="user_input")
        
        col1, col2, col3 = st.columns([1, 1, 2])
        
        with col1:
            if st.button("Send Message", type="primary"):
                if user_input:
                    process_user_message(user_input, user_profile, framework, constellation_type)
        
        with col2:
            if st.button("End Session"):
                end_learning_session()
        
        with col3:
            st.write(f"Session ID: {st.session_state.current_session_id}")

def process_user_message(message: str, user_profile: UserProfile, framework: SupportedFrameworks, constellation_type: ConstellationType):
    """Process user message (demo implementation)."""
    # Add user message to history
    st.session_state.chat_history.append({
        "role": "user",
        "content": message
    })
    
    # Simulate constellation response (since we don't have LLM configured)
    demo_responses = {
        ConstellationType.KNOWLEDGE_INTENSIVE: {
            "role": "assistant",
            "agent": "Instructor",
            "content": f"Great question about {framework.value}! Let me provide you with a comprehensive explanation. This framework is particularly useful for building AI applications. Would you like me to walk you through the core concepts step by step?"
        },
        ConstellationType.HANDS_ON_FOCUSED: {
            "role": "assistant",
            "agent": "Code Assistant", 
            "content": f"Let's dive into some practical {framework.value} code! Here's a simple example to get you started:\n\n```python\n# Basic {framework.value} example\nfrom {framework.value} import ...\n# Your code here\n```\n\nWould you like me to explain this code or show you more examples?"
        },
        ConstellationType.THEORY_PRACTICE_BALANCED: {
            "role": "assistant",
            "agent": "Knowledge Synthesizer",
            "content": f"Excellent question! Let me combine theory and practice for {framework.value}. First, let's understand the concept, then we'll see it in action with a practical example. This balanced approach will help you understand both the 'why' and the 'how'."
        }
    }
    
    response = demo_responses.get(constellation_type, {
        "role": "assistant",
        "agent": "Assistant",
        "content": "I understand your question. In a fully configured system, the constellation of specialized agents would provide you with personalized, adaptive responses based on your learning profile."
    })
    
    # Add response to history
    st.session_state.chat_history.append(response)
    
    # Clear input and rerun
    st.rerun()

def end_learning_session():
    """End the current learning session."""
    st.session_state.current_session_id = None
    st.session_state.chat_history = []
    st.success("Session ended. Starting a new session...")
    st.rerun()

def display_system_status():
    """Display system status in sidebar."""
    st.sidebar.markdown("### 🔧 System Status")
    
    # Check LLM availability
    llm_config = settings.get_llm_config()
    if llm_config:
        st.sidebar.success(f"✅ LLM Available ({len(llm_config)} models)")
    else:
        st.sidebar.warning("⚠️ No LLM configured")
        st.sidebar.info("Add API keys to .env file to enable full functionality")
    
    # Check database connections
    st.sidebar.info("💾 Using in-memory storage (demo mode)")
    
    # System metrics
    frameworks_count = len(get_all_frameworks())
    constellation_types = len(ConstellationType)
    
    st.sidebar.metric("Supported Frameworks", frameworks_count)
    st.sidebar.metric("Constellation Types", constellation_types)

def main():
    """Main Streamlit application."""
    # Initialize session state
    initialize_session_state()
    
    # Header
    st.markdown('<h1 class="main-header">🤖 GAAPF - Guidance AI Agent for Python Framework</h1>', unsafe_allow_html=True)
    st.markdown("### Adaptive Multi-Agent Learning System with Temporal Optimization")
    
    # Sidebar system status
    display_system_status()
    
    # Main content
    if st.session_state.user_profile is None:
        # Show profile creation form
        create_user_profile_form()
    else:
        # Show main application
        display_user_profile()
        
        st.markdown("---")
        
        # Framework selection
        selected_framework = display_framework_selection()
        
        st.markdown("---")
        
        # Constellation recommendation
        optimal_constellation = display_constellation_recommendation(
            st.session_state.user_profile, 
            selected_framework
        )
        
        st.markdown("---")
        
        # Learning interface
        display_learning_interface(
            st.session_state.user_profile,
            selected_framework, 
            optimal_constellation
        )
    
    # Footer
    st.markdown("---")
    st.markdown("""
    <div style="text-align: center; color: #666; padding: 1rem;">
    <p>GAAPF - Guidance AI Agent v0.1.0 | 
    Powered by LangChain & LangGraph | 
    <a href="https://github.com/your-repo" target="_blank">Source Code</a></p>
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main() 