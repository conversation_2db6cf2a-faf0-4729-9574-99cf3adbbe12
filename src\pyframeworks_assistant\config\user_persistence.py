"""
User Profile Persistence System
Handles saving and loading user profiles to/from JSON files for personal study sessions.
"""

import os
import json
import logging
from typing import Optional, Dict, Any
from datetime import datetime
from pathlib import Path

from .user_profiles import UserProfile, SkillLevel, LearningPace, LearningStyle

logger = logging.getLogger(__name__)


class UserProfilePersistence:
    """Manages saving and loading user profiles from JSON files."""
    
    def __init__(self, profiles_dir: str = "user_profiles"):
        """Initialize the persistence manager."""
        self.profiles_dir = Path(profiles_dir)
        self.profiles_dir.mkdir(exist_ok=True)
        logger.info(f"User profiles directory: {self.profiles_dir.absolute()}")
    
    def get_profile_path(self, user_id: str) -> Path:
        """Get the file path for a user's profile."""
        return self.profiles_dir / f"{user_id}.json"
    
    def profile_exists(self, user_id: str) -> bool:
        """Check if a user profile exists."""
        return self.get_profile_path(user_id).exists()
    
    def save_profile(self, profile: UserProfile) -> bool:
        """
        Save user profile to JSON file.
        
        Args:
            profile: UserProfile to save
            
        Returns:
            bool: True if saved successfully, False otherwise
        """
        try:
            profile_path = self.get_profile_path(profile.user_id)
            
            # Convert profile to dict
            profile_data = {
                "user_id": profile.user_id,
                "programming_experience_years": profile.programming_experience_years,
                "python_skill_level": profile.python_skill_level.value,
                "learning_pace": profile.learning_pace.value,
                "preferred_learning_style": profile.preferred_learning_style.value,
                "learning_goals": profile.learning_goals,
                "created_at": profile.created_at.isoformat(),
                "last_updated": datetime.now().isoformat(),
                
                # Preferences
                "preferences": {
                    "notification_frequency": profile.preferences.notification_frequency.value,
                    "difficulty_progression": profile.preferences.difficulty_progression.value,
                    "feedback_style": profile.preferences.feedback_style.value,
                    "code_complexity": profile.preferences.code_complexity,
                    "interactive_mode": profile.preferences.interactive_mode,
                    "save_progress": profile.preferences.save_progress
                },
                
                # Progress
                "total_learning_time": profile.progress.total_learning_time,
                "current_streak": profile.progress.current_streak,
                "completed_modules": profile.progress.completed_modules,
                "current_framework": profile.progress.current_framework,
                "achievements": profile.progress.achievements,
                
                # Session history (last 10 sessions to keep file size manageable)
                "recent_sessions": [
                    {
                        "session_id": session.session_id,
                        "start_time": session.start_time.isoformat(),
                        "end_time": session.end_time.isoformat() if session.end_time else None,
                        "framework": session.framework,
                        "topics_covered": session.topics_covered,
                        "completion_percentage": session.completion_percentage,
                        "effectiveness_score": session.effectiveness_score,
                        "user_satisfaction": session.user_satisfaction,
                        "constellation_used": session.constellation_used,
                        "notes": session.notes
                    }
                    for session in profile.progress.learning_session_history[-10:]  # Last 10 sessions
                ]
            }
            
            # Save to file
            with open(profile_path, 'w', encoding='utf-8') as f:
                json.dump(profile_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"User profile saved: {profile.user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save user profile {profile.user_id}: {e}")
            return False
    
    def load_profile(self, user_id: str) -> Optional[UserProfile]:
        """
        Load user profile from JSON file.
        
        Args:
            user_id: User ID to load
            
        Returns:
            UserProfile if found, None otherwise
        """
        try:
            profile_path = self.get_profile_path(user_id)
            
            if not profile_path.exists():
                logger.info(f"No profile found for user: {user_id}")
                return None
            
            with open(profile_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Create UserProfile from saved data
            profile = UserProfile(
                user_id=data["user_id"],
                programming_experience_years=data["programming_experience_years"],
                python_skill_level=SkillLevel(data["python_skill_level"]),
                learning_pace=LearningPace(data["learning_pace"]),
                preferred_learning_style=LearningStyle(data["preferred_learning_style"]),
                learning_goals=data["learning_goals"],
                created_at=datetime.fromisoformat(data["created_at"])
            )
            
            # Update preferences
            if "preferences" in data:
                from .user_profiles import NotificationFrequency, DifficultyProgression, FeedbackStyle
                prefs = data["preferences"]
                profile.preferences.notification_frequency = NotificationFrequency(prefs.get("notification_frequency", "daily"))
                profile.preferences.difficulty_progression = DifficultyProgression(prefs.get("difficulty_progression", "gradual"))
                profile.preferences.feedback_style = FeedbackStyle(prefs.get("feedback_style", "encouraging"))
                profile.preferences.code_complexity = prefs.get("code_complexity", "medium")
                profile.preferences.interactive_mode = prefs.get("interactive_mode", True)
                profile.preferences.save_progress = prefs.get("save_progress", True)
            
            # Update progress
            profile.progress.total_learning_time = data.get("total_learning_time", 0.0)
            profile.progress.current_streak = data.get("current_streak", 0)
            profile.progress.completed_modules = data.get("completed_modules", [])
            profile.progress.current_framework = data.get("current_framework")
            profile.progress.achievements = data.get("achievements", [])
            
            # Load recent sessions
            if "recent_sessions" in data:
                from .user_profiles import LearningSession
                for session_data in data["recent_sessions"]:
                    session = LearningSession(
                        session_id=session_data["session_id"],
                        start_time=datetime.fromisoformat(session_data["start_time"]),
                        end_time=datetime.fromisoformat(session_data["end_time"]) if session_data.get("end_time") else None,
                        framework=session_data["framework"],
                        topics_covered=session_data["topics_covered"],
                        completion_percentage=session_data["completion_percentage"],
                        effectiveness_score=session_data["effectiveness_score"],
                        user_satisfaction=session_data["user_satisfaction"],
                        constellation_used=session_data["constellation_used"],
                        notes=session_data.get("notes")
                    )
                    profile.progress.learning_session_history.append(session)
            
            logger.info(f"User profile loaded: {user_id}")
            return profile
            
        except Exception as e:
            logger.error(f"Failed to load user profile {user_id}: {e}")
            return None
    
    def list_profiles(self) -> list[str]:
        """List all available user profiles."""
        try:
            profile_files = list(self.profiles_dir.glob("*.json"))
            return [f.stem for f in profile_files]
        except Exception as e:
            logger.error(f"Failed to list profiles: {e}")
            return []
    
    def delete_profile(self, user_id: str) -> bool:
        """
        Delete a user profile.
        
        Args:
            user_id: User ID to delete
            
        Returns:
            bool: True if deleted successfully, False otherwise
        """
        try:
            profile_path = self.get_profile_path(user_id)
            if profile_path.exists():
                profile_path.unlink()
                logger.info(f"User profile deleted: {user_id}")
                return True
            else:
                logger.warning(f"Profile not found for deletion: {user_id}")
                return False
        except Exception as e:
            logger.error(f"Failed to delete user profile {user_id}: {e}")
            return False
    
    def get_profile_summary(self, user_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a summary of user profile without loading the full profile.
        
        Args:
            user_id: User ID
            
        Returns:
            Dict with profile summary or None if not found
        """
        try:
            profile_path = self.get_profile_path(user_id)
            
            if not profile_path.exists():
                return None
            
            with open(profile_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Extract frameworks learned from session history
            frameworks_learned = list(set(
                session.get("framework", "") 
                for session in data.get("recent_sessions", [])
                if session.get("framework")
            ))
            
            return {
                "user_id": data["user_id"],
                "python_skill_level": data["python_skill_level"],
                "learning_pace": data["learning_pace"],
                "preferred_learning_style": data["preferred_learning_style"],
                "total_sessions": len(data.get("recent_sessions", [])),
                "frameworks_learned": frameworks_learned,
                "completed_modules": data.get("completed_modules", []),
                "achievements": data.get("achievements", []),
                "last_updated": data.get("last_updated", data.get("created_at"))
            }
            
        except Exception as e:
            logger.error(f"Failed to get profile summary for {user_id}: {e}")
            return None


# Global instance
user_persistence = UserProfilePersistence()


def save_user_profile(profile: UserProfile) -> bool:
    """Save user profile using global persistence manager."""
    return user_persistence.save_profile(profile)


def load_user_profile(user_id: str) -> Optional[UserProfile]:
    """Load user profile using global persistence manager."""
    return user_persistence.load_profile(user_id)


def user_profile_exists(user_id: str) -> bool:
    """Check if user profile exists using global persistence manager."""
    return user_persistence.profile_exists(user_id)


def get_user_profile_summary(user_id: str) -> Optional[Dict[str, Any]]:
    """Get user profile summary using global persistence manager."""
    return user_persistence.get_profile_summary(user_id) 