"""
Framework Initialization Service
Provides comprehensive context about AI frameworks by dynamically fetching
current information from official documentation and repositories using Tavily search.
"""

import asyncio
import json
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from enum import Enum

from ..config.framework_configs import SupportedFrameworks, get_framework_config
from ..tools.search_tools import TavilySearchTool, TavilyExtractTool, TavilyCrawlTool

import logging
logger = logging.getLogger(__name__)


@dataclass
class FrameworkSyntax:
    """Framework syntax patterns and examples."""
    basic_patterns: List[str]
    advanced_patterns: List[str]
    common_imports: List[str]
    boilerplate_code: str
    naming_conventions: Dict[str, str]
    source_urls: List[str]


@dataclass
class CoreModule:
    """Core module information."""
    name: str
    description: str
    primary_functions: List[str]
    usage_examples: List[str]
    best_practices: List[str]
    common_pitfalls: List[str]
    source_urls: List[str]


@dataclass
class PracticeExample:
    """Practical example with explanation."""
    title: str
    description: str
    difficulty_level: str
    code_example: str
    explanation: str
    concepts_demonstrated: List[str]
    next_steps: List[str]
    source_urls: List[str]


@dataclass
class FrameworkContext:
    """Comprehensive framework context for agent guidance."""
    framework: SupportedFrameworks
    version: str
    description: str
    key_concepts: List[str]
    syntax: FrameworkSyntax
    core_modules: List[CoreModule]
    practice_examples: List[PracticeExample]
    learning_path: List[str]
    common_use_cases: List[str]
    integration_patterns: List[str]
    troubleshooting_guide: Dict[str, str]
    resources: Dict[str, str]
    created_at: datetime
    source_urls: List[str]
    

class DynamicFrameworkInitializer:
    """Service for initializing comprehensive framework context using dynamic web search."""
    
    def __init__(self):
        """Initialize the framework initializer with Tavily search tools."""
        self.context_cache: Dict[SupportedFrameworks, FrameworkContext] = {}
        self.search_tool = TavilySearchTool()
        self.extract_tool = TavilyExtractTool()
        self.crawl_tool = TavilyCrawlTool()
        
    async def initialize(self) -> None:
        """Initialize the framework initializer."""
        # Import here to avoid circular imports
        from ..core.vector_storage import vector_storage
        self.vector_storage = vector_storage
        
    async def initialize_framework_context(self, framework: SupportedFrameworks) -> FrameworkContext:
        """Generate comprehensive framework context using dynamic web search."""
        
        # Check cache first
        if framework in self.context_cache:
            logger.info(f"Using cached context for {framework.value}")
            return self.context_cache[framework]
        
        # Try to load from vector database
        context = await self.load_framework_context_from_vectordb(framework)
        if context:
            logger.info(f"Loaded context for {framework.value} from vector database")
            self.context_cache[framework] = context
            return context
        
        try:
            logger.info(f"Generating dynamic context for {framework.value}")
            
            # Get basic framework config
            config = get_framework_config(framework)
            
            # Fetch dynamic information from web using Tavily
            framework_info = await self._fetch_framework_info_dynamic(framework, config)
            syntax_info = await self._fetch_syntax_info_dynamic(framework, config)
            core_modules = await self._fetch_core_modules_dynamic(framework, config)
            practice_examples = await self._fetch_practice_examples_dynamic(framework, config)
            
            # Create comprehensive context
            context = FrameworkContext(
                framework=framework,
                version=framework_info.get('version', config.latest_version),
                description=framework_info.get('description', f"{config.display_name} - AI Framework"),
                key_concepts=framework_info.get('key_concepts', config.key_features),
                syntax=syntax_info,
                core_modules=core_modules,
                practice_examples=practice_examples,
                learning_path=framework_info.get('learning_path', self._create_default_learning_path(framework)),
                common_use_cases=framework_info.get('use_cases', config.primary_use_cases),
                integration_patterns=framework_info.get('integration_patterns', []),
                troubleshooting_guide=framework_info.get('troubleshooting_guide', {}),
                resources=self._get_framework_resources(framework, config),
                created_at=datetime.now(),
                source_urls=framework_info.get('source_urls', [])
            )
            
            # Cache the context
            self.context_cache[framework] = context
            
            # Persist to vector database
            await self.persist_framework_context(framework, context)
            
            logger.info(f"Successfully generated dynamic context for {framework.value}")
            return context
            
        except Exception as e:
            logger.error(f"Error generating context for {framework.value}: {e}")
            return self._get_fallback_context(framework)
    
    async def persist_framework_context(self, framework: SupportedFrameworks, context: FrameworkContext) -> bool:
        """
        Save framework context to vector database for persistent storage.
        
        Args:
            framework: The framework to save context for
            context: The framework context to save
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Import here to avoid circular imports if not already imported
            if not hasattr(self, 'vector_storage'):
                from ..core.vector_storage import vector_storage
                self.vector_storage = vector_storage
                
            # Store in vector database
            success = await self.vector_storage.store_framework_context(context)
            
            if success:
                logger.info(f"Successfully persisted framework context for {framework.value} to vector database")
            else:
                logger.warning(f"Failed to persist framework context for {framework.value} to vector database")
                
            return success
            
        except Exception as e:
            logger.error(f"Error persisting framework context: {e}")
            return False
    
    async def load_framework_context_from_vectordb(self, framework: SupportedFrameworks) -> Optional[FrameworkContext]:
        """
        Load framework context from vector database.
        
        Args:
            framework: The framework to load context for
            
        Returns:
            Optional[FrameworkContext]: The framework context if found, None otherwise
        """
        try:
            # Import here to avoid circular imports if not already imported
            if not hasattr(self, 'vector_storage'):
                from ..core.vector_storage import vector_storage
                self.vector_storage = vector_storage
                
            # Retrieve from vector database
            results = await self.vector_storage.retrieve_framework_context(framework)
            
            if not results:
                logger.info(f"No framework context found in vector database for {framework.value}")
                return None
                
            # Extract information from results
            basic_info = next((r for r in results if r["metadata"]["type"] == "basic_info"), None)
            key_concepts = next((r for r in results if r["metadata"]["type"] == "key_concepts"), None)
            syntax = next((r for r in results if r["metadata"]["type"] == "syntax"), None)
            modules = next((r for r in results if r["metadata"]["type"] == "modules"), None)
            examples = next((r for r in results if r["metadata"]["type"] == "examples"), None)
            
            if not all([basic_info, key_concepts, syntax, modules, examples]):
                logger.warning(f"Incomplete framework context in vector database for {framework.value}")
                return None
                
            # Parse information
            config = get_framework_config(framework)
            
            # Extract version and description from basic info
            version = config.latest_version
            # Use getattr to safely access the description attribute
            description = getattr(config, 'description', '')
            if not description and hasattr(config, 'primary_use_cases') and config.primary_use_cases:
                # Use the first primary use case as a fallback description
                description = config.primary_use_cases[0]
                
            if basic_info:
                for line in basic_info["content"].split("\n"):
                    if line.startswith("Version:"):
                        version = line.replace("Version:", "").strip()
                    elif line.startswith("Description:"):
                        description = line.replace("Description:", "").strip()
            
            # Extract key concepts
            key_concepts_list = []
            if key_concepts and "Key concepts:" in key_concepts["content"]:
                concepts_text = key_concepts["content"].replace("Key concepts:", "").strip()
                key_concepts_list = [c.strip() for c in concepts_text.split(",")]
            
            # Create framework context
            context = FrameworkContext(
                framework=framework,
                version=version,
                description=description,
                key_concepts=key_concepts_list or config.key_features,
                syntax=self._parse_syntax_from_vector_result(syntax) if syntax else self._get_default_syntax(framework),
                core_modules=self._parse_modules_from_vector_result(modules) if modules else self._get_default_modules(framework),
                practice_examples=self._parse_examples_from_vector_result(examples) if examples else self._get_default_examples(framework),
                learning_path=self._create_default_learning_path(framework),
                common_use_cases=config.primary_use_cases,
                integration_patterns=[],
                troubleshooting_guide={},
                resources=self._get_framework_resources(framework, config),
                created_at=datetime.now(),
                source_urls=[]
            )
            
            logger.info(f"Successfully loaded framework context for {framework.value} from vector database")
            return context
            
        except Exception as e:
            logger.error(f"Error loading framework context from vector database: {e}")
            return None
    
    def _parse_syntax_from_vector_result(self, result: Dict[str, Any]) -> FrameworkSyntax:
        """Parse syntax information from vector database result."""
        content = result["content"]
        
        basic_patterns = []
        advanced_patterns = []
        common_imports = []
        boilerplate_code = ""
        
        for line in content.split("\n"):
            if line.startswith("Basic patterns:"):
                patterns_text = line.replace("Basic patterns:", "").strip()
                basic_patterns = [p.strip() for p in patterns_text.split(",")]
            elif line.startswith("Advanced patterns:"):
                patterns_text = line.replace("Advanced patterns:", "").strip()
                advanced_patterns = [p.strip() for p in patterns_text.split(",")]
            elif line.startswith("Common imports:"):
                imports_text = line.replace("Common imports:", "").strip()
                common_imports = [i.strip() for i in imports_text.split(",")]
            elif line.startswith("Boilerplate code:"):
                boilerplate_code = line.replace("Boilerplate code:", "").strip()
        
        return FrameworkSyntax(
            basic_patterns=basic_patterns,
            advanced_patterns=advanced_patterns,
            common_imports=common_imports,
            boilerplate_code=boilerplate_code,
            naming_conventions={},
            source_urls=[]
        )
    
    def _parse_modules_from_vector_result(self, result: Dict[str, Any]) -> List[CoreModule]:
        """Parse module information from vector database result."""
        content = result["content"]
        modules = []
        
        module_texts = content.split("\n\n")
        for module_text in module_texts:
            if not module_text.strip():
                continue
                
            name = ""
            description = ""
            primary_functions = []
            
            for line in module_text.split("\n"):
                if line.startswith("Module:"):
                    name = line.replace("Module:", "").strip()
                elif line.startswith("Description:"):
                    description = line.replace("Description:", "").strip()
                elif line.startswith("Primary functions:"):
                    funcs_text = line.replace("Primary functions:", "").strip()
                    primary_functions = [f.strip() for f in funcs_text.split(",")]
            
            if name:
                modules.append(CoreModule(
                    name=name,
                    description=description,
                    primary_functions=primary_functions,
                    usage_examples=[],
                    best_practices=[],
                    common_pitfalls=[],
                    source_urls=[]
                ))
        
        return modules
    
    def _parse_examples_from_vector_result(self, result: Dict[str, Any]) -> List[PracticeExample]:
        """Parse practice examples from vector database result."""
        content = result["content"]
        examples = []
        
        example_texts = content.split("\n\n")
        for example_text in example_texts:
            if not example_text.strip():
                continue
                
            title = ""
            description = ""
            code_example = ""
            explanation = ""
            
            in_code = False
            code_lines = []
            
            for line in example_text.split("\n"):
                if line.startswith("Example:"):
                    title = line.replace("Example:", "").strip()
                elif line.startswith("Description:"):
                    description = line.replace("Description:", "").strip()
                elif line.startswith("Code:"):
                    in_code = True
                elif line.startswith("Explanation:"):
                    in_code = False
                    explanation = line.replace("Explanation:", "").strip()
                elif in_code:
                    code_lines.append(line)
            
            code_example = "\n".join(code_lines).strip()
            
            if title:
                examples.append(PracticeExample(
                    title=title,
                    description=description,
                    difficulty_level="intermediate",
                    code_example=code_example,
                    explanation=explanation,
                    concepts_demonstrated=[],
                    next_steps=[],
                    source_urls=[]
                ))
        
        return examples
    
    async def _fetch_framework_info_dynamic(self, framework: SupportedFrameworks, config) -> Dict[str, Any]:
        """Fetch general framework information using Tavily search."""
        try:
            framework_name = framework.value
            
            # Search for comprehensive framework information
            info_query = f"{framework_name} framework overview features capabilities 2024 latest"
            search_result = self.search_tool._run(info_query)
            
            # Extract specific version information
            version_query = f"{framework_name} latest version release notes changelog"
            version_result = self.search_tool._run(version_query)
            
            # Parse the search results
            framework_info = self._parse_framework_search_results(
                framework, search_result, version_result, config
            )
            
            return framework_info
            
        except Exception as e:
            logger.error(f"Error fetching dynamic framework info for {framework.value}: {e}")
            return self._get_default_framework_info(framework, config)
    
    async def _fetch_syntax_info_dynamic(self, framework: SupportedFrameworks, config) -> FrameworkSyntax:
        """Fetch syntax information using Tavily search."""
        try:
            framework_name = framework.value
            
            # Search for syntax and code examples
            syntax_query = f"{framework_name} syntax examples import statements code patterns tutorial"
            search_result = self.search_tool._run(syntax_query)
            
            # Get official documentation for syntax
            docs_url = config.documentation_url
            if docs_url:
                try:
                    docs_content = self.extract_tool._run(docs_url)
                    syntax_info = self._parse_syntax_from_results(framework, search_result, docs_content)
                except:
                    syntax_info = self._parse_syntax_from_results(framework, search_result, "")
            else:
                syntax_info = self._parse_syntax_from_results(framework, search_result, "")
            
            return syntax_info
            
        except Exception as e:
            logger.error(f"Error fetching dynamic syntax info for {framework.value}: {e}")
            return self._get_default_syntax(framework)
    
    async def _fetch_core_modules_dynamic(self, framework: SupportedFrameworks, config) -> List[CoreModule]:
        """Fetch core modules information using Tavily search."""
        try:
            framework_name = framework.value
            
            # Search for core modules and API reference
            modules_query = f"{framework_name} core modules API reference classes functions components"
            search_result = self.search_tool._run(modules_query)
            
            # Parse module information from search results
            core_modules = self._parse_modules_from_results(framework, search_result)
            
            return core_modules
            
        except Exception as e:
            logger.error(f"Error fetching dynamic core modules for {framework.value}: {e}")
            return self._get_default_modules(framework)
    
    async def _fetch_practice_examples_dynamic(self, framework: SupportedFrameworks, config) -> List[PracticeExample]:
        """Fetch practice examples using Tavily search."""
        try:
            framework_name = framework.value
            
            # Search for tutorials and practical examples
            examples_query = f"{framework_name} tutorial examples beginner intermediate advanced hands-on"
            search_result = self.search_tool._run(examples_query)
            
            # Parse practice examples from search results
            examples = self._parse_examples_from_results(framework, search_result)
            
            return examples
            
        except Exception as e:
            logger.error(f"Error fetching dynamic practice examples for {framework.value}: {e}")
            return self._get_default_examples(framework)
    
    def _parse_framework_search_results(self, framework: SupportedFrameworks, 
                                       search_result: str, version_result: str, config) -> Dict[str, Any]:
        """Parse framework information from search results."""
        framework_info = {}
        
        # Extract description
        lines = search_result.split('\n')
        for line in lines:
            if framework.value.lower() in line.lower() and any(word in line.lower() for word in ['framework', 'library', 'tool']):
                if len(line) > 20 and len(line) < 300:
                    framework_info['description'] = line.strip()
                    break
        
        # Extract key concepts/features
        key_concepts = []
        for line in lines:
            if any(keyword in line.lower() for keyword in ['feature', 'capability', 'support', 'key', 'main']):
                if len(line) > 15 and len(line) < 200:
                    key_concepts.append(line.strip())
        framework_info['key_concepts'] = key_concepts[:8]
        
        # Extract version from version search
        import re
        version_match = re.search(r'version\s*([0-9]+\.[0-9]+\.[0-9]+)', version_result.lower())
        if version_match:
            framework_info['version'] = version_match.group(1)
        
        # Extract use cases
        use_cases = []
        for line in lines:
            if any(keyword in line.lower() for keyword in ['use case', 'used for', 'application', 'build']):
                if len(line) > 15 and len(line) < 200:
                    use_cases.append(line.strip())
        framework_info['use_cases'] = use_cases[:6]
        
        # Extract source URLs
        url_pattern = r'https?://[^\s<>"{}|\\^`\[\]]+[^\s.,<>"{}|\\^`\[\]]'
        urls = re.findall(url_pattern, search_result + version_result)
        framework_info['source_urls'] = list(set(urls))[:5]
        
        return framework_info
    
    def _parse_syntax_from_results(self, framework: SupportedFrameworks, 
                                 search_result: str, docs_content: str) -> FrameworkSyntax:
        """Parse syntax information from search results."""
        import re
        
        all_content = search_result + "\n" + docs_content
        
        # Extract basic patterns (import statements, basic usage)
        basic_patterns = []
        import_patterns = re.findall(r'(?:from|import)\s+[a-zA-Z0-9_.]+[^\n]*', all_content)
        basic_patterns.extend(import_patterns[:5])
        
        # Extract common imports
        common_imports = []
        for pattern in import_patterns:
            if framework.value.lower() in pattern.lower():
                common_imports.append(pattern.strip())
        
        # Extract advanced patterns (complex usage, decorators, etc.)
        advanced_patterns = []
        lines = all_content.split('\n')
        for line in lines:
            if any(keyword in line.lower() for keyword in ['@', 'class ', 'async def', 'context']):
                if len(line.strip()) > 10 and len(line.strip()) < 200:
                    advanced_patterns.append(line.strip())
        
        # Extract boilerplate code
        boilerplate = f"# {framework.value} basic setup\n# Check official documentation for current syntax"
        code_blocks = re.findall(r'```(?:python)?\n(.*?)\n```', all_content, re.DOTALL)
        if code_blocks:
            boilerplate = code_blocks[0][:500]  # First code block, truncated
        
        # Extract source URLs
        url_pattern = r'https?://[^\s<>"{}|\\^`\[\]]+[^\s.,<>"{}|\\^`\[\]]'
        source_urls = list(set(re.findall(url_pattern, all_content)))[:5]
        
        return FrameworkSyntax(
            basic_patterns=basic_patterns[:5],
            advanced_patterns=advanced_patterns[:5],
            common_imports=common_imports[:5],
            boilerplate_code=boilerplate,
            naming_conventions={},
            source_urls=source_urls
        )
    
    def _parse_modules_from_results(self, framework: SupportedFrameworks, search_result: str) -> List[CoreModule]:
        """Parse core modules from search results."""
        import re
        
        modules = []
        lines = search_result.split('\n')
        
        # Look for module/class definitions
        current_module = None
        for line in lines:
            line = line.strip()
            
            # Look for module or class names
            if any(keyword in line.lower() for keyword in ['module', 'class', 'component']):
                if len(line) > 10 and len(line) < 150:
                    if current_module:
                        modules.append(current_module)
                    
                    module_name = self._extract_module_name(line)
                    current_module = CoreModule(
                        name=module_name,
                        description=line,
                        primary_functions=[],
                        usage_examples=[],
                        best_practices=[],
                        common_pitfalls=[],
                        source_urls=[]
                    )
            
            # Look for functions within modules
            elif current_module and any(keyword in line.lower() for keyword in ['function', 'method', 'def ']):
                if len(line) > 5 and len(line) < 100:
                    current_module.primary_functions.append(line)
        
        # Add the last module
        if current_module:
            modules.append(current_module)
        
        # If no modules found, create default ones
        if not modules:
            modules = self._get_default_modules(framework)
        
        return modules[:5]  # Limit to 5 modules
    
    def _parse_examples_from_results(self, framework: SupportedFrameworks, search_result: str) -> List[PracticeExample]:
        """Parse practice examples from search results."""
        import re
        
        examples = []
        lines = search_result.split('\n')
        
        # Look for code examples
        in_code_block = False
        current_code = []
        current_title = ""
        
        for line in lines:
            if '```' in line:
                if in_code_block and current_code:
                    # End of code block
                    code = '\n'.join(current_code)
                    if len(code) > 20:
                        example = PracticeExample(
                            title=current_title or f"{framework.value} Example",
                            description=f"Example demonstrating {framework.value} usage",
                            difficulty_level="beginner",
                            code_example=code,
                            explanation="Check official documentation for detailed explanation",
                            concepts_demonstrated=[framework.value],
                            next_steps=["Explore official documentation", "Try variations"],
                            source_urls=[]
                        )
                        examples.append(example)
                    current_code = []
                    current_title = ""
                in_code_block = not in_code_block
            elif in_code_block:
                current_code.append(line)
            elif not in_code_block and any(keyword in line.lower() for keyword in ['example', 'tutorial', 'demo']):
                current_title = line.strip()
        
        # If no examples found, create default ones
        if not examples:
            examples = self._get_default_examples(framework)
        
        return examples[:3]  # Limit to 3 examples
    
    def _extract_module_name(self, text: str) -> str:
        """Extract module name from text."""
        import re
        
        # Look for class names (CapitalCase)
        class_match = re.search(r'\b([A-Z][a-zA-Z0-9]*)\b', text)
        if class_match:
            return class_match.group(1)
        
        # Look for module names
        module_match = re.search(r'\b([a-z_][a-z0-9_]*)\b', text)
        if module_match:
            return module_match.group(1)
        
        return "CoreModule"
    
    def _get_default_framework_info(self, framework: SupportedFrameworks, config) -> Dict[str, Any]:
        """Get default framework information when search fails."""
        return {
            'description': f"{config.display_name} - AI Framework",
            'key_concepts': config.key_features or [f"Core {framework.value} functionality"],
            'version': config.latest_version,
            'use_cases': config.primary_use_cases or [f"{framework.value} applications"],
            'source_urls': [config.official_website, config.documentation_url, config.github_repository]
        }
    
    def _create_default_learning_path(self, framework: SupportedFrameworks) -> List[str]:
        """Create default learning path when dynamic fetch fails."""
        return [
            "Start with official documentation",
            "Follow getting started guide",
            "Complete basic tutorials",
            "Explore API reference",
            "Build sample projects",
            "Study best practices",
            "Join community discussions"
        ]
    
    def _get_framework_resources(self, framework: SupportedFrameworks, config) -> Dict[str, str]:
        """Get framework resources (only official URLs)."""
        return {
            "official_docs": config.documentation_url,
            "github_repo": config.github_repository,
            "official_website": config.official_website
        }
    
    def _get_default_syntax(self, framework: SupportedFrameworks) -> FrameworkSyntax:
        """Get default syntax when search fails."""
        return FrameworkSyntax(
            basic_patterns=[f"# {framework.value} basic usage", "# Check official documentation"],
            advanced_patterns=[f"# {framework.value} advanced patterns", "# Refer to API reference"],
            common_imports=[f"# import {framework.value}"],
            boilerplate_code=f"# {framework.value} boilerplate\n# See official documentation for current syntax",
            naming_conventions={},
            source_urls=[]
        )
    
    def _get_default_modules(self, framework: SupportedFrameworks) -> List[CoreModule]:
        """Get default modules when search fails."""
        return [
            CoreModule(
                name="Core",
                description=f"Core {framework.value} functionality",
                primary_functions=["main_function", "helper_function"],
                usage_examples=[f"# {framework.value} core usage"],
                best_practices=["Follow official guidelines"],
                common_pitfalls=["Check documentation for common issues"],
                source_urls=[]
            )
        ]
    
    def _get_default_examples(self, framework: SupportedFrameworks) -> List[PracticeExample]:
        """Get default examples when search fails."""
        return [
            PracticeExample(
                title=f"Basic {framework.value} Example",
                description=f"Getting started with {framework.value}",
                difficulty_level="beginner",
                code_example=f"# {framework.value} example\n# Check official documentation for current syntax",
                explanation=f"Basic {framework.value} usage example",
                concepts_demonstrated=[framework.value],
                next_steps=["Explore official documentation"],
                source_urls=[]
            )
        ]
    
    def _get_fallback_context(self, framework: SupportedFrameworks) -> FrameworkContext:
        """Get fallback context when all else fails."""
        config = get_framework_config(framework)
        
        return FrameworkContext(
            framework=framework,
            version=config.latest_version,
            description=f"{config.display_name} - AI Framework",
            key_concepts=config.key_features or [f"Core {framework.value} functionality"],
            syntax=self._get_default_syntax(framework),
            core_modules=self._get_default_modules(framework),
            practice_examples=self._get_default_examples(framework),
            learning_path=self._create_default_learning_path(framework),
            common_use_cases=config.primary_use_cases or [f"{framework.value} applications"],
            integration_patterns=[],
            troubleshooting_guide={},
            resources=self._get_framework_resources(framework, config),
            created_at=datetime.now(),
            source_urls=[config.official_website, config.documentation_url, config.github_repository]
        )
    
    def get_context_summary(self, framework: SupportedFrameworks) -> Optional[str]:
        """Get a summary of the framework context."""
        if framework not in self.context_cache:
            return None
        
        context = self.context_cache[framework]
        summary = f"""
{context.framework.value.upper()} Framework Context Summary:
- Version: {context.version}
- Description: {context.description}
- Key Concepts: {len(context.key_concepts)} identified
- Core Modules: {len(context.core_modules)} documented
- Practice Examples: {len(context.practice_examples)} available
- Learning Path: {len(context.learning_path)} steps
- Generated: {context.created_at.strftime('%Y-%m-%d %H:%M:%S')}
- Sources: {len(context.source_urls)} URLs
"""
        return summary
    
    async def refresh_framework_context(self, framework: SupportedFrameworks) -> FrameworkContext:
        """Force refresh of framework context with new search."""
        # Clear cache
        if framework in self.context_cache:
            del self.context_cache[framework]
        
        # Generate new context
        return await self.initialize_framework_context(framework)


# Global initializer instance
dynamic_initializer = DynamicFrameworkInitializer()


# Legacy compatibility
FrameworkInitializer = DynamicFrameworkInitializer


async def get_framework_context(framework: SupportedFrameworks) -> FrameworkContext:
    """Get framework context using dynamic search."""
    return await dynamic_initializer.initialize_framework_context(framework)


async def refresh_framework_context(framework: SupportedFrameworks) -> FrameworkContext:
    """Refresh framework context with new search."""
    return await dynamic_initializer.refresh_framework_context(framework)