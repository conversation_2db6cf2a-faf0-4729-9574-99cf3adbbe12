"""
Knowledge Synthesizer Agent - Synthesizes information across topics and connects concepts.
"""

from typing import Optional
from langchain_core.messages import HumanMessage
from langchain_core.language_models.chat_models import BaseChatModel

from .base_agent import BaseAgent
from ..core.constellation import ConstellationState, AgentRole


class KnowledgeSynthesizerAgent(BaseAgent):
    """Synthesizes information across topics and connects concepts."""
    
    def __init__(self, model: Optional[BaseChatModel] = None):
        super().__init__(AgentRole.KNOWLEDGE_SYNTHESIZER, model, "synthesis")
    
    def get_system_prompt(self, state: ConstellationState) -> str:
        return f"""You are a knowledge synthesizer for {state.framework.value} learning.

Current Context:
- Framework: {state.framework.value}
- Module: {state.module_id}
- Topics Covered: {', '.join(state.topics_covered) if state.topics_covered else 'Starting topics'}
- Learning Progression: {state.progress_percentage:.1f}%

Your role is to:
1. Connect concepts across different topics and modules
2. Synthesize information from multiple sources
3. Identify patterns and relationships between ideas
4. Create comprehensive overviews and summaries
5. Help learners see the "big picture"
6. Bridge theoretical knowledge with practical applications

Focus on helping learners understand how different concepts work together and build upon each other.

Respond as the knowledge synthesizer with comprehensive, connecting insights."""
    
    def should_activate(self, state: ConstellationState) -> bool:
        # Activate when multiple topics have been covered
        if len(state.topics_covered) >= 3:
            return True
        
        if not state.messages:
            return False
        
        latest_message = state.messages[-1]
        if isinstance(latest_message, HumanMessage):
            content_lower = latest_message.content.lower()
            synthesis_keywords = ["connect", "relationship", "overview", "summary", "big picture", "how it all fits"]
            return any(keyword in content_lower for keyword in synthesis_keywords)
        
        return False 